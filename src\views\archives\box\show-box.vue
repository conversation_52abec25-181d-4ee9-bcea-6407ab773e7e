<template>
    <div class="showBox" v-if="page.is_display">
        <van-tabs :border="false">
            <van-tab title="档案盒信息">
                <van-cell-group>
                    <van-cell title="档案盒名称" :value="info.box_name"/>
                    <van-cell title="档案盒编号" :value="info.box_num"/>
                    <van-cell title="文件时间段" :value="info.date_span"/>
                    <van-cell title="文件数量" :value="info.file_nums"/>
                    <van-cell title="即将过期数量" :value="info.toexpire_nums"/>
                    <van-cell title="覆盖设备数量" :value="info.assets_nums"/>
                    <van-cell title="覆盖科室数量" :value="info.depart_nums"/>
                    <van-cell title="添加人" :value="info.add_user"/>
                    <van-cell title="添加时间" :value="info.add_time"/>
                    <van-cell title="备注" :value="info.remark"/>
                </van-cell-group>
            </van-tab>
        </van-tabs>

        <div class="card-header">
            <h2 class="formTitle">档案盒设备&文件信息</h2>
        </div>
        <van-cell-group v-for="(item,key) in file" :key="key">
          <van-cell :icon="icon[key]" :title="item.assets"  :value="item.assnum+'|'+item.department" class="background" @click="OnClick(key)"/>
          <div v-show="show[key]">
          <div class="content_file" v-if="item.files !== undefined && item.files.length > 0">
            <van-field center clearable :value="i.file_name" readonly v-for="i in item.files" :key="i.id">
                <template #left-icon>
                    <van-image v-if="i.file_type=='docx'" width="20" height="20" :src="url+'/public/mobile/images/icon/word.png'"/>
                    <van-image v-else width="20" height="20" :src="url+'/public/mobile/images/icon/'+i.file_type+'.png'"/>
                </template>
                <template #button>
                    <van-button size="small" type="info" v-if="i.file_type === 'pdf'"
                                @click="scan_file(i.file_url)">预览
                    </van-button>
                    <van-button size="small" type="info" v-if="i.file_type === 'jpg'||i.file_type === 'png'"
                                @click="img(i.file_url)">预览
                    </van-button>
                    <van-button size="small" type="primary" @click="down(i.file_url,i.file_name,i.file_size)">
                        下载
                    </van-button>
                </template>
            </van-field>
        </div>
        <div class="no_file" v-else>暂无相关文件</div>
         </div>
        </van-cell-group>

    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>
<script>
    import {Divider, Cell, CellGroup, Icon, Tab,Field, Tabs,Loading,Image as VanImage,Button,ImagePreview} from 'vant';
    import {getBoxList} from "@/api/archives/list"; 
    export default {
        name: 'showAssets',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Field.name]: Field,
            [Loading.name]: Loading,
            [VanImage.name]: VanImage,
            [Button.name]: Button,
        },
        data() {
            return {
                page: {
                    is_display: 0,
                },
                icon:[],
                show:[],
                info:[],
                file:{
                    files:[],
                },
                url:process.env.VUE_APP_BASE_PROXY_URL,
            }
        },
        methods: {
            getInfo(box_id,box_num) {
                let params = {box_id: box_id,box_num:box_num, action: 'show_box'};
                getBoxList(params).then(response => {
                    this.info = response.boxInfo;
                    this.file = response.filesInfo;
                    for(let i in this.file)
                    {  
                        this.icon[i] = "arrow";
                        this.show[i] = false;
                    }
                    this.page.is_display = 1;
                    
                })
            },
            OnClick(key){
                let data =[];
                if (this.icon[key]=='arrow') {
                   this.icon[key]='arrow-down';
                   this.show[key]= true;
                }else{
                   this.icon[key]='arrow';
                   this.show[key]= false;
                }
                this.icon.reverse();
                this.icon.reverse();
                this.show.reverse();
                this.show.reverse();
                console.log(this.icon);
            },
            img(url){
               ImagePreview([process.env.VUE_APP_BASE_PROXY_URL+url]);
            },
            down(path, name, size) {
                path = process.env.VUE_APP_BASE_PROXY_URL+path;
                this.$router.push({
                    name: 'dw',
                    query: {
                        path: path,
                        name: name,
                        size: size,
                    }
                });
            },
            scan_file(path) {
                path = process.env.VUE_APP_BASE_PROXY_URL+path;
                this.$router.push({
                    name: 'showFile',
                    query: {
                        path: path
                    }
                });
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.box_id,this.$route.query.box_num);
        }
    }
</script>
<style>
    .no_file{
        text-align: center;
        color: #A9A9A9;
    }
</style>