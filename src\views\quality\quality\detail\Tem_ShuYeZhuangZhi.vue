<template>
    <div class="con_detail" v-if="page.is_display===1">
        <Quality :info="info"/>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">质控检测明细（模板：{{page.templatename}}）</h2>
        </div>

        <van-form ref="qualityForm" @submit="onSubmit">
            <van-field name="lookslike" label="外观功能：">
                <template #input>
                    <van-radio-group @change="looklike_change" v-model="form.lookslike" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field v-show="show_lookslike_desc" name="lookslike_desc" type="textarea" rows="2" autosize
                       v-model="form.lookslike_desc" placeholder="外观不符合的请说明情况" style="margin-bottom: 10px;"/>

            <van-collapse v-model="page.activeNames">
                <van-collapse-item :title="page.flow" name="1">
                    <van-row v-for="(item,key) in page.setting.flow.slice(0,2)" :key="key">
                    <van-field :label="'设定值：'+item" v-model="page.flowvalue[key]" type="number" placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            <van-collapse-item :title="page.block" name="2">
                     <van-row v-for="(item,key) in page.setting.block.slice(0,2)" :key="key">
                    <van-field :label="'设定值：'+item" v-model="page.blockvalue[key]" type="number" placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            </van-collapse>

            <van-cell title="堵塞" size="large">
                <template #label>
                    <van-field name="blocking" >
                        <template #input>
                            <van-radio-group v-model="form.blocking" direction="horizontal">
                                <van-radio name="1">符合</van-radio>
                                <van-radio name="2">不符合</van-radio>
                                <van-radio name="3">不适用</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                </template>
            </van-cell>

            <van-cell title="即将空瓶" size="large">
                <template #label>
                    <van-field name="forthcoming_empty_bottle">
                        <template #input>
                            <van-radio-group v-model="form.forthcoming_empty_bottle" direction="horizontal">
                                <van-radio name="1">符合</van-radio>
                                <van-radio name="2">不符合</van-radio>
                                <van-radio name="3">不适用</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                </template>
            </van-cell>

            <van-cell title="电池电量不足" size="large">
                <template #label>
                    <van-field name="battery_low">
                        <template #input>
                            <van-radio-group v-model="form.battery_low" direction="horizontal">
                                <van-radio name="1">符合</van-radio>
                                <van-radio name="2">不符合</van-radio>
                                <van-radio name="3">不适用</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                </template>
            </van-cell>

            <van-cell title="流速错误" size="large">
                <template #label>
                    <van-field name="flow_error">
                        <template #input>
                            <van-radio-group v-model="form.flow_error" direction="horizontal">
                                <van-radio name="1">符合</van-radio>
                                <van-radio name="2">不符合</van-radio>
                                <van-radio name="3">不适用</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                </template>
            </van-cell>

            <van-cell title="输液管路安装不妥" size="large">
                <template #label>
                    <van-field name="improper_installation">
                        <template #input>
                            <van-radio-group v-model="form.improper_installation" direction="horizontal">
                                <van-radio name="1">符合</van-radio>
                                <van-radio name="2">不符合</van-radio>
                                <van-radio name="3">不适用</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                </template>
            </van-cell>

            <van-cell title="气泡报警" size="large">
                <template #label>
                    <van-field name="bubble_alarm">
                        <template #input>
                            <van-radio-group v-model="form.bubble_alarm" direction="horizontal">
                                <van-radio name="1">符合</van-radio>
                                <van-radio name="2">不符合</van-radio>
                                <van-radio name="3">不适用</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                </template>
            </van-cell>

            <van-cell title="电源线脱开" size="large">
                <template #label>
                    <van-field name="power_line_disconnect">
                        <template #input>
                            <van-radio-group v-model="form.power_line_disconnect" direction="horizontal">
                                <van-radio name="1">符合</van-radio>
                                <van-radio name="2">不符合</van-radio>
                                <van-radio name="3">不适用</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                </template>
            </van-cell>

            <van-cell title="开门报警" size="large">
                <template #label>
                    <van-field name="open_door_alarm">
                        <template #input>
                            <van-radio-group v-model="form.open_door_alarm" direction="horizontal">
                                <van-radio name="1">符合</van-radio>
                                <van-radio name="2">不符合</van-radio>
                                <van-radio name="3">不适用</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                </template>
            </van-cell>
            <van-field name="total_desc" type="textarea" rows="3" show-word-limit maxlength="120" label="检测备注：" v-model="form.total_desc" placeholder="请填写检测备注（如偏离情况说明）"/>

            <div class="mp">
                <van-divider>设备铭牌照片</van-divider>
                <van-row type="flex" justify="center">
                    <van-uploader multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" :before-read="beforeRead" v-model="form.nameplate_fileList" :after-read="nameplate_afterRead"
                                  :before-delete="del_nameplate">
                    </van-uploader>
                </van-row>
                <van-divider>检测仪器视图照片</van-divider>
                <van-row type="flex" justify="center">
                    <van-uploader multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" :before-read="beforeRead" v-model="form.instrument_fileList" :after-read="instrument_afterRead"
                                  :before-delete="del_instrument">
                    </van-uploader>
                </van-row>
                <div style="margin: 16px;">
                    <van-button round block color="#FFB800" native-type="button" @click="changeFormType('keepquality')"
                                style="margin-bottom: 16px;">
                        暂时保存
                    </van-button>
                    <van-button round block type="primary" native-type="button" @click="changeFormType('end')">
                        确认并提交
                    </van-button>
                </div>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>
<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Row,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Toast,
        Loading,
        Uploader,
        ImagePreview,
        Image as VanImage,
        Swipe,
        SwipeItem,
        RadioGroup,
        Radio,
        Col,
        Stepper,
        Notify,
        Collapse,
         CollapseItem
    } from 'vant';
    import {getInfo, submit} from "@/api/quality/detail";
    import Quality from "@/components/Quality";

    export default {
        name: 'ChuChanYi',
        components: {
            [Divider.name]: Divider,
            [Collapse.name]: Collapse,
            [CollapseItem.name]: CollapseItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Row.name]: Row,
            [Col.name]: Col,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Uploader.name]: Uploader,
            [Tab.name]: Tab,
            [Stepper.name]: Stepper,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Tabs.name]: Tabs,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [VanImage.name]: VanImage,
            [Loading.name]: Loading,
            Quality,
        },
        data() {
            return {
                info: {
                    asInfo: [],
                    qsInfo: [],
                },
                page: {
                    flow: '心率（次/min）：最大允差：',
                    block: '阻塞报警检测： 最大允差：',
                    file_data: '',
                    flowvalue: [],
                    blockvalue: [],
                    setting: {
                        flow: [],
                        block: [],
                    },
                    templatename: '',
                    nameplate: [],
                    instrument_view: [],
                    detail_result: [],
                    positions: 0,
                    is_display: 0,
                    activeNames:[],
                  max_count:3
                },
                form: {
                    lookslike: "1",
                    charge_result: '1',
                    internal_discharge: '1',
                    type: 'keepquality',
                    total_desc: "",
                    charge: "",
                    blocking: '1',
                    forthcoming_empty_bottle: '1',
                    battery_low: '1',
                    flow_error: '1',
                    improper_installation: '1',
                    bubble_alarm: '1',
                    power_line_disconnect: '1',
                    open_door_alarm: '1',
                    nameplate_fileList: [],
                    instrument_fileList: [],
                },
                show_lookslike_desc: false,
            }
        },
        methods: {
            looklike_change(value) {
                if (value == 2) {
                    this.show_lookslike_desc = true;
                } else {
                    this.show_lookslike_desc = false;
                    this.form.lookslike_desc = '';
                }
            },
            onChange(item, key) {
                let tolerance = Math.abs(item - this.page.energesisvalue[key]);
                this.page.tolerancevalue[key] = tolerance;
            },
            show_nameplate_Image(key) {
                ImagePreview({
                    images: this.page.nameplate,
                    startPosition: key,
                });
            },
            show_instrument_Image(key) {
                ImagePreview({
                    images: this.page.instrument_view,
                    startPosition: key,
                });
            },
            on_nameplate_Change(index) {
                this.page.positions = index;
            },
            on_instrument_Change(index) {
                this.page.positions = index;
            },
            nameplate_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                //上传图片
                let values = [];
                values.type = 'nameplate';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';

                        this.form.nameplate_fileList.pop();
                        this.form.nameplate_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                });
            },
          beforeRead(file){
            if (file.length > this.page.max_count){
              Notify({ type: 'danger', message: `最多只能选择${this.page.max_count}个图片` });
              return  false
            }
            return true
          },
            instrument_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                //上传图片
                let values = [];
                values.type = 'instrument_view';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';

                        this.form.instrument_fileList.pop();
                        this.form.instrument_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                });
            },
            oversize(){
                //超出限制大小
                Notify({ type: 'danger', message: '图片超出10M大小限制' });
                return false;
            },
            del_nameplate(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            del_instrument(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            changeFormType(type) {
                this.form.type = type;
                this.$refs.qualityForm.submit();
            },
            onSubmit(values) {
                if(values.lookslike == 2){
                    //外观不符合的要说明情况
                    if(!values.lookslike_desc.trim()){
                        Notify({ type: 'danger', message: '外观不符合的请说明情况' });
                        return false;
                    }
                }
                //心率
                values.flow = this.page.flowvalue;
                for(let i = 0;i < this.page.setting.flow.slice(0,2).length;i++){
                    if(!values.flow[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整心率测量值' });
                        return false;
                    }else if (!values.flow[i]) {
                        values.flow[i] = "";
                    }
                }

                //阻塞报警
                values.block = this.page.blockvalue;
                for(let i = 0;i < this.page.setting.block.slice(0,2).length;i++){
                    if(!values.block[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整阻塞报警测量值' });
                        return false;
                    }else if (!values.block[i]) {
                        values.block[i] = "";
                    }
                }
                values.qsid = this.$route.query.qsid;
                values.action = this.form.type;
                let _this = this;
                submit(Qs.stringify(values)).then(res => {
                    if (res.status == 1) {
                        Toast({
                            type: 'success',//失败fail
                            duration: 2000,//2秒
                            message: res.msg,
                            icon: 'success',//失败cross
                            forbidClick: true,//是否禁止背景点击，避免重复提交
                            onClose() {
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Quality/qualityDetailList');
                            }
                        });
                    }
                });
            },
            getInfo(qsid) {
                let params = {qsid: qsid};
                getInfo(params).then(response => {
                    this.info.asInfo = response.asInfo;
                    this.info.qsInfo = response.qsInfo;
                    this.page.templatename = response.templatename;
                    for (let i in response.setting) {
                        if (response.setting[i].detection_Ename == 'flow') {
                            this.page.setting.flow = response.setting[i].set;
                        }
                        if (response.setting[i].detection_Ename == 'block') {
                            this.page.setting.block = response.setting[i].set;
                        }
                    }
                    if (response.detail_data != null && response.detail_data.fixed_detection) {
                        this.page.flowvalue = response.detail_data.fixed_detection.flow;
                        this.page.blockvalue = response.detail_data.fixed_detection.block;
                        this.form.lookslike = response.detail_data.fixed_detection.lookslike;
                        if(response.detail_data.fixed_detection.lookslike == 2){
                            this.show_lookslike_desc = true;
                            this.form.lookslike_desc = response.detail_data.fixed_detection.lookslike_desc;
                        }
                        this.form.blocking = response.detail_data.fixed_detection.blocking;
                        this.form.forthcoming_empty_bottle = response.detail_data.fixed_detection.forthcoming_empty_bottle;
                        this.form.battery_low = response.detail_data.fixed_detection.battery_low;
                        this.form.flow_error = response.detail_data.fixed_detection.flow_error;
                        this.form.improper_installation = response.detail_data.fixed_detection.improper_installation;
                        this.form.bubble_alarm = response.detail_data.fixed_detection.bubble_alarm;
                        this.form.power_line_disconnect = response.detail_data.fixed_detection.power_line_disconnect;
                        this.form.open_door_alarm = response.detail_data.fixed_detection.open_door_alarm;
                        this.form.total_desc = response.detail_data.fixed_detection.total_desc;
                    }
                    if (response.file_data) {
                        for (let i in response.file_data.nameplate) {
                            this.form.nameplate_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.nameplate[i].file_url,
                                id: response.file_data.nameplate[i].file_id
                            });
                        }
                        for (let i in response.file_data.instrument_view) {
                            this.form.instrument_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.instrument_view[i].file_url,
                                id: response.file_data.instrument_view[i].file_id
                            });
                        }

                    }
                    this.page.flow = this.page.flow + response.tolerance.flow;
                    this.page.block = this.page.block + response.tolerance.block;
                    this.page.is_display = 1;
                });
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.qsid);
        }
    }
</script>
<style scoped lang="scss">
    .background {
        background: rgba(69, 90, 100, 0.1);
    }

    ::v-deep .text_length .van-cell__title {
        width: 95px
    }

    .font-lable {
        color: #323233;
        font-size: 14px;
        line-height: 24px;
        padding: 0px 16px;
    }

    .card-header {
        margin-top: 20px;
    }

    .mp {
        margin-top: 20px;
        padding-top: 10px;
        background: #fff;
    }
</style>
