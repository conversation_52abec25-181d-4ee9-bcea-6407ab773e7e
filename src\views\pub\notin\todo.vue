<template>
    <div class="AssetsInfo">
        <van-collapse v-model="activeNames">
            <van-collapse-item title="设备基本信息" name="1" icon="pause" title-class="van_title" >
                <van-cell title="设备名称 " :value="info.assets"/>
                <van-cell title="设备编号" :value="info.assnum"/>
                <van-cell title="设备原编号" :value="info.assorignum"/>
                <van-cell title="规格/型号" :value="info.model"/>
                <van-cell title="产品序列号" :value="info.serialnum"/>
                <van-cell title="所属科室" :value="info.department"/>
                <van-cell title="设备分类" :value="info.cate_name"/>
                <van-cell title="设备状态">
                    <template #right-icon><span v-html="info.status_name"/></template>
                </van-cell>
            </van-collapse-item>
            <van-collapse-item icon="pause" title="基本操作" name="2" title-class="van_title">
                <div class="todo_menu" v-if="menus.length>0">
                    <van-button plain hairline type="info" v-for="(item,index) in menus" :key="index" :to="item.to_url">{{item.name}}</van-button>
                </div>
                <div v-else>
                    没有可操作的权限
                </div>
            </van-collapse-item>
        </van-collapse>
    </div>
</template>

<script>
    import {Cell, Collapse, CollapseItem, Icon, Grid, GridItem, Button} from 'vant';
    import {todo} from "@/api/index/index";

    export default {
        name: "todo",
        data(){
            return{
                info: [],
                menus: [],
                activeNames: ['1','2'],
            }
        },
        components:{
            [Cell.name]: Cell,
            [Collapse.name]: Collapse,
            [CollapseItem.name]: CollapseItem,
            [Icon.name]: Icon,
            [Grid.name]: Grid,
            [GridItem.name]: GridItem,
            [Button.name]: Button,
        },
        mounted(){
            todo(this.$route.query.assnum).then(res => {
                if(res){
                    this.info = res.asInfo;
                    this.menus = res.menus;
                }
            })
        },
        methods:{
            onChange(event) {
                this.setData({
                    activeNames: event.detail,
                });
            },
        },
    }
</script>

<style scoped lang="scss">
.todo_menu{
    button{
        margin-right: 15px;
        margin-bottom: 15px;
    }
}
</style>
<style>
    .van-cell__left-icon {
        color: #5FB878;
    }
</style>
