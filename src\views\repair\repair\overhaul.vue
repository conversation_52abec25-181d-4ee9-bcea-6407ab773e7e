<template>
  <div class="con_detail" v-if="page.is_display===1">
    <van-tabs :border="false">
      <van-tab title="设备基本信息">
        <AssetsInfo :info='info'/>
      </van-tab>
      <van-tab title="报修信息">
        <van-cell-group>
          <van-cell title="报修人" :value="info.repairdata.applicant"/>
          <van-cell title="报修时间" :value="info.repairdata.applicant_time"/>
          <van-cell title="维修单号" :value="info.repairdata.repnum"/>
          <van-cell title="故障描述" :value="info.repairdata.breakdown"/>
          <div v-if="jugdePlatform()">
          <van-cell title="语音描述" v-if="info.repairdata.wxTapeAmr==''" value="无"/>
          <van-cell title="语音描述" v-else>
            <template #right-icon>
              <van-icon class-prefix="wx-icon" :name="page.icon_name" style="line-height: inherit;" @click="bofang"/>
              <span @click="bofang">{{ info.repairdata.seconds }}〞 点击播放</span>
            </template>
          </van-cell>
          </div>
          <van-cell title="故障照片" v-if="!info.repairdata.pic_url" value="无"/>
          <van-cell title="故障照片" v-else>
            <template #right-icon>
              <a href="javascript:" @click="img">查看(总共{{ info.repairdata.imgCount }}张)</a>
            </template>
          </van-cell>
        </van-cell-group>
      </van-tab>
      <van-tab title="接单信息">
        <van-cell-group>
          <van-cell title="接单人" :value="info.repairdata.response"/>
          <van-cell title="接单时间" :value="info.repairdata.response_date"/>
          <van-cell title="预计到场时间（分钟）" :value="info.repairdata.expect_arrive"/>
          <van-cell title="现场签到时间" :value="info.repairdata.sign_in_time"/>
          <van-cell title="备注" :value="info.repairdata.reponse_remark"/>
        </van-cell-group>
      </van-tab>
    </van-tabs>
    <div style="margin: 16px;" v-if="page.show_from==1">
      <van-button block type="warning" native-type="button" @click="page.showSelectUsername = true">
        转单
      </van-button>
    </div>
    <div style="margin: 16px;" v-if="page.wx_sign_in==1">
      <van-button block type="warning" native-type="button" @click="sign_in">
        现场签到
      </van-button>
    </div>
    <van-popup v-model="page.showSelectUsername" position="bottom">
      <van-field v-model="page.UsernameKeywords" label="搜索" placeholder="请输入工程师名称"/>
      <van-picker
          title="选择工程师"
          show-toolbar
          :columns="this.UsernamecolumnsFilter"
          @confirm="onConfirm"
          @cancel="page.showSelectUsername = false"
      />
    </van-popup>

    <div class="card-header">
      <img class="formImage" :src="require('@/assets/images/form/form.png')" />
      <h2 class="formTitle">检修表单</h2>
    </div>

    <van-form ref="repairForm" @submit="onSubmit" v-if="page.show_from==1">
      <van-field
          readonly
          clickable
          required
          :value="page.errorType"
          label="故障问题"
          placeholder="点击选择问题"
          @click="page.showSelecttype = true"
      />
      <van-popup v-model="page.showSelecttype" position="bottom">
        <van-field v-model="page.itemsKeyword" label="搜索" placeholder="请输入故障问题"/>
        <van-tree-select :items="this.itemFilter" :active-id.sync="page.activeIds"
                         :main-active-index.sync="page.activeIndex" @click-item="onClickitem"/>
      </van-popup>
      <van-field
          v-model="page.Solution"
          name="Solution"
          rows="1"
          autosize
          label="解决方式"
          required
          readonly
          type="textarea"
          placeholder="解决方式"
          @click="page.showSelectSolution = true"
      />
      <van-popup v-model="page.showSelectSolution" position="bottom">
        <van-picker
            title="解决方式"
            show-toolbar
            :columns="page.Solutioncolumns"
            @confirm="onSolutionConfirm"
        />
      </van-popup>
      <van-field
          v-model="page.nature"
          name="nature"
          v-if="page.shownature"
          rows="1"
          autosize
          label="维修性质"
          required
          readonly
          type="textarea"
          placeholder="维修性质"
          @click="page.showSelectnature = true"
      />
      <van-popup v-model="page.showSelectnature" position="bottom">
        <van-picker
            title="维修性质"
            show-toolbar
            :columns="page.naturecolumns"
            @confirm="on_nature_Confirm"
            :default-index="page.defaultnature"
        />
      </van-popup>

      <!-- 维保厂家信息展示 -->
      <template v-if="page.nature === '维保厂家'">
        <van-field v-model="info.guarantee_name" label="维修厂家" readonly />
        <van-field
          v-model="info.salesman_name"
          label="维修公司联系人"
          readonly
        />
        <van-field
          v-model="info.salesman_phone"
          label="维修公司联系电话"
          readonly
        />
      </template>

      <van-field
          v-model="form.dispose_detail"
          v-if="!page.shownature"
          name="dispose_detail"
          required
          :rules="[{ required: true, message: '请输入处理详情' }]"
          autosize
          label="处理详情"
          type="textarea"
          placeholder="请输入处理详情"
          rows="3"
          maxlength="180"
          show-word-limit
      />
      <audio :src="info.repairdata.wxTapeAmr" id="audio"></audio>
      <van-field
        v-if="!page.shownature"
        label="维修工程师"
        :value="page.response"
        readonly
      />
      <van-field
        name="expect_time"
        label="预计修复日期"
        :value="form.expect_time"
        readonly
        placeholder="点击选择日期"
        @click="page.showSelectdate = true"
      />
      <van-popup v-model="page.showSelectdate" position="bottom">
        <van-datetime-picker
          v-model="page.currentDate"
          type="date"
          title="选择时间"
          @confirm="onConfirmoverhaulDate"
          @cancel="page.showSelectdate = false"
        />
      </van-popup>
      <van-field
        v-model="form.repair_remark"
        name="repair_remark"
        autosize
        label="维修备注"
        type="textarea"
        placeholder="请输入维修备注"
        rows="3"
        maxlength="180"
        show-word-limit
      />

      <!--添加配件-->
      <Table :table="page.table_parts" v-show="page.showparts" />
      <van-row type="flex" justify="center" class="pj">
        <van-button
          v-show="page.showparts"
          type="default"
          @click="page.showSelectparts = true"
          native-type="button"
          >添加配件
        </van-button>
      </van-row>

      <van-popup v-model="page.showSelectparts" position="top" :style="{ height: '100%' }" closeable>
        <h2 style="padding-left: 10px;">添加配件</h2>
        <van-field
            readonly
            clickable
            :value="page.partsSelectTips"
            label="配件名称"
            placeholder="点击选择添加配件"
            @click="page.showPartsPage = true"
        />
        <van-popup v-model="page.showPartsPage" position="bottom">
          <van-field
            v-model="page.partsKeyword"
            label="搜索"
            placeholder="请输入配件名称"
          />
          <van-tree-select
            :items="this.partsFilter"
            :active-id.sync="page.partsActiveIds"
            main-active-index.sync="0"
            @click-item="onClickPartsItem"
          />
        </van-popup>
        <van-cell-group>
          <van-cell :title="item.text" v-for="item in page.partsActiveStepper" :key="item.id">
            <template #default>
              <van-stepper v-model="item.count" theme="round" button-size="22" disable-input/>
            </template>
          </van-cell>
        </van-cell-group>
        <div style="margin-top: 20px;text-align: center;">
          <van-button native-type="button" type="info" @click="page.showSelectparts = false">确定并关闭弹窗</van-button>
        </div>
      </van-popup>
      <!--添加配件-->
      <!--添加维修商-->
      <van-row type="flex" justify="center">
        <van-button
          v-show="page.showcompany"
          type="default"
          @click="page.showSelectcompany = true"
          native-type="button"
          >添加维修商
        </van-button>
      </van-row>
      <table class="cus_table" v-for="item in page.company" :key="item.offid">
        <tr>
          <td class="cus_td">维修商</td>
          <td colspan="3" class="cus_td_text">{{ item.company }}</td>
        </tr>
        <tr>
          <td class="cus_td">联系人</td>
          <td>{{ item.salesman_name }}</td>
          <td class="cus_td">联系方式</td>
          <td>{{ item.salesman_phone }}</td>
        </tr>
        <tr>
          <td class="cus_td">送修时间</td>
          <td>{{ item.send_date }}</td>
          <td class="cus_td">返还时间</td>
          <td>{{ item.return_date }}</td>
        </tr>
        <tr>
          <td class="cus_td">发票&金额</td>
          <td colspan="3">{{ item.invoice }}&{{ item.total_price }}</td>
        </tr>
        <tr>
          <td class="cus_td">维修周期</td>
          <td>{{ item.cycletext }}</td>
          <td class="cus_td">最终选择</td>
          <td>{{ item.proposaltext }}</td>
        </tr>
      </table>
      <van-popup
        @close="company_close"
        v-model="page.showSelectcompany"
        position="top"
        :style="{ height: '100%' }"
        closeable
      >
        <h2 style="padding-left: 10px">添加维修商</h2>
        <van-field
          readonly
          clickable
          :value="page.companySelectTips"
          label="维修商"
          placeholder="点击选择添加维修商"
          @click="page.showCompanyPage = true"
        />
        <van-popup v-model="page.showCompanyPage" position="bottom">
          <van-field
            v-model="page.companyKeyword"
            label="搜索"
            placeholder="请输入维修商"
          />
          <van-tree-select
            :items="this.companyFilter"
            :active-id.sync="page.companyActiveIds"
            main-active-index.sync="0"
            @click-item="onClickCompanyItem"
          />
        </van-popup>

        <van-popup v-model="popupSendDate" position="bottom">
          <van-datetime-picker
            v-model="page.currentDate"
            type="date"
            title="选择时间"
            @confirm="dealSendDate"
            @cancel="closeSelectSendDate"
          />
        </van-popup>
        <van-popup v-model="popupReturnDate" position="bottom">
          <van-datetime-picker
            v-model="page.currentDate"
            type="date"
            title="选择时间"
            @confirm="dealReturnDate"
            @cancel="closeSelectReturnDate"
          />
        </van-popup>

        <van-cell-group>
          <van-row v-for="(item, key) in page.company" :key="item.olsid">
            <van-cell :title="item.company" />
            <van-field
              label="联系人"
              type="text"
              v-model="item.salesman_name"
              required
              placeholder="请输入联系人"
            />
            <van-field
              label="联系号码"
              type="tel"
              v-model="item.salesman_phone"
              required
              placeholder="请输入联系人电话"
              :rules="[{ pattern: page.phone, message: '不符合手机号码格式' }]"
            />
            <van-field
              label="送修时间"
              v-model="item.send_date"
              readonly
              placeholder="点击选择日期"
              @click="showSelectSendDate(key)"
            />
            <van-field
              label="返还时间"
              v-model="item.return_date"
              readonly
              placeholder="点击选择日期"
              @click="showSelectReturnDate(key)"
            />

            <van-field name="radio" label="发票">
              <template #input>
                <van-radio-group v-model="item.invoice" direction="horizontal">
                  <van-radio name="专票">专票</van-radio>
                  <van-radio name="普票">普票</van-radio>
                  <van-radio name="无票">无票</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              label="金额"
              v-if="page.isOpenOffer_formOffer == '1'"
              type="number"
              v-model="item.total_price"
              required
              placeholder="请输入金额"
            />
            <div class="com_cus">
              <van-col span="6">维修周期</van-col>
              <van-col span="10">
                <van-stepper v-model="item.cycle" @change="onChange(key)" />
              </van-col>
              <van-col span="6">天</van-col>
            </div>
            <div class="com_cus">
              <van-col span="6">{{ page.selecttext }}</van-col>
              <van-col span="16">
                <van-switch v-model="item.proposal" @click="editproposal(item.proposal,key)"/>
              </van-col>
            </div>

            <!-- <van-field label="维修周期" type="digit" v-model="item.cycle" required  placeholder="请输入天数"/> -->
          </van-row>
        </van-cell-group>
        <div style="margin: 20px 0;text-align: center;">
          <van-button native-type="button" type="info" @click="page.showSelectcompany = false">确定并关闭弹窗</van-button>
        </div>
      </van-popup>
      <!--添加维修商-->
      <div class="van-doc-demo-block">
        <h2 class="van-doc-demo-block__title">相关维修文件</h2>
        <van-uploader
          multiple
          v-model="form.fileList"
          image-fit="cover"
          max-size="10485760"
          @oversize="oversize"
          :before-delete="del_file"
          :before-read="beforeRead"
          :after-read="afterRead"
          :max-count="page.max_count"
        />
      </div>
      <div style="margin: 16px">
        <van-button
          block
          color="#FFB800"
          v-if="page.shownature"
          native-type="button"
          @click="changeFormType('tmp_save')"
          style="margin-bottom: 16px"
        >
          暂时保存
        </van-button>
        <van-button block type="primary" native-type="button" @click="changeFormType('overhaul')">
          确认并提交
        </van-button>
      </div>
    </van-form>
  </div>
  <div v-else>
    <div class="global-loading">
      <van-loading color="#1989fa" size="60px" />
    </div>
  </div>
</template>

<script>
import Qs from 'qs';
import {
  Divider,
  Cell,
  Col,
  CellGroup,
  Row,
  Icon,
  Field,
  Toast,
  Form,
  Button,
  Tab,
  Tabs,
  Popup,
  Picker,
  DatetimePicker,
  TreeSelect,
  Uploader,
  Loading,
  RadioGroup,
  Radio,
  Stepper,
  Switch,
  ImagePreview,
  Dialog,
  Notify,
  Calendar,
} from 'vant';
import { getInfo, submit, get_real_location } from "@/api/repair/accept";
import AssetsInfo from "@/components/Assetsinfo";
import Table from '@/components/Table';
import wechatUtil from '@/utils/wechatUtil';
import feishuUtil from "@/utils/feishuUtil";
import {getRegexAssnum} from "@/utils/regex";

export default {
  name: 'accept',
  components: {
    [Divider.name]: Divider,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [Form.name]: Form,
    [Button.name]: Button,
    [Tab.name]: Tab,
    [Switch.name]: Switch,
    [Tabs.name]: Tabs,
    [Col.name]: Col,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [Row.name]: Row,
    [Stepper.name]: Stepper,
    [RadioGroup.name]: RadioGroup,
    [Loading.name]: Loading,
    [Radio.name]: Radio,
    [Uploader.name]: Uploader,
    [ImagePreview.name]: ImagePreview,
    [TreeSelect.name]: TreeSelect,
    [DatetimePicker.name]: DatetimePicker,
    [Toast.name]: Toast,
    AssetsInfo,
    Table,
  },
  data() {
    return {
      info: {
        factoryInfo: [],
        supplierInfo: [],
        repairInfo: [],
        repairdata: [],
      },
      page: {
        wx_sign_in: 0,
        showSelectUsername: false,//转单工程师控件显示
        showSelecttype: false,
        showSelectSolution: false,
        showFile: false,
        showPartsSelectPage: false,
        showSelectparts: false,
        shownature: false,
        showSelectpartsnum: false,
        showSelectnature: false,
        showSelectcompany: false,
        showSelectcompanynum: false,
        showCompanySelectPage: false,
        currentDate: new Date(),//当前日期
        showSelectdate: false,
        showparts: true,
        showcompany: false,
        response: '',
        response_date: '',
        Usernamecolumns: [],
        UsernameKeywords: '',//仅用于转单按钮的选择框搜索名称
        partscolumns: [],
        companycolumns: [],
        parts: [],
        partsAll: [],
        company: [],
        companyAll: [],
        errorType: '',
        Solutioncolumns: ['现场解决', '非现场解决'],
        naturecolumns: ['自修', '第三方维修', '维保厂家'],
        nature: '自修',
        Solution: '现场解决',
        activeIds: [],
        activeIndex: 0,
        is_display: 0,
        show_from: 1,
        defaultnature: 0, //默认值维修性质
        items: [],
        itemsKeyword: '', //故障问题搜索关键字
        isOpenOffer_formOffer: '0', //第三方维修是否有权限报价
        selecttext: '建议选择',
        partsSelectTips: '', //添加配件弹窗用
        showPartsPage: false, //添加配件弹窗用
        partsKeyword: '', //添加配件弹窗用
        partsActiveIds: [], //添加配件弹窗用
        partsActiveStepper: [], //添加配件弹窗用
        companySelectTips: '', //添加维修商弹窗用
        showCompanyPage: false, //添加维修商弹窗用
        companyKeyword: '', //添加维修商弹窗用
        companyActiveIds: [], //添加维修商弹窗用
        icon_name: 'yuyin',
        voiceTime: 0,
        img: [],
        table_parts: {
          headerData: [
            {text: '配件名称', field: 'parts', width: 40},
            {text: '规格型号', field: 'parts_model', width: 40},
            {text: '数量', field: 'count'},
          ],
          noData: '暂无相关配件信息',
          bodyData: []
        },
        table_company: {
          headerData: [
            {text: '维修商', field: 'company'},
            {text: '维修周期', field: 'cycletext'},
            {text: '发票', field: 'invoice'},
            {text: '金额', field: 'total_price'},
            {text: '最终选择', field: 'proposaltext'},
          ],
          noData: '暂无相关维修商信息',
          bodyData: []
        },
        phone: /^1[3456789]\d{9}$/,
        max_count: 3,
      },
      form: {
        edit_engineer: '',//转单工程师
        expect_arrive: '',
        reponse_remark: '',
        type: '',
        expect_time: '',
        errorTypeid: [],
        file: [],
        fileList: [],
        workplaces: [],
        repair_remark: '',
        dispose_detail: '',
        repair_type: '3',
        tmp_save: 1,
        is_scene: 1
      },
      popupReturnDate: false,
      ReturnDateKey: 0,
      popupSendDate: false,
      SendDateKey: 0
    }
  },
  computed: {
    UsernamecolumnsFilter() {
      return this.page.Usernamecolumns.filter((val) => {
        return val.indexOf(this.page.UsernameKeywords.trim()) >= 0
      })
    },
    itemFilter() {
      let arr = []
      this.page.items.forEach((v) => {
        let obj = {
          id: v.id,
          text: v.text,
          badge: v.badge,
          children: v['children'].filter((v1) => {
            return v1['text'].indexOf(this.page.itemsKeyword.trim()) >= 0
          })
        }
        if (obj.children.length > 0) {
          arr.push(obj)
        }
      })
      return arr
    },
    partsFilter() {
      //添加配件弹窗用
      return [
        {
          text: '配件',
          children: this.page.partscolumns.filter((v) => {
            return v['text'].indexOf(this.page.partsKeyword.trim()) >= 0
          }),
          badge: 0
        }
      ]
    },
    companyFilter() {
      //添加配件弹窗用
      return [
        {
          text: '维修商',
          children: this.page.companycolumns.filter((v) => {
            return v['text'].indexOf(this.page.companyKeyword.trim()) >= 0
          }),
          badge: 0
        }
      ]
    }
  },
  methods: {
    jugdePlatform() {
      return process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX
    },
    showSelectReturnDate(key) {
      this.ReturnDateKey = key
      this.popupReturnDate = true
    },
    closeSelectReturnDate() {
      this.ReturnDateKey = 0
      this.popupReturnDate = false
    },
    dealReturnDate(time) {
      this.page.company[this.ReturnDateKey].return_date = this.timeFormat(time)
      this.closeSelectReturnDate()
    },

    showSelectSendDate(key) {
      this.SendDateKey = key
      this.popupSendDate = true
    },
    closeSelectSendDate() {
      this.SendDateKey = 0
      this.popupSendDate = false
    },
    dealSendDate(time) {
      this.page.company[this.SendDateKey].send_date = this.timeFormat(time)
      this.closeSelectSendDate()
    },

    oversize() {
      //超出限制大小
      Notify({ type: 'danger', message: '图片超出10M大小限制' })
      return false
    },
    company_close() {
      let error = 0
      let msg = ''
      for (let i in this.page.company) {
        if (!this.page.company[i].salesman_name) {
          error = 1
          msg = this.page.company[i].company + '的联系人不能为空'
        }
        if (!this.page.company[i].salesman_phone) {
          error = 1
          msg = this.page.company[i].company + '的联系电话不能为空'
        }
        if (!this.page.company[i].total_price) {
          error = 1
          msg = this.page.company[i].company + '的金额不能为空'
        }
      }
      if (error == 1) {
        Toast.fail(msg)
        this.page.showSelectcompany = true
      }
    },
    async sign_in() {
      var _this = this
      switch (parseInt(process.env.VUE_APP_VERSION)) {
        case 1:
          // 微信版本
          wechatUtil
            .init([
              'getLocation', //获取位置
              'scanQRCode' //扫一扫
            ])
            .then((wx) => {
              // 这里写微信的接口
              wx.getLocation({
                type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
                success: async (res) => {
                  //let latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
                  //let longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
                  let latitude = 0
                  let longitude = 0
                  let loc = {}
                  loc.latitude = res.latitude
                  loc.longitude = res.longitude
                  await get_real_location(Qs.stringify(loc)).then(
                    (response) => {
                      if (response.status == 1) {
                        latitude = response.info['latitude']
                        longitude = response.info['longitude']
                      } else {
                        Toast.fail(response.msg)
                      }
                    }
                  )
                  wx.scanQRCode({
                    needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                    scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
                    async success(res) {
                      // let assnum = res.resultStr;
                      // if (assnum.indexOf("ODE_") > 0) {
                      //   assnum = res.resultStr.substr(9);
                      // }
                      const assnum = await getRegexAssnum(res.resultStr)

                      let params = {}
                      params.assnum = assnum
                      params.latitude = latitude
                      params.longitude = longitude
                      params.action = 'sign_in'
                      submit(Qs.stringify(params)).then((response) => {
                        if (response.status == 1) {
                          _this.page.wx_sign_in = 0
                          Toast.success(response.msg)
                        } else {
                          Toast.fail(response.msg)
                        }
                      })
                    }
                  })
                }
              })
              /*wx.getLocation({
                needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
                success(res) {
                    var assnum = res.resultStr;// 当needResult 为 1 时，扫码返回的结果
                    if (assnum.indexOf("ODE_") > 0) {
                        assnum = res.resultStr.substr(9);
                    }
                    _this.$router.push({
                        name: 'todo',
                        query: {
                            assnum: assnum
                        }
                    });
                }
            });*/
            })
          break
        case 2:
          // 飞书版本
          await feishuUtil.init(['scanCode', 'getLocation'])
          window.h5sdk.ready(() => {
            window.tt.getLocation({
              success: async (res) => {
                // alert(`经度 ${res.longitude}，纬度 ${res.latitude}`);
                //let latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
                //let longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
                let latitude = 0
                let longitude = 0
                let loc = {}
                loc.latitude = res.latitude
                loc.longitude = res.longitude
                await get_real_location(Qs.stringify(loc)).then((response) => {
                  if (response.status === 1) {
                    latitude = response.info['latitude']
                    longitude = response.info['longitude']
                  } else {
                    Toast.fail(response.msg)
                  }
                })
                window.tt.scanCode({
                  success(res1) {
                    // alert(JSON.stringify(`${res.result}`));
                    let params = {}
                    params.assnum = res1.result
                    params.latitude = latitude
                    params.longitude = longitude
                    params.action = 'sign_in'
                    submit(Qs.stringify(params)).then((response) => {
                      if (response.status === 1) {
                        _this.page.wx_sign_in = 0
                        Toast.success(response.msg)
                      } else {
                        Toast.fail(response.msg)
                      }
                    })
                  },
                  fail() {
                    alert(`调用失败`)
                  }
                })
              },
              fail() {
                alert(`调用失败`)
              }
            })
          })
          break
      }
    },
    onChange(key) {
      this.page.company[key].cycletext = this.page.company[key].cycle + '天'
    },
    editproposal(proposal, index) {
      if (!proposal) {
        for (let i in this.page.company) {
          if (i == index) {
            this.page.company[i].proposal = true
            this.page.company[i].proposaltext = '是'
          } else {
            this.page.company[i].proposal = false
            this.page.company[i].proposaltext = '否'
          }
        }
      } else {
        for (let i in this.page.company) {
          this.page.company[i].proposal = false
          this.page.company[i].proposaltext = '否'
        }
      }
    },
    del_file(index) {
      let name = index['file']['name']
      if (this.form.file.length > 0 && this.form.fileList.length > 0) {
        let index = this.form.file.findIndex((v) => {
          return v.old_name === name
        })
        this.form.file.splice(index, 1)
        this.form.fileList.splice(index, 1)
      }
    },
    beforeRead(file) {
      if (file.length > this.page.max_count) {
        Notify({
          type: 'danger',
          message: `最多只能选择${this.page.max_count}个图片`
        })
        return false
      }
      return true
    },
    afterRead(file) {
      let values = []
      values.action = 'upload'
      values.base64 = file.content
      values.fileName = file.file.name
      submit(Qs.stringify(values)).then((response) => {
        let file = {
          file_name: response.data.formerly,
          save_name: response.data.title,
          file_url: response.data.src,
          src: process.env.VUE_APP_BASE_PROXY_URL + response.data.src,
          file_type: response.data.ext,
          file_size: response.data.size,
          old_name: values.fileName
        }
        this.form.file.push(file)
        /*this.$router.push('/');*/
      })
    },
    changeFormType(tmp_save) {
      this.form.tmp_save = tmp_save //改变提交表单的formType
      try {
        this.$refs.repairForm.submit()
      } catch (error) {
        console.error('表单提交异常:', error)
        Toast.fail('表单提交失败，请检查填写内容')
      }
    },
    timeFormat(time) {
      // 时间格式化
      let year = time.getFullYear()
      let month = time.getMonth() + 1
      let day = time.getDate()
      return year + '-' + month + '-' + day
    },
    on_nature_Confirm(value) {
      if (value == '维保厂家') {
        if (this.info.is_guarantee !== 1) {
          Toast.fail('该设备不在保修期内，无法选择维保厂家')
          return
        }
        this.form.repair_type = '1'
        this.page.showparts = false
        this.page.showcompany = true
        // 自动填充维保厂家信息，并设置为最终选择
        this.page.company = [
          {
            olsid: 'guarantee_' + Date.now(), // 生成唯一ID
            company: this.info.guarantee_name,
            salesman_name: this.info.salesman_name,
            salesman_phone: this.info.salesman_phone,
            send_date: '',
            return_date: '',
            invoice: '专票',
            cycle: 1,
            cycletext: '1天',
            proposal: true, // 维保厂家默认设置为最终选择
            proposaltext: '是',
            total_price: '0'
          }
        ]
        this.page.table_company.bodyData = this.page.company
      } else if (value == '自修') {
        this.form.repair_type = '0'
        this.page.showparts = true
        this.page.showcompany = false
        // 清空维修商数据
        this.page.company = []
        this.page.table_company.bodyData = []
      } else {
        // 第三方维修
        this.form.repair_type = '2'
        this.page.showparts = false
        this.page.showcompany = true
        // 如果从维保厂家切换到第三方维修，清空维修商数据
        if (this.page.nature === '维保厂家') {
          this.page.company = []
          this.page.table_company.bodyData = []
        }
      }
      this.page.nature = value
      this.page.showSelectnature = false
    },
    onConfirmoverhaulDate(time) {
      this.form.expect_time = this.timeFormat(time)
      this.page.showSelectdate = false
    },
    onSolutionConfirm(value) {
      if (value == '非现场解决') {
        this.page.shownature = true
        this.form.repair_type = '0'
        this.form.is_scene = 0
      } else {
        this.page.shownature = false
      }
      this.page.Solution = value
      this.page.showSelectSolution = false
    },
    onClickitem(data) {
      if (this.page.activeIds.length === 0) {
        this.page.errorType = '点击选择故障问题'
      } else {
        this.page.errorType = `共${this.page.activeIds.length}项，点击查看`
      }
      //如果有该选项id 就在父级元素统计+1 反之则-1
      let index = this.page.items.findIndex((v) => {
        return parseInt(data.id.split('-')[0]) === parseInt(v.id)
      })
      if (this.page.activeIds.includes(data.id)) {
        this.page.items[index]['badge']++
      } else {
        this.page.items[index]['badge']--
      }
    },
    onClickPartsItem(data) {
      //添加配件用
      if (this.page.partsActiveIds.length === 0) {
        this.page.partsSelectTips = '点击选择配件'
      } else {
        this.page.partsSelectTips = `共${this.page.partsActiveIds.length}项，点击查看`
      }
      //统计选项徽章
      this.partsFilter[0]['badge'] = this.page.partsActiveIds.length
      //用于步进器及历史记录
      let noExist = true
      if (this.page.partsActiveStepper.length > 0) {
        this.page.partsActiveStepper.forEach((v, k) => {
          if (v.id === data.id) {
            noExist = false
            this.page.partsActiveStepper.splice(k, 1)
          }
        })
      }
      if (noExist) {
        let obj = {
          id: data.id,
          text: data.text,
          count: 1,
          parts: data.parts,
          parts_model: data.parts_model
        }
        this.page.partsActiveStepper.push(obj)
      }
      this.page.table_parts.bodyData = this.page.partsActiveStepper
    },
    onClickCompanyItem(data) {
      //添加配件用
      if (this.page.companyActiveIds.length === 0) {
        this.page.companySelectTips = '点击选择维修商'
      } else {
        this.page.companySelectTips = `共${this.page.companyActiveIds.length}项，点击查看`
      }
      //统计选项徽章
      this.companyFilter[0]['badge'] = this.page.companyActiveIds.length
      //用于步进器及历史记录
      let noExist = true
      if (this.page.company.length > 0) {
        this.page.company.forEach((v, k) => {
          if (v.company === data.text) {
            noExist = false
            this.page.company.splice(k, 1)
          }
        })
      }
      if (noExist) {
        let obj = {
          olsid: data.id,
          company: data.text,
          invoice: data.invoice ? data.invoice : '专票',
          salesman_name: data.salesman_name,
          salesman_phone: data.salesman_phone,
          send_date: data.send_date,
          return_date: data.return_date,
          cycle: data.cycle,
          cycletext: data.cycle ? data.cycle : '1' + '天',
          proposal: data.proposal ? data.proposal : false,
          proposaltext: data.proposal ? '是' : '否',
          total_price: data.price ? data.price : '0'
        }
        this.page.company.push(obj)
      }
      this.page.table_company.bodyData = this.page.company
    },
    onConfirm(value) {
      let values = []
      values.repid = this.$route.query.repid
      values.edit_engineer = value
      this.info.repairdata.response = value
      values.action = 'edit_engineer'
      this.page.showSelectUsername = false
      Dialog.confirm({
        title: '转单',
        message: `该维修单将转到${value}`
      })
        .then(() => {
          // on confirm
          submit(Qs.stringify(values)).then((response) => {
            Toast.success(response.msg)
            /*this.$router.push('/');*/
          })
        })
        .catch(() => {
          // on cancel
        })
    },
    onSubmit(values) {
      values.partname = ''
      values.model = ''
      values.num = ''
      values.problem = ''
      values.is_scene = this.form.is_scene
      for (let i in this.page.activeIds) {
        values.problem += this.page.activeIds[i] + '|'
      }
      values.problem = values.problem.substr(0, values.problem.length - 1)
      let addParts = []
      this.page.partsActiveStepper.forEach((v) => {
        this.page.partscolumns.forEach((v1) => {
          if (v.id === v1.id) {
            addParts.push({
              num: v.count,
              parts: v1.parts,
              model: v1.parts_model
            })
          }
        })
      })
      for (let i in addParts) {
        if (addParts[i].num > 0) {
          values.partname += addParts[i].parts + '|'
          values.model += addParts[i].model + '|'
          values.num += addParts[i].num + '|'
        }
      }

      // 初始化维修商相关字段
      values.companyName = ''
      values.offid = ''
      values.olsid = ''
      values.cycle = ''
      values.telphone = ''
      values.send_date = ''
      values.return_date = ''
      values.invoice = ''
      values.contracts = ''
      values.proposal = ''
      values.last_decisioin = ''
      values.totalPrice = ''

      // 如果是维保厂家，直接设置维修厂家信息
      if (this.page.nature === '维保厂家') {
        values.companyName = this.info.guarantee_name
        values.telphone = this.info.salesman_phone
        values.contracts = this.info.salesman_name
        // 维保厂家默认设置为最终选择
        if (this.page.isOpenOffer_formOffer == '1') {
          values.last_decisioin = this.info.guarantee_name
        } else {
          values.proposal = this.info.guarantee_name
        }
      } else {
        // 其他情况按原有逻辑处理
        for (let i in this.page.company) {
          values.companyName += this.page.company[i].company + '|'
          values.offid += '' + '|'
          values.olsid += this.page.company[i].olsid + '|'
          values.cycle += this.page.company[i].cycle + '|'
          values.telphone += this.page.company[i].salesman_phone + '|'
          values.send_date += this.page.company[i].send_date + '|'
          values.return_date += this.page.company[i].return_date + '|'
          values.invoice += this.page.company[i].invoice + '|'
          values.contracts += this.page.company[i].salesman_name + '|'
          values.totalPrice += this.page.company[i].total_price + '|'
          if (this.page.company[i].proposal == true) {
            if (this.page.isOpenOffer_formOffer == '1') {
              values.last_decisioin = this.page.company[i].company
            } else {
              values.proposal = this.page.company[i].company
            }
          }
        }
      }

      // 验证最终厂家选择
      if (this.form.tmp_save !== 'tmp_save') {
        // 正式提交时需要验证
        if (this.page.nature === '第三方维修' && this.page.company.length > 0) {
          // 检查是否选择了最终厂家
          let hasSelectedFinalCompany = this.page.company.some(
            (company) => company.proposal === true
          )
          if (!hasSelectedFinalCompany) {
            Toast.fail('请选择最终厂家')
            return false
          }
        }
        if (this.page.nature === '维保厂家' && !this.info.guarantee_name) {
          Toast.fail('维保厂家信息不完整')
          return false
        }
      }

      if (this.form.tmp_save == 'tmp_save') {
        values.tmp_save = 1
        values.action = 'tmp_save'
      } else {
        values.tmp_save = 0
        values.action = 'overhaul'
        //判断配件是否为空
        // if (addParts.length == 0 && this.form.repair_type == '0') {
        //   Toast.fail('自修时配件不能为空');
        //   return false;
        // }
      }

      //文件上传
      values.file_name = ''
      values.save_name = ''
      values.file_url = ''
      values.file_type = ''
      values.file_size = ''
      for (let i in this.form.file) {
        values.file_name += this.form.file[i].file_name + '|'
        values.save_name += this.form.file[i].save_name + '|'
        values.file_url += this.form.file[i].file_url + '|'
        values.file_type += this.form.file[i].file_type + '|'
        values.file_size += this.form.file[i].file_size + '|'
      }

      values.repair_type = this.form.repair_type
      // values.repid = this.$route.query.repid;
      values.repid = this.info.repairdata.repid
      values.assnum = this.info.assnum
      // if ('assnum' in this.$route.query){
      //     values.assnum = this.$route.query.assnum
      //
      // }
      // let _this = this;
      submit(Qs.stringify(values))
        .then((response) => {
          if (response.status == 1) {
            Toast({
              type: 'success', //失败fail
              duration: 2000, //2秒
              message: response.msg,
              icon: 'success', //失败cross
              forbidClick: true, //是否禁止背景点击，避免重复提交
              onClose() {
                //关闭后跳转到首页
                // _this.$router.push(this.$store.getters.moduleName+'/Repair/overhaulLists');
              }
            })
            // 成功后跳转
            this.$router.push(
              this.$store.getters.moduleName + '/Repair/overhaulLists'
            )
          }
        })
        .catch((error) => {
          // 处理API调用错误
          console.error('提交表单失败:', error)
          // Toast.fail已经在request.js中处理，这里只需要记录错误
          // 如果需要特殊处理，可以在这里添加
          if (error.message && error.message.includes('请选择最终厂家')) {
            // 特殊处理最终厂家选择错误
            setTimeout(() => {
              Dialog.alert({
                title: '提示',
                message: '请在维修商列表中选择一个作为最终选择，然后重新提交。'
              })
            }, 1000)
          }
        })
    },
    img() {
      ImagePreview(this.page.img)
    },
    bofang() {
      let audio = document.getElementById('audio')
      if (audio.paused) {
        //判断当前的状态是否为暂停，若是则点击播放，否则暂停
        this.page.icon_name = 'zanting'
        audio.play()
        let sec = this.page.voiceTime * 1000
        //变更图标
        setTimeout(() => {
          this.page.icon_name = 'yuyin'
        }, sec)
      } else {
        this.page.icon_name = 'yuyin'
        audio.pause()
      }
    },
    getInfo(repid, assnum) {
      let params = { repid: repid, assnum: assnum, action: 'overhaul' }
      getInfo(params).then((response) => {
        this.info = response.asArr
        this.info.repairdata = response.repArr
        this.page.response_date = response.response_date
        this.page.response = response.response
        this.page.partsAll = response.partsAll
        this.page.companyAll = response.company
        this.page.wx_sign_in = response.wx_sign_in
        this.form.repair_remark = response.repArr.repair_remark
        this.form.dispose_detail = response.repArr.dispose_detail
        this.page.isOpenOffer_formOffer = response.isOpenOffer_formOffer
        this.page.show_from = response.is_display
        if (this.info.repairdata.wxTapeAmr != '') {
          this.info.repairdata.wxTapeAmr =
            process.env.VUE_APP_BASE_PROXY_URL + this.info.repairdata.wxTapeAmr
          this.page.voiceTime = this.info.repairdata.seconds
        }
        for (let i in this.info.repairdata.pic_url) {
          this.page.img.push(
            process.env.VUE_APP_BASE_PROXY_URL + this.info.repairdata.pic_url[i]
          )
        }
        if (response.isOpenOffer_formOffer == '1') {
          this.page.selecttext = '最终选择'
        } else {
          this.page.selecttext = '建议选择'
        }
        if (response.repArr.expect_time != '1970/01/01') {
          this.form.expect_time = response.repArr.expect_time
        }
        for (let i in response.partsAll) {
          let partsObj = {
            id: i,
            text:
              response.partsAll[i].parts +
              '(规格型号:' +
              response.partsAll[i].parts_model +
              ')',
            parts: response.partsAll[i].parts,
            parts_model: response.partsAll[i].parts_model
          }
          this.page.partscolumns.push(partsObj)
        }
        for (let i in response.company) {
          let companyObj = {
            id: response.company[i].olsid,
            text: response.company[i].sup_name,
            salesman_name: response.company[i].salesman_name,
            salesman_phone: response.company[i].salesman_phone,
            status: response.company[i].status,
            send_date: response.company[i].send_date,
            return_date: response.company[i].return_date
          }
          this.page.companycolumns.push(companyObj)
        }
        for (let i in response.errorType) {
          this.page.items.push(response.errorType[i])
        }
        //暂存-自修
        if (response.temporary) {
          this.page.activeIds = response.temporary.activeIds
          this.form.is_scene = 0
          if (this.page.activeIds.length === 0) {
            this.page.errorType = '点击选择故障问题'
          } else {
            this.page.errorType = `共${this.page.activeIds.length}项，点击查看`
          }
          for (let i in this.page.activeIds) {
            let index = this.page.items.findIndex((v) => {
              return (
                parseInt(this.page.activeIds[i].split('-')[0]) ===
                parseInt(v.id)
              )
            })
            if (this.page.activeIds.includes(this.page.activeIds[i])) {
              this.page.items[index]['badge']++
            } else {
              this.page.items[index]['badge']--
            }
          }
          //文件
          for (let i in response.temporary.files) {
            response.temporary.files[i].src =
              process.env.VUE_APP_BASE_PROXY_URL +
              response.temporary.files[i].file_url
            this.form.file.push(response.temporary.files[i])

            this.form.fileList.push(response.temporary.files[i])
            this.form.fileList[i].url = this.form.fileList[i].src
          }
          //解决方式
          this.page.shownature = true
          this.page.Solution = '非现场解决'
          this.page.showSelectSolution = false
          if (response.repArr.repair_type == '0') {
            this.form.repair_type = '0'
          } else {
            this.form.repair_type = '2'
            this.page.defaultnature = 1
            this.page.nature = '第三方维修'
            this.page.showparts = false
            this.page.showcompany = true
          }
          //配件
          for (let i in response.temporary.parts) {
            let index = this.partsFilter[0].children.findIndex((v) => {
              if (
                v.parts == response.temporary.parts[i].parts &&
                v.part_model == response.temporary.parts[i].parts_model
              ) {
                return true
              }
            })
            this.page.partsActiveIds.push(String(index))
            this.onClickPartsItem(this.partsFilter[0].children[i])
          }

          //维修商
          for (let i in response.temporary.company) {
            let title = response.temporary.company[i]['offer_company']
            let index
            for (let j in this.page.companycolumns) {
              if (this.page.companycolumns[j].text == title) {
                index = j
              }
            }
            let proposal
            if (response.temporary.company[i]['proposal'] == '1') {
              proposal = true
            } else {
              proposal = false
            }
            if (
              response.temporary.company[i]['last_decisioin'] == '1' &&
              response.isOpenOffer_formOffer == '1'
            ) {
              proposal = true
            } else {
              proposal = false
            }
            this.page.companyActiveIds.push(
              String(response.company[index].olsid)
            )
            this.page.company.push({
              index: index,
              salesman_name: response.temporary.company[i]['offer_contacts'],
              salesman_phone: response.temporary.company[i]['telphone'],
              send_date: response.temporary.company[i]['send_date'],
              return_date: response.temporary.company[i]['return_date'],
              invoice: response.temporary.company[i]['invoice'],
              cycle: response.temporary.company[i]['cycle'],
              cycletext: response.temporary.company[i]['cycle'] + '天',
              proposal: proposal,
              proposaltext: proposal ? '是' : '否',
              company: response.temporary.company[i]['offer_company'],
              olsid: response.temporary.company[i]['offid'],
              total_price: response.temporary.company[i]['total_price']
            })
            this.page.table_company.bodyData = this.page.company
          }
          if (this.page.companyActiveIds.length === 0) {
            this.page.companySelectTips = '点击选择维修商'
          } else {
            this.page.companySelectTips = `共${this.page.companyActiveIds.length}项，点击查看`
          }
          //统计选项徽章
          this.companyFilter[0]['badge'] = this.page.companyActiveIds.length
        }
        let values = []
        values.action = 'getengineer'
        // values.repid = this.$route.query.repid;
        values.repid = this.info.repairdata.repid
        submit(Qs.stringify(values)).then((response) => {
          /*this.$router.push('/');*/
          this.form.edit_engineer = response.response
          this.page.Usernamecolumns = response.data
          this.page.is_display = 1
        })
      })
    }
  },
  mounted() {
    //获取相关设备信息
    this.getInfo(this.$route.query.repid, this.$route.query.assnum)
  }
}
</script>

<style lang="scss" scoped>
.showPartsTable {
  .th {
    background-color: #e6e6e654;
  }

  ::v-deep .van-col {
    text-align: center;
  }
}

.van-button--default {
  margin: 5px 0;
}

.pj {
  margin-top: 10px;
}

.van-form {
  background: #fff;
}

.cus_table td {
  position: relative;
  padding: 9px 0px;
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  border: 1px solid #e6e6e6;
  text-align: center;
}

.cus_table {
  width: 94%;
  background-color: #fff;
  color: #666;
  margin: 0 auto;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
  margin-bottom: 1px;
}

.cus_td {
  background-color: #f2f2f2;
  width: 90px;
}

.com_cus {
  margin: 10px 0;
  text-align: center;
  height: 30px;
}
</style>
