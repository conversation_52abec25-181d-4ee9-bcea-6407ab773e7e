<template>
    <div class="show" v-if="page.is_display===1">
        <van-row type="flex" justify="center">
            <van-col>
                <div class="emer_title" style="color: initial;">{{info.emergency}}</div>
            </van-col>
        </van-row>
        <van-row type="flex" justify="space-around">
            <van-col span="6">
                <van-tag plain type="primary">{{info.type}}</van-tag>
            </van-col>
            <van-col span="12" class="emer_time">{{info.add_time}}</van-col>
        </van-row>

        <van-row type="flex" justify="end">
            <van-col>{{info.adddate}}</van-col>
        </van-row>

        <van-divider/>
        <div class="noticeDetail" v-html="info.content"></div>
        <van-divider/>

        <van-row type="flex">
            <van-col>相关文件：</van-col>
        </van-row>
        <div class="content_file" v-if="page.file !== undefined && page.file.length > 0">
            <van-field center clearable :value="item.file_name" readonly v-for="item in page.file" :key="item.id">
                <template #left-icon>
                    <van-image width="20" height="20" :src="item.type_img"/>
                </template>
                <template #button>
                    <van-button size="small" type="info" v-if="item.file_type === 'pdf'"
                                @click="scan_file(item.file_url)">预览
                    </van-button>
                    <van-button size="small" type="primary" @click="down(item.file_url,item.file_name,item.file_size)">
                        下载
                    </van-button>
                </template>
            </van-field>
        </div>
        <div class="no_file" v-else>暂无相关文件</div>
        <van-divider/>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import {Divider, Col, Row, Card, Button, Tag, Field, Icon, Image as VanImage,Loading} from "vant";
    import {getInfo} from "@/api/archives/list";

    export default {
        name: 'getfile',
        components: {
            [Divider.name]: Divider,
            [Col.name]: Col,
            [Row.name]: Row,
            [Card.name]: Card,
            [Loading.name]: Loading,
            [Button.name]: Button,
            [Tag.name]: Tag,
            [Field.name]: Field,
            [Icon.name]: Icon,
            [VanImage.name]: VanImage,
        },
        data() {
            return {
                info: [],
                page: {
                    is_display:0,
                },
            }
        },
        methods: {
            down(path, name, size) {
                this.$router.push({
                    name: 'dw',
                    query: {
                        path: path,
                        name: name,
                        size: size,
                    }
                });
            },
            scan_file(path) {
                this.$router.push({
                    name: 'showFile',
                    query: {
                        path: path
                    }
                });
            },
            getInfo(id) {
                getInfo(
                    {id: id}
                ).then(response => {
                    this.info = response.data;
                    this.page.file = response.file;
                    this.page.is_display = 1;
                })
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.id);
        }
    }
</script>

<style scoped lang="scss">
    .emer_title {
        text-align: center;
        padding-top: 20px;
        padding-bottom: 20px;
        font-size: 20px;
    }
    .noticeDetail{
        padding: 0 10px 20px 10px;
    }
    .van-col {
        padding: 0 10px;
    }
    .van-button--small {
        min-width: 45px;
    }
    .emer_time{
        color:#A9A9A9;
    }
</style>
<style>
    .noticeDetail img {
        max-width: 100%;
        max-height: 20rem;
    }
    .no_file{
        text-align: center;
        color: #A9A9A9;
    }
</style>
