import axios from 'axios'
import {Toast} from 'vant';
import router from "@/router";
// 创建一个axios实例
// axios.defaults.headers['JWT'] = document.domain
// axios.defaults.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS, PUT, DELETE'
// axios.defaults.headers['Access-Control-Allow-Headers'] = 'Accept,DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Cookie'
// axios.defaults.headers['Access-Control-Allow-Credentials'] = true
const service = axios.create({
    // baseURL: process.env.VUE_APP_BASE_PROXY_URL+process.env.VUE_APP_BASE_AXIOS_URL, // url = base url + request url
    baseURL: process.env.VUE_APP_BASE_AXIOS_URL, // url = base url + request url
    withCredentials: true, // send cookies when cross-domain requests
    timeout: 15000, // request timeout
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
});
console.log(process.env.VUE_APP_PLATFORM)
// 添加请求拦截器
service.interceptors.request.use((config) => {
    config.headers['Platform'] = process.env.VUE_APP_PLATFORM
    if(process.env.VUE_APP_PLATFORM == 'APP'){
        config.headers['Auth'] = localStorage.getItem('Auth')
    }
    //全局传token值
    config.headers['Authorization'] = '1';
    //console.log(config)
    // 在发送请求之前做些什么
    return config;
}, function (error) {
    // 对请求错误做些什么
    return Promise.reject(error);
});

// 添加响应拦截器
service.interceptors.response.use((response) => {
    // 对响应数据做点什么（全局封装）
    const res = response.data;
    if (res.status === 1) {
        //正确直接返回
        return res;
    } else if (res.code === 200) {
        // 处理新的接口格式 (code: 200)
        return res;
    } else if (res.status === -1) {
        //Toast.fail(res.msg);
        Toast({
            type:'fail',//失败fail
            duration:2000,//2秒
            message: res.msg,
            forbidClick:true,//是否禁止背景点击，避免重复提交
            icon:'cross',//失败cross
        });
        return res;
    }else if(res.status == '-1'){
        //Toast.fail(res.msg);
        Toast({
            type:'fail',//失败fail
            duration:2000,//2秒
            message: res.msg,
            forbidClick:true,//是否禁止背景点击，避免重复提交
            icon:'cross',//失败cross
        });
        return res;
    } else if (res.status === 302) {
        let msg = '非法请求';
        if (res.msg !== '') {
            msg = res.msg
        }
        // Toast.fail(msg);
        let redirectUrl = `/fail/${msg}`;
        //重定向
        if (res.redirectUrl !== '' && typeof res.redirectUrl !== 'undefined') {
            redirectUrl = res.redirectUrl
        }
        router.push({
            path: redirectUrl
        });
        //Promise.reject(new Error(msg));
    }else if (res.status === 999) {
        //999代码 为后台查询不到userid
        // window.location.href = process.env.VUE_APP_BASE_PROXY_URL+'/'+process.env.VUE_APP_BASE_AXIOS_URL+'/Login/getUserOpenId'
        router.replace({path:'/login'})
    } else {
        //错误弹提示
        //Toast.fail(res.msg);
        // console.log('后台重定向到页面');
        return Promise.reject(new Error(res.msg || '接口访问失败： ' + response.request.responseURL))
    }
}, function (error) {
    // 对响应错误做点什么（全局封装）
    Toast.fail('网络连接失败');
    return Promise.reject(error);
});

export default service
