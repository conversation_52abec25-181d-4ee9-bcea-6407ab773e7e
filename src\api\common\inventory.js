import request from '@/utils/request'


export function getInventoryList(query) {
    query.inventory_plan_status = [1,2]
    return request({
        url: '/InventoryPlan/inventoryPlanList',
        method: 'post',
        data:query
    })
}
export function showInventoryPlan(query) {
    return request({
        url: 'InventoryPlan/showInventoryPlan?inventory_plan_id='+query.inventory_plan_id,
        method:'post',
        // data:query,
        // headers: {
        //     'Content-Type': 'application/x-www-form-urlencoded'
        //   }
    })
}

export function saveOrEndInventoryPlan(query){
    return request({
        url: 'InventoryPlan/saveOrEndInventoryPlan',
        method:'post',
        data:query
    })
}

// export function clientGetUndoneList(query){
//     return request({
//         url: 'InventoryPlan/showInventoryPlan',
//         method:'post',
//         data:query
//     })
// }
// export function clientGetFinishList(query){
//     return request({
//         url: 'InventoryPlan/showInventoryPlan',
//         method:'post',
//         data:query
//     })
// }

export function clientHandDeal(query){
    return request({
        url: 'InventoryPlan/dealInventoryAsset',
        method:'post',
        data:query
    })
}
// export function handleAllAsset(query){
//     return request({
//         url: 'InventoryPlan/saveOrEndInventoryPlan',
//         method:'post',
//         data:query
//     })
// }

export function getAssetInfo(assnum){
    return request({
        url: 'InventoryPlan/getAssetInfo?assnum='+assnum,
        method:'get'
    })
}