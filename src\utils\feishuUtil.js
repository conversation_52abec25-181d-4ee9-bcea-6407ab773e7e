import { getSignature } from "@/api/feishu";
import sha1 from "sha1";

export default {
    /* 初始化wxjsdk各种接口 */
    init(apiList = [
        'scanCode',
        'getRecorderManager',
        'getLocation',
        'saveFile'
    ], url = '') {
        //需要使用的api列表
        return new Promise((resolve, reject) => {
            getSignature().then(response => {
                var res = response.signPackage;
                const timestamp = new Date().getTime();
                const signature = sha1(`jsapi_ticket=${res.jsapiTicket}&noncestr=${res.nonceStr}&timestamp=${timestamp}&url=${res.url}`)
                if (res.appId) {
                    window.h5sdk.config({
                        debug: false,
                        appId: res.appId,
                        timestamp: timestamp,
                        nonceStr: res.nonceStr,
                        signature: signature,
                        jsApiList: apiList, // 必填，需要使用的jsapi列表。
                        onSuccess: function (result) {
                            //alert("JSSDK已获取权限", JSON.stringify(result));
                        },
                        onFail: function (err) {
                            //alert("JSSDK权限获取失败", JSON.stringify(err));
                        }
                    });
                    resolve()
                } else {
                    reject(res)
                }
            })
        })
    }
}
