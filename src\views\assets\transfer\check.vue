<template>
    <div class="con_detail">
        <van-tabs :border="false">
            <van-tab title="设备基本信息">
                <AssetsInfo :info='info'/>
            </van-tab>
            <van-tab title="转科申请信息">
                <van-cell-group>
                    <van-cell title="转科单号" :value="info.zkInfo.transfernum"/>
                    <van-cell title="申请科室" :value="info.zkInfo.tranout_depart_name"/>
                    <van-cell title="申请人" :value="info.zkInfo.applicant_user"/>
                    <van-cell title="申请时间" :value="info.zkInfo.applicant_time"/>
                    <van-cell title="申请原因" :value="info.zkInfo.tran_reason"/>
                    <van-cell title="转入科室" :value="info.zkInfo.tranin_depart_name"/>
                    <van-cell title="科室负责人" :value="info.zkInfo.tranin_departrespon"/>
                    <van-cell title="联系电话" :value="info.zkInfo.departtel"/>
                </van-cell-group>
            </van-tab>
            <van-tab title="转科验收信息" v-if="page.show_from==0">
                <van-cell title="验收人" :value="info.zkInfo.check_user"/>
                <van-cell title="验收时间" :value="info.zkInfo.check_time"/>
                <van-cell title="验收意见" :value="info.zkInfo.is_check_txt"></van-cell>
                <van-cell title="备注" :value="info.zkInfo.check"/>
            </van-tab>
        </van-tabs>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">转科验收表单</h2>
        </div>
        <van-form ref="transferForm" @submit="onSubmit" v-if="page.show_from==1">
            <van-cell-group>
                <van-field label="验收人" :value="page.username" readonly/>
                <van-field name="checkdate" label="验收时间" :value="page.date" readonly/>
                <van-field
                        v-model="form.remark"
                        name="check"
                        rows="1"
                        autosize
                        label="验收意见"
                        type="textarea"
                        placeholder="请输入验收意见"
                />
            </van-cell-group>
            <div style="margin: 16px;">
                <van-button round block type="primary" native-type="Button" @click="changeFormType('yes')">
                    确认设备完好无损并通过验收
                </van-button>
                <p></p>
                <van-button round block type="warning" native-type="Button" @click="changeFormType('no')">
                    验收不通过
                </van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Step,
        Steps,
        Radio,
        RadioGroup,
        Toast
    } from 'vant';
    import {getInfo, submit} from "@/api/assets/transfer/check";
    import AssetsInfo from "@/components/Assetsinfo";

    export default {
        name: 'check',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Step.name]: Step,
            [Steps.name]: Steps,
            [Radio.name]: Radio,
            [RadioGroup.name]: RadioGroup,
            AssetsInfo,
        },
        data() {
            return {
                form: {
                    formType: 1,
                    atid: 0,
                    remark: '',
                },
                page: {
                    active: 4,//步骤条
                    username: "",
                    date: "",
                    show_from: 1,
                },
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    approves: [],
                    zkInfo: [],
                },
            }
        },
        methods: {
            getInfo(atid) {
                let params = {atid: atid};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.info.zkInfo = response.zkInfo;
                    this.page.username = response.username;
                    this.page.date = response.date;
                    this.info.approves = response.approves;
                    this.page.show_from = response.is_display;
                    if (this.page.show_from == 0) {
                        if (this.info.zkInfo.is_check == 1) {
                            this.info.zkInfo.is_check_txt = "通过";
                        } else {
                            this.info.zkInfo.is_check_txt = "不通过";
                        }
                    }
                });
            },
            onSubmit(values) {
                if (this.form.formType == 'yes') {
                    values.res = 1;
                } else {
                    values.res = 2;
                }
                values.atid = this.$route.query.atid;
                let _this = this;
                submit(Qs.stringify(values)).then(response => {
                    if (response.status == 1) {
                        Toast({
                            type: 'success',//失败fail
                            duration: 2000,//2秒
                            message: response.msg,
                            icon: 'success',//失败cross
                            forbidClick: true,//是否禁止背景点击，避免重复提交
                            onClose() {
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Transfer/checkLists');
                            }
                        });
                    }
                })
            },
            changeFormType(type) {
                this.form.formType = type;//改变提交表单的formType
                this.$refs.transferForm.submit();//执行提交
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.atid);
        }
    }
</script>
<style scoped lang="scss">
    .card-header {
        margin-top: 20px;
    }
</style>
