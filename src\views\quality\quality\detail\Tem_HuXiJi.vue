<template>
    <div class="con_detail" v-if="page.is_display===1">
        <Quality :info="info"/>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">质控检测明细（模板：{{page.templatename}}）</h2>
        </div>

        <van-form ref="qualityForm" @submit="onSubmit">
            <van-field name="lookslike" label="外观功能：">
                <template #input>
                    <van-radio-group @change="looklike_change" v-model="form.lookslike" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field v-show="show_lookslike_desc" name="lookslike_desc" type="textarea" rows="2" autosize
                       v-model="form.lookslike_desc" placeholder="外观不符合的请说明情况" style="margin-bottom: 10px;"/>

            <van-collapse v-model="page.activeNames">
            <van-collapse-item :title="page.humidity" name="1">
                <div v-for="(item,key) in page.setting.humidity.slice(0,3)"  :key="key">
                    <van-row>
                        <van-col span="16">
                            <van-field :label="'设定值：'+item" v-model="page.humidityvalue[key]" type="number" placeholder="输出实测值" @change="on_h_Change(item,key)"/>
                        </van-col>
                        <van-col span="8">
                            <van-field v-model="page.humidity_value[key]" type="number" placeholder="示值" @change="on_hv_Change(item,key)"/>
                        </van-col>
                    </van-row>
                    <van-row class="row_center">
                        <van-col span="12">误差：{{page.h_tolerancevalue[key]}}</van-col>
                        <van-col span="12">示值误差：{{page.hv_tolerancevalue[key]}}</van-col>
                    </van-row>
                    <van-divider class="fenge"/>
                </div>
                <van-cell title="最大输出误差" :value="form.humidity_max_output"/>
                <van-cell title="最大示值误差" :value="form.humidity_max_value"/>
            </van-collapse-item>
            <van-collapse-item :title="page.aeration" name="2">
                <div v-for="(item,key) in page.setting.aeration.slice(0,3)" :key="key">
                    <van-row>
                        <van-col span="14">
                            <van-field :label="'设定值：'+item" v-model="page.aerationvalue[key]" type="number" placeholder="输出实测值" @change="on_a_Change(item,key)"/>
                        </van-col>
                        <van-col span="5">
                            <van-field v-model="page.aeration_value[key]" type="number" placeholder="示值" @change="on_av_Change(item,key)"/>
                        </van-col>
                        <van-col span="5">
                            <van-field v-model="page.setIE[key]" placeholder="I:E值"/>
                        </van-col>
                    </van-row>
                    <van-row class="row_center">
                        <van-col span="12">误差：{{page.a_tolerancevalue[key]}}</van-col>
                        <van-col span="8">示值误差：{{page.av_tolerancevalue[key]}}</van-col>
                    </van-row>
                    <van-divider class="fenge"/>
                </div>
                <van-cell title="最大输出误差" :value="form.aeration_max_output"/>
                <van-cell title="最大示值误差" :value="form.aeration_max_value"/>
            </van-collapse-item>
            <van-collapse-item :title="page.IOI" name="3">
                <div v-for="(item,key) in page.setting.IOI.slice(0,3)" :key="key">
                    <van-row>
                        <van-col span="16">
                            <van-field :label="'设定值：'+item" v-model="page.IOIvalue[key]" type="number" placeholder="输出实测值" @change="on_IOI_Change(item,key)"/>
                        </van-col>
                        <van-col span="8">
                            <van-field v-model="page.IOI_value[key]" type="number" placeholder="示值" @change="on_IOIv_Change(item,key)"/>
                        </van-col>
                    </van-row>
                    <van-row class="row_center">
                        <van-col span="12">误差：{{page.IOI_tolerancevalue[key]}}</van-col>
                        <van-col span="12">示值误差：{{page.IOIv_tolerancevalue[key]}}</van-col>
                    </van-row>
                    <van-divider class="fenge"/>
                </div>
                <van-cell title="最大输出误差" :value="form.IOI_max_output" />
                <van-cell title="最大示值误差" :value="form.IOI_max_value" />
            </van-collapse-item>
            <van-collapse-item :title="page.IPAP" name="4">
                <div v-for="(item,key) in page.setting.IPAP.slice(0,3)" :key="key">
                    <van-row>
                        <van-col span="16">
                            <van-field :label="'设定值：'+item" v-model="page.IPAPvalue[key]" type="number" placeholder="输出实测值" @change="on_IPAP_Change(item,key)"/>
                        </van-col>
                        <van-col span="8">
                            <van-field v-model="page.IPAP_value[key]" type="number" placeholder="示值" @change="on_IPAPv_Change(item,key)"/>
                        </van-col>
                    </van-row>
                    <van-row class="row_center">
                        <van-col span="12">误差：{{page.IPAP_tolerancevalue[key]}}</van-col>
                        <van-col span="12">示值误差：{{page.IPAPv_tolerancevalue[key]}}</van-col>
                    </van-row>
                    <van-divider class="fenge"/>
                </div>
                <van-cell title="最大输出误差" :value="form.IPAP_max_output"/>
                <van-cell title="最大示值误差" :value="form.IPAP_max_value"/>
            </van-collapse-item>
            <van-collapse-item :title="page.PEEP" name="5">
                <div v-for="(item,key) in page.setting.PEEP.slice(0,3)" :key="key">
                    <van-row>
                        <van-col span="16">
                            <van-field :label="'设定值：'+item" v-model="page.PEEPvalue[key]" type="number" placeholder="输出实测值" @change="on_PEEP_Change(item,key)"/>
                        </van-col>
                        <van-col span="8">
                            <van-field v-model="page.PEEP_value[key]" type="number" placeholder="示值" @change="on_PEEPv_Change(item,key)"/>
                        </van-col>
                    </van-row>
                    <van-row class="row_center">
                        <van-col span="12">误差：{{page.PEEP_tolerancevalue[key]}}</van-col>
                        <van-col span="12">示值误差：{{page.PEEPv_tolerancevalue[key]}}</van-col>
                    </van-row>
                    <van-divider class="fenge"/>
                </div>
                <van-cell title="最大输出误差" :value="form.PEEP_max_output"/>
                <van-cell title="最大示值误差" :value="form.PEEP_max_value"/>
            </van-collapse-item>
            <van-collapse-item title="机械通气模式评价" name="6">
                <div>
                    <van-cell title="容量预制模式" size="large">
                        <template #label>
                            <van-field name="capacity_precut_mode">
                                <template #input>
                                    <van-radio-group v-model="form.capacity_precut_mode" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>

                    <van-cell title="流量触发功能" size="large">
                        <template #label>
                            <van-field name="traffic_trigger">
                                <template #input>
                                    <van-radio-group v-model="form.traffic_trigger" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>

                    <van-cell title="压力预制模式" size="large">
                        <template #label>
                            <van-field name="pressure_precut_mode">
                                <template #input>
                                    <van-radio-group v-model="form.pressure_precut_mode" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>

                    <van-cell title="压力触发功能" size="large">
                        <template #label>
                            <van-field name="pressure_tigger">
                                <template #input>
                                    <van-radio-group v-model="form.pressure_tigger" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>
                </div>
            </van-collapse-item>
            <van-collapse-item title="安全报警功能等检查" name="7">
                <div>
                    <van-cell title="电源报警" size="large">
                        <template #label>
                            <van-field name="power_supply_alarm">
                                <template #input>
                                    <van-radio-group v-model="form.power_supply_alarm" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>

                    <van-cell title="氧浓度上/下限报警" size="large">
                        <template #label>
                            <van-field name="oxygen_concentration_bound">
                                <template #input>
                                    <van-radio-group v-model="form.oxygen_concentration_bound" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>

                    <van-cell title="气源报警" size="large">
                        <template #label>
                            <van-field name="gas_supply_alarm">
                                <template #input>
                                    <van-radio-group v-model="form.gas_supply_alarm" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>

                    <van-cell title="窒息报警" size="large">
                        <template #label>
                            <van-field name="apnea_alarm">
                                <template #input>
                                    <van-radio-group v-model="form.apnea_alarm" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>

                    <van-cell title="气道压力上/下限报警" size="large">
                        <template #label>
                            <van-field name="AWP_alarm">
                                <template #input>
                                    <van-radio-group v-model="form.AWP_alarm" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>

                    <van-cell title="病人回路过压保护功能" size="large">
                        <template #label>
                            <van-field name="loop_overvoltage_pretection">
                                <template #input>
                                    <van-radio-group v-model="form.loop_overvoltage_pretection" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>

                    <van-cell title="分钟通气量上/下限报警" size="large">
                        <template #label>
                            <van-field name="minute_ventilation_bound">
                                <template #input>
                                    <van-radio-group v-model="form.minute_ventilation_bound" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>

                    <van-cell title="按键功能检查（含键盘锁）" size="large">
                        <template #label>
                            <van-field name="press_key">
                                <template #input>
                                    <van-radio-group v-model="form.press_key" direction="horizontal">
                                        <van-radio name="1">符合</van-radio>
                                        <van-radio name="2">不符合</van-radio>
                                        <van-radio name="3">不适用</van-radio>
                                    </van-radio-group>
                                </template>
                            </van-field>
                        </template>
                    </van-cell>
                </div>
            </van-collapse-item>
            </van-collapse>
            <van-field name="total_desc" type="textarea" rows="3" show-word-limit maxlength="120" label="检测备注：" v-model="form.total_desc" placeholder="请填写检测备注（如偏离情况说明）"/>

            <div class="mp">
                <van-divider>设备铭牌照片</van-divider>
                <van-row type="flex" justify="center">
                    <van-uploader multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" :before-read="beforeRead" v-model="form.nameplate_fileList" :after-read="nameplate_afterRead"
                                  :before-delete="del_nameplate">
                    </van-uploader>
                </van-row>
                <van-divider>检测仪器视图照片</van-divider>
                <van-row type="flex" justify="center">
                    <van-uploader multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" :before-read="beforeRead" v-model="form.instrument_fileList" :after-read="instrument_afterRead"
                                  :before-delete="del_instrument">
                    </van-uploader>
                </van-row>
                <div style="margin: 16px;">
                    <van-button round block color="#FFB800" native-type="button" @click="changeFormType('keepquality')"
                                style="margin-bottom: 16px;">
                        暂时保存
                    </van-button>
                    <van-button round block type="primary" native-type="button" @click="changeFormType('end')">
                        确认并提交
                    </van-button>
                </div>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>
<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Row,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Toast,
        Loading,
        Uploader,
        ImagePreview,
        Image as VanImage,
        Swipe,
        SwipeItem,
        RadioGroup,
        Radio,
        Col,
        Stepper,
        Notify,
        Collapse,
        CollapseItem
    } from 'vant';
    import {getInfo, submit} from "@/api/quality/detail";
    import Quality from "@/components/Quality";

    export default {
        name: 'ChuChanYi',
        components: {
            [Divider.name]: Divider,
            [Collapse.name]: Collapse,
            [CollapseItem.name]: CollapseItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Row.name]: Row,
            [Col.name]: Col,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Uploader.name]: Uploader,
            [Tab.name]: Tab,
            [Stepper.name]: Stepper,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Tabs.name]: Tabs,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [VanImage.name]: VanImage,
            [Loading.name]: Loading,
            Quality,
        },
        data() {
            return {
                info: {
                    asInfo: [],
                    qsInfo: [],
                },
                page: {
                    humidity: '潮气量(ml) (vcv模式) 最大允差：',
                    aeration: '强制通气频率与呼吸比(vcv模式)最大允差:',
                    IOI: '吸入氧浓度FiO₂ 最大允差：',
                    IPAP: '吸气压力水平 PCV或新生儿呼吸的PLV模式 f=20 最大允差：',
                    PEEP: '呼气末正压PEEP VCV模式 Vt=400ml f=20 最大允差：',
                    file_data: '',
                    humidityvalue: [],
                    aerationvalue: [],
                    IOIvalue: [],
                    IPAPvalue: [],
                    PEEPvalue: [],
                    h_tolerancevalue: [],
                    humidity_value: [],
                    hv_tolerancevalue: [],
                    a_tolerancevalue: [],
                    aeration_value: [],
                    av_tolerancevalue: [],
                    setIE: [],
                    IOI_tolerancevalue: [],
                    IOI_value: [],
                    IOIv_tolerancevalue: [],
                    IPAP_tolerancevalue: [],
                    IPAP_value: [],
                    IPAPv_tolerancevalue: [],
                    PEEP_tolerancevalue: [],
                    PEEP_value: [],
                    PEEPv_tolerancevalue: [],
                    setting: {
                        humidity: [],
                        aeration: [],
                    },
                    templatename: '',
                    nameplate: [],
                    instrument_view: [],
                    detail_result: [],
                    activeNames:[],
                    positions: 0,
                    is_display: 0,
                  max_count:3
                },
                form: {
                    lookslike: "1",
                    charge_result: '1',
                    internal_discharge: '1',
                    type: 'keepquality',
                    humidity_max_value: 0,
                    humidity_max_output: 0,
                    aeration_max_value: 0,
                    aeration_max_output: 0,
                    IOI_max_value: 0,
                    IOI_max_output: 0,
                    IPAP_max_value: 0,
                    IPAP_max_output: 0,
                    PEEP_max_value: 0,
                    PEEP_max_output: 0,
                    total_desc: "",
                    charge: "",
                    capacity_precut_mode: '1',
                    traffic_trigger: '1',
                    pressure_precut_mode: '1',
                    pressure_tigger: '1',
                    power_supply_alarm: '1',
                    oxygen_concentration_bound: '1',
                    gas_supply_alarm: '1',
                    apnea_alarm: '1',
                    AWP_alarm: '1',
                    loop_overvoltage_pretection: '1',
                    minute_ventilation_bound: '1',
                    press_key: '1',
                    nameplate_fileList: [],
                    instrument_fileList: [],
                },
                show_lookslike_desc:false,
            }
        },
        methods: {
            looklike_change(value){
                if(value == 2){
                    this.show_lookslike_desc = true;
                }else{
                    this.show_lookslike_desc = false;
                    this.form.lookslike_desc = '';
                }
            },
            on_h_Change(item, key) {
                let tolerance = Math.abs(item - this.page.humidityvalue[key]);
                this.page.h_tolerancevalue[key] = tolerance;
                let humidity_max_output = tolerance;
                for (let i in this.page.h_tolerancevalue) {
                    if (this.page.h_tolerancevalue[i] > humidity_max_output) {
                        humidity_max_output = this.page.h_tolerancevalue[i];
                    }
                }
                this.form.humidity_max_output = humidity_max_output;

            },
            on_hv_Change(item, key) {
                let tolerance = Math.abs(item - this.page.humidity_value[key]);
                this.page.hv_tolerancevalue[key] = tolerance;
                let humidity_max_value = tolerance;
                for (let i in this.page.hv_tolerancevalue) {
                    if (this.page.hv_tolerancevalue[i] > humidity_max_value) {
                        humidity_max_value = this.page.hv_tolerancevalue[i];
                    }
                }
                this.form.humidity_max_value = humidity_max_value;
            },
            on_a_Change(item, key) {
                let tolerance = Math.abs(item - this.page.aerationvalue[key]);
                this.page.a_tolerancevalue[key] = tolerance;
                let aeration_max_output = tolerance;
                for (let i in this.page.a_tolerancevalue) {
                    if (this.page.a_tolerancevalue[i] > aeration_max_output) {
                        aeration_max_output = this.page.a_tolerancevalue[i];
                    }
                }
                this.form.aeration_max_output = aeration_max_output;

            },
            on_av_Change(item, key) {
                let tolerance = Math.abs(item - this.page.aeration_value[key]);
                this.page.av_tolerancevalue[key] = tolerance;
                let aeration_max_value = tolerance;
                for (let i in this.page.av_tolerancevalue) {
                    if (this.page.av_tolerancevalue[i] > aeration_max_value) {
                        aeration_max_value = this.page.av_tolerancevalue[i];
                    }
                }
                this.form.aeration_max_value = aeration_max_value;
            },
            on_IOI_Change(item, key) {
                let tolerance = Math.abs(item - this.page.IOIvalue[key]);
                this.page.IOI_tolerancevalue[key] = tolerance;
                let IOI_max_output = tolerance;
                for (let i in this.page.IOI_tolerancevalue) {
                    if (this.page.IOI_tolerancevalue[i] > IOI_max_output) {
                        IOI_max_output = this.page.IOI_tolerancevalue[i];
                    }
                }
                this.form.IOI_max_output = IOI_max_output;

            },
            on_IOIv_Change(item, key) {
                let tolerance = Math.abs(item - this.page.IOI_value[key]);
                this.page.IOIv_tolerancevalue[key] = tolerance;
                let IOI_max_value = tolerance;
                for (let i in this.page.IOIv_tolerancevalue) {
                    if (this.page.IOIv_tolerancevalue[i] > IOI_max_value) {
                        IOI_max_value = this.page.IOIv_tolerancevalue[i];
                    }
                }
                console.log(IOI_max_value);
                this.form.IOI_max_value = IOI_max_value;
            },
            on_IPAP_Change(item, key) {
                let tolerance = Math.abs(item - this.page.IPAPvalue[key]);
                this.page.IPAP_tolerancevalue[key] = tolerance;
                let IPAP_max_output = tolerance;
                for (let i in this.page.IPAP_tolerancevalue) {
                    if (this.page.IPAP_tolerancevalue[i] > IPAP_max_output) {
                        IPAP_max_output = this.page.IPAP_tolerancevalue[i];
                    }
                }
                this.form.IPAP_max_output = IPAP_max_output;

            },
            on_IPAPv_Change(item, key) {
                let tolerance = Math.abs(item - this.page.IPAP_value[key]);
                this.page.IPAPv_tolerancevalue[key] = tolerance;
                let IPAP_max_value = tolerance;
                for (let i in this.page.IPAPv_tolerancevalue) {
                    if (this.page.IPAPv_tolerancevalue[i] > IPAP_max_value) {
                        IPAP_max_value = this.page.IPAPv_tolerancevalue[i];
                    }
                }
                this.form.IPAP_max_value = IPAP_max_value;
            },
            on_PEEP_Change(item, key) {
                let tolerance = Math.abs(item - this.page.PEEPvalue[key]);
                this.page.PEEP_tolerancevalue[key] = tolerance;
                let PEEP_max_output = tolerance;
                for (let i in this.page.PEEP_tolerancevalue) {
                    if (this.page.PEEP_tolerancevalue[i] > PEEP_max_output) {
                        PEEP_max_output = this.page.PEEP_tolerancevalue[i];
                    }
                }
                this.form.PEEP_max_output = PEEP_max_output;

            },
            on_PEEPv_Change(item, key) {
                let tolerance = Math.abs(item - this.page.PEEP_value[key]);
                this.page.PEEPv_tolerancevalue[key] = tolerance;
                let PEEP_max_value = tolerance;
                for (let i in this.page.PEEPv_tolerancevalue) {
                    if (this.page.PEEPv_tolerancevalue[i] > PEEP_max_value) {
                        PEEP_max_value = this.page.PEEPv_tolerancevalue[i];
                    }
                }
                this.form.PEEP_max_value = PEEP_max_value;
            },
            show_nameplate_Image(key) {
                ImagePreview({
                    images: this.page.nameplate,
                    startPosition: key,
                });
            },
            show_instrument_Image(key) {
                ImagePreview({
                    images: this.page.instrument_view,
                    startPosition: key,
                });
            },
            on_nameplate_Change(index) {
                this.page.positions = index;
            },
            on_instrument_Change(index) {
                this.page.positions = index;
            },
          beforeRead(file){
            if (file.length > this.page.max_count){
              Notify({ type: 'danger', message: `最多只能选择${this.page.max_count}个图片` });
              return  false
            }
            return true
          },
            nameplate_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                //上传图片
                let values = [];
                values.type = 'nameplate';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';

                        this.form.nameplate_fileList.pop();
                        this.form.nameplate_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                });
            },
            instrument_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                //上传图片
                let values = [];
                values.type = 'instrument_view';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';

                        this.form.instrument_fileList.pop();
                        this.form.instrument_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                });
            },
            oversize(){
                //超出限制大小
                Notify({ type: 'danger', message: '图片超出10M大小限制' });
                return false;
            },
            del_nameplate(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            del_instrument(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            changeFormType(type) {
                this.form.type = type;
                this.$refs.qualityForm.submit();
            },
            onSubmit(values) {
                if(values.lookslike == 2){
                    //外观不符合的要说明情况
                    if(!values.lookslike_desc.trim()){
                        Notify({ type: 'danger', message: '外观不符合的请说明情况' });
                        return false;
                    }
                }
                values.humidity = this.page.humidityvalue;
                for(let i = 0;i < this.page.setting.humidity.slice(0,3).length;i++){
                    if(!values.humidity[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整的潮气量数据' });
                        return false;
                    }else if (!values.humidity[i]) {
                        values.humidity[i] = "";
                    }
                }
                values.humidity_value = this.page.humidity_value;
                for(let i = 0;i < this.page.setting.humidity.slice(0,3).length;i++){
                    if(!values.humidity_value[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整的潮气量数据' });
                        return false;
                    }else if (!values.humidity_value[i]) {
                        values.humidity_value[i] = "";
                    }
                }
                values.humidity_tolerance = this.page.h_tolerancevalue;
                values.humidity_value_tolerance = this.page.hv_tolerancevalue;
                values.humidity_max_output = this.form.humidity_max_output;
                values.humidity_max_value = this.form.humidity_max_value;

                values.aeration = this.page.aerationvalue;
                for(let i = 0;i < this.page.setting.aeration.slice(0,3).length;i++){
                    if(!values.aeration[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整的呼吸比数据' });
                        return false;
                    }else if (!values.aeration[i]) {
                        values.aeration[i] = "";
                    }
                }
                values.aeration_value = this.page.aeration_value;
                for(let i = 0;i < this.page.setting.aeration.slice(0,3).length;i++){
                    if(!values.aeration_value[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整的呼吸比数据' });
                        return false;
                    }else if (!values.aeration_value[i]) {
                        values.aeration_value[i] = "";
                    }
                }
                values.aeration_setIE = this.page.setIE;
                for(let i = 0;i < this.page.setting.aeration.slice(0,3).length;i++){
                    if(!values.aeration_setIE[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整的呼吸比数据' });
                        return false;
                    }else if (!values.aeration_setIE[i]) {
                        values.aeration_setIE[i] = "";
                    }
                }
                values.aeration_tolerance = this.page.a_tolerancevalue;
                values.aeration_value_tolerance = this.page.av_tolerancevalue;
                values.aeration_max_output = this.form.aeration_max_output;
                values.aeration_max_value = this.form.aeration_max_value;

                values.IOI = this.page.IOIvalue;
                for(let i = 0;i < this.page.setting.IOI.slice(0,3).length;i++){
                    if(!values.IOI[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整的氧溶度数据' });
                        return false;
                    }else if (!values.IOI[i]) {
                        values.IOI[i] = "";
                    }
                }
                values.IOI_value = this.page.IOI_value;
                for(let i = 0;i < this.page.setting.IOI.slice(0,3).length;i++){
                    if(!values.IOI_value[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整的氧溶度数据' });
                        return false;
                    }else if (!values.IOI_value[i]) {
                        values.IOI_value[i] = "";
                    }
                }
                values.IOI_tolerance = this.page.IOI_tolerancevalue;
                values.IOI_value_tolerance = this.page.IOIv_tolerancevalue;
                values.IOI_max_output = this.form.IOI_max_output;
                values.IOI_max_value = this.form.IOI_max_value;

                values.IPAP = this.page.IPAPvalue;
                for(let i = 0;i < this.page.setting.IPAP.slice(0,3).length;i++){
                    if(!values.IPAP[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整的压力水平数据' });
                        return false;
                    }else if (!values.IPAP[i]) {
                        values.IPAP[i] = "";
                    }
                }
                values.IPAP_value = this.page.IPAP_value;
                for(let i = 0;i < this.page.setting.IPAP.slice(0,3).length;i++){
                    if(!values.IPAP_value[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整的压力水平数据' });
                        return false;
                    }else if (!values.IPAP_value[i]) {
                        values.IPAP_value[i] = "";
                    }
                }
                values.IPAP_tolerance = this.page.IPAP_tolerancevalue;
                values.IPAP_value_tolerance = this.page.IPAPv_tolerancevalue;
                values.IPAP_max_output = this.form.IPAP_max_output;
                values.IPAP_max_value = this.form.IPAP_max_value;

                values.PEEP = this.page.PEEPvalue;
                for(let i = 0;i < this.page.setting.PEEP.slice(0,3).length;i++){
                    if(!values.PEEP[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整的PEEP数据' });
                        return false;
                    }else if (!values.PEEP[i]) {
                        values.PEEP[i] = "";
                    }
                }
                values.PEEP_value = this.page.PEEP_value;
                for(let i = 0;i < this.page.setting.PEEP.slice(0,3).length;i++){
                    if(!values.PEEP_value[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整的PEEP数据' });
                        return false;
                    }else if (!values.PEEP_value[i]) {
                        values.PEEP_value[i] = "";
                    }
                }
                values.PEEP_tolerance = this.page.PEEP_tolerancevalue;
                values.PEEP_value_tolerance = this.page.PEEPv_tolerancevalue;
                values.PEEP_max_output = this.form.PEEP_max_output;
                values.PEEP_max_value = this.form.PEEP_max_value;

                values.qsid = this.$route.query.qsid;
                values.action = this.form.type;
                let _this = this;
                submit(Qs.stringify(values)).then(res => {
                    if (res.status == 1) {
                        Toast({
                            type: 'success',//失败fail
                            duration: 2000,//2秒
                            message: res.msg,
                            icon: 'success',//失败cross
                            forbidClick: true,//是否禁止背景点击，避免重复提交
                            onClose() {
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Quality/qualityDetailList');
                            }
                        });
                    }
                });
            },
            getInfo(qsid) {
                let params = {qsid: qsid};
                getInfo(params).then(response => {
                    this.info.asInfo = response.asInfo;
                    this.info.qsInfo = response.qsInfo;
                    this.page.templatename = response.templatename;
                    for (let i in response.setting) {
                        if (response.setting[i].detection_Ename == 'humidity') {
                            this.page.setting.humidity = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.h_tolerancevalue[j] = "";
                            }
                            for (let j in response.setting[i].set) {
                                this.page.hv_tolerancevalue[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'aeration') {
                            this.page.setting.aeration = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.setIE[j] = "";
                            }
                            for (let j in response.setting[i].set) {
                                this.page.a_tolerancevalue[j] = "";
                            }
                            for (let j in response.setting[i].set) {
                                this.page.av_tolerancevalue[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'IOI') {
                            this.page.setting.IOI = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.IOI_tolerancevalue[j] = "";
                            }
                            for (let j in response.setting[i].set) {
                                this.page.IOIv_tolerancevalue[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'IPAP') {
                            this.page.setting.IPAP = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.IPAP_tolerancevalue[j] = "";
                            }
                            for (let j in response.setting[i].set) {
                                this.page.IPAPv_tolerancevalue[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'PEEP') {
                            this.page.setting.PEEP = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.PEEP_tolerancevalue[j] = "";
                            }
                            for (let j in response.setting[i].set) {
                                this.page.PEEPv_tolerancevalue[j] = "";
                            }
                        }
                    }
                    if (response.detail_data != null && response.detail_data.fixed_detection) {
                        this.page.humidityvalue = response.detail_data.fixed_detection.humidity;
                        this.form.humidity_max_output = response.detail_data.fixed_detection.humidity_max_output;
                        this.form.humidity_max_value = response.detail_data.fixed_detection.humidity_max_value;
                        this.page.h_tolerancevalue = response.detail_data.fixed_detection.humidity_tolerance;
                        this.page.humidity_value = response.detail_data.fixed_detection.humidity_value;
                        this.page.hv_tolerancevalue = response.detail_data.fixed_detection.humidity_value_tolerance;

                        this.page.aerationvalue = response.detail_data.fixed_detection.aeration;
                        this.form.aeration_max_output = response.detail_data.fixed_detection.aeration_max_output;
                        this.form.aeration_max_value = response.detail_data.fixed_detection.aeration_max_value;
                        this.page.setIE = response.detail_data.fixed_detection.aeration_setIE;
                        this.page.a_tolerancevalue = response.detail_data.fixed_detection.aeration_tolerance;
                        this.page.aeration_value = response.detail_data.fixed_detection.aeration_value;
                        this.page.av_tolerancevalue = response.detail_data.fixed_detection.aeration_value_tolerance;

                        this.page.IOIvalue = response.detail_data.fixed_detection.IOI;
                        this.form.IOI_max_output = response.detail_data.fixed_detection.IOI_max_output;
                        this.form.IOI_max_value = response.detail_data.fixed_detection.IOI_max_value;
                        this.page.IOI_tolerancevalue = response.detail_data.fixed_detection.IOI_tolerance;
                        this.page.IOI_value = response.detail_data.fixed_detection.IOI_value;
                        this.page.IOIv_tolerancevalue = response.detail_data.fixed_detection.IOI_value_tolerance;

                        this.page.IPAPvalue = response.detail_data.fixed_detection.IPAP;
                        this.form.IPAP_max_output = response.detail_data.fixed_detection.IPAP_max_output;
                        this.form.IPAP_max_value = response.detail_data.fixed_detection.IPAP_max_value;
                        this.page.IPAP_tolerancevalue = response.detail_data.fixed_detection.IPAP_tolerance;
                        this.page.IPAP_value = response.detail_data.fixed_detection.IPAP_value;
                        this.page.IPAPv_tolerancevalue = response.detail_data.fixed_detection.IPAP_value_tolerance;

                        this.page.PEEPvalue = response.detail_data.fixed_detection.PEEP;
                        this.form.PEEP_max_output = response.detail_data.fixed_detection.PEEP_max_output;
                        this.form.PEEP_max_value = response.detail_data.fixed_detection.PEEP_max_value;
                        this.page.PEEP_tolerancevalue = response.detail_data.fixed_detection.PEEP_tolerance;
                        this.page.PEEP_value = response.detail_data.fixed_detection.PEEP_value;
                        this.page.PEEPv_tolerancevalue = response.detail_data.fixed_detection.PEEP_value_tolerance;

                        this.form.capacity_precut_mode = response.detail_data.fixed_detection.capacity_precut_mode;
                        this.form.traffic_trigger = response.detail_data.fixed_detection.traffic_trigger;
                        this.form.pressure_precut_mode = response.detail_data.fixed_detection.pressure_precut_mode;
                        this.form.pressure_tigger = response.detail_data.fixed_detection.pressure_tigger;
                        this.form.power_supply_alarm = response.detail_data.fixed_detection.power_supply_alarm;
                        this.form.oxygen_concentration_bound = response.detail_data.fixed_detection.oxygen_concentration_bound;
                        this.form.gas_supply_alarm = response.detail_data.fixed_detection.gas_supply_alarm;
                        this.form.apnea_alarm = response.detail_data.fixed_detection.apnea_alarm;
                        this.form.AWP_alarm = response.detail_data.fixed_detection.AWP_alarm;
                        this.form.loop_overvoltage_pretection = response.detail_data.fixed_detection.loop_overvoltage_pretection;
                        this.form.minute_ventilation_bound = response.detail_data.fixed_detection.minute_ventilation_bound;
                        this.form.press_key = response.detail_data.fixed_detection.press_key;


                        this.form.lookslike = response.detail_data.fixed_detection.lookslike;
                        if(response.detail_data.fixed_detection.lookslike == 2){
                            this.show_lookslike_desc = true;
                            this.form.lookslike_desc = response.detail_data.fixed_detection.lookslike_desc;
                        }
                        this.form.internal_discharge = response.detail_data.fixed_detection.internal_discharge;
                        this.form.charge = response.detail_data.fixed_detection.charge;
                        this.form.total_desc = response.detail_data.fixed_detection.total_desc;
                    }
                    if (response.file_data) {
                        for (let i in response.file_data.nameplate) {
                            this.form.nameplate_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.nameplate[i].file_url,
                                id: response.file_data.nameplate[i].file_id
                            });
                        }
                        for (let i in response.file_data.instrument_view) {
                            this.form.instrument_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.instrument_view[i].file_url,
                                id: response.file_data.instrument_view[i].file_id
                            });
                        }

                    }
                    this.page.humidity = this.page.humidity + response.tolerance.humidity;
                    this.page.aeration = this.page.aeration + response.tolerance.aeration;
                    this.page.IOI = this.page.IOI + response.tolerance.IOI;
                    this.page.IPAP = this.page.IPAP + response.tolerance.IPAP;
                    this.page.PEEP = this.page.PEEP + response.tolerance.PEEP;
                    this.page.is_display = 1;
                });
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.qsid);
        }
    }
</script>
<style scoped lang="scss">
    .background {
        background: rgba(69, 90, 100, 0.1);
    }

    ::v-deep .text_length .van-cell__title {
        width: 95px
    }
    .card-header{margin-top: 20px;}
    .mp{
        margin-top: 20px;
        padding-top: 10px;
        background: #fff;
    }
    .row_center{text-align: center;}
    .fenge{width: 94%;margin: 8px auto;}
</style>
