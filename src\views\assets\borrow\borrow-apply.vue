<template>
    <div class="con_detail" v-if="page.is_display===1">
        <div class="card-header">
            <h2 class="detailTitle">设备基本信息</h2>
            <div class="bl1px"></div>
        </div>
        <AssetsInfo :info='info'/>

        <van-tab title="借调审批信息" v-if="info.approves!==0&&info.approves!==undefined">
            <Approves :info='info.approves'/>
        </van-tab>
        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">借调申请表单</h2>
        </div>
        <van-form ref="borrowForm" @submit="onSubmit">
            <van-cell-group>
                <div v-if="page.subsidiary.length>0">
                    <van-row type="flex" justify="center">
                    <van-col span="20">勾选连同所属附属设备一起借调</van-col>
                    </van-row>
                    <van-row v-for="item in page.subsidiary" :key="item.assid">
                    <van-checkbox-group v-model="page.result" ref="checkboxGroup">
                      <van-col offset="2" span="14" ><van-checkbox :name="item.assid" >{{item.assets}}</van-checkbox></van-col>
                      <van-col  span="8">{{item.model}}</van-col>
                  </van-checkbox-group>
                     </van-row>
                </div>
                <van-field label="流水号" :value="page.flowNumber" readonly/>
                <van-field
                        readonly
                        clickable
                        name="department"
                        :value="form.depart"
                        label="申请科室"
                        placeholder="点击选择申请科室"
                        v-if="page.departments.length>0"
                        :rules="[{ required: true, message: '请选择申请科室',trigger:'onChange' }]"
                        @click="page.showSelectDepartment = true"
                />
                <van-popup v-model="page.showSelectDepartment" position="bottom">
                    <van-field v-model="page.departmentKeyword" label="搜索" placeholder="请输入科室名称"/>
                    <van-picker
                            show-toolbar
                            :columns="this.departmentFilter"
                            @confirm="onConfirmDepartment"
                            @cancel="page.showSelectDepartment = false"
                    />
                </van-popup>
                <van-field name="estimate_back" label="归还时间" required clickable :rules="[{ required: true, message: '请选择时间',trigger:'onChange' }]" :value="form.estimate_back" placeholder="点击选择时间"
                           @click="page.showSelectTransfer = true"/>
                <van-popup v-model="page.showSelectTransfer" position="bottom">
                    <van-datetime-picker
                            v-model="page.currentDate"
                            type="datetime"
                            title="选择时间"
                            @confirm="onConfirmBorrowDate"
                            @cancel="page.showSelectTransfer = false"
                    />
                </van-popup>
                <van-field
                        v-model="form.borrow_reason"
                        name="borrow_reason"
                        rows="1"
                        autosize
                        required
                        label="借调原因"
                        type="textarea"
                        placeholder="借调原因"
                        :rules="[{ validator, message: '请填写借调原因' }]"
                />
                <van-field label="申请人" :value="page.username" readonly/>
                <van-field label="申请时间" :value="page.borrow_in_time" readonly/>
            </van-cell-group>
            <div style="margin: 16px;">
                <van-button square color="#009688" block type="warning" native-type="button" @click="changeFormType('edit')" v-show="form.borid>0">申请重审</van-button>
                <van-button square  block type="danger" native-type="button" @click="changeFormType('end')" v-show="form.borid >0">结束进程</van-button>
                <van-button square color="#009688" block type="info" native-type="button" @click="changeFormType('apply')" v-show="form.borid===undefined">确认并提交</van-button>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Divider, Cell, CellGroup, Icon, Field, Form, Button, Tab, Tabs, Toast, Loading, Popup, Picker, DatetimePicker,Checkbox, CheckboxGroup,Col, Row} from 'vant';
    import {getInfo, submit} from "@/api/assets/borrow/apply";
    import AssetsInfo from "@/components/Assetsinfo";
    import Approves from '@/components/Approves';

    export default {
        name: 'applyScrap',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Loading.name]: Loading,
            [Popup.name]: Popup,
            [Col.name]: Col,
            [Row.name]: Row,
            [Checkbox.name]: Checkbox,
            [CheckboxGroup.name]: CheckboxGroup,
            [Picker.name]: Picker,
            [DatetimePicker.name]: DatetimePicker,
            AssetsInfo,
            Approves,
        },
        data() {
            return {
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    approves: [],//审批记录
                },
                page: {
                    departments: [],//科室列表
                    departmentColumns: [],//科室选项
                    departmentKeyword: '',//科室搜索栏
                    show_form: 1,//判断是修改订单还是新增订单
                    is_display: 0,//用于判断是否加载完数据
                    formType: 'apply',//apply 申请 edit 重审 end 结束进程
                    username: '',//申请人
                    currentDate: new Date(),//当前日期
                    borrow_in_time: '',//申请时间
                    showSelectDepartment: false,//科室控件显示
                    showSelectTransfer: false,//时间控件显示
                    flowNumber: '',//流水号
                    show_department:false,//非超级管理不显示
                    apply_borrow_back_start_time:'',
                    apply_borrow_back_end_time:'',
                    subsidiary:[],
                    result:[],
                },
                form: {
                    borid: 0,//借调id新增不存在修改存在
                    borrow_reason: '',//借调原因
                    estimate_back: '',//归还时间
                    departmentid: '',//选择科室
                },
            }
        },
        computed: {
            departmentFilter() {
                return this.page.departmentColumns.filter(val => {
                    return val.indexOf(this.page.departmentKeyword.trim()) >= 0
                })
            }
        },
        methods: {
            //选择归还时间
            onConfirmBorrowDate(time) {
                let estimate_back = this.timeFormat(time)
                if (!estimate_back) {
                    Toast.fail('归还时间范围 ' + this.page.apply_borrow_back_start_time + ' 至 ' + this.page.apply_borrow_back_end_time);
                    return false;
                }
                this.form.estimate_back = estimate_back;
                this.page.showSelectTransfer = false;
            },
            //选择科室选项
            onConfirmDepartment(value) {
                this.form.depart = value;
                this.page.departments.forEach(val => {
                    if (val.department === value) {
                        this.form.departmentid=val.departid;
                    }
                });
                this.page.showSelectDepartment = false;
            },
            validator(val) {
                if (this.form.borid > 0) {
                    return true;
                } else {
                    if (typeof val === 'undefined' || val.replace(/(^\s*)|(\s*$)/g, "") === '') {
                        this.form.borrow_reason = '';
                        return false;
                    }
                }
            },
            getInfo(assid, borid,assnum) {
                let params = {assid: assid, borid: borid,assnum:assnum};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.info.approves = response.approve;
                    if (typeof response.data !== 'undefined') {
                        this.form.borid = response.data.borid;
                        this.form.departmentid = response.data.apply_departid;
                        this.form.depart = response.depart;
                        this.form.estimate_back = response.data.estimate_back;
                        this.form.borrow_reason = response.data.borrow_reason;
                    } else {
                        this.form.borid = undefined;
                    }
                    if (response.departname) {
                        this.page.departments = response.departname;
                        this.page.departmentColumns = response.departmentColumns;
                        this.page.show_department = true;
                    }
                    for(let i in response.subsidiary){
                        this.page.result.push(response.subsidiary[i].assid);
                    }
                    this.page.subsidiary = response.subsidiary;
                    this.page.apply_borrow_back_start_time = response.apply_borrow_back_start_time;
                    this.page.apply_borrow_back_end_time = response.apply_borrow_back_end_time;
                    this.page.borrow_in_time = response.borrow_in_time;
                    this.page.username = response.username;
                    this.page.flowNumber = response.flowNumber;
                    this.page.is_display = 1;
                })
            },
            onSubmit(values) {
                values.subsidiary_assid = "";
                for(let i in this.page.result){
                      values.subsidiary_assid+= this.page.result[i]+',';
                }
                if (this.page.result.length>0) {
                values.subsidiary_assid = values.subsidiary_assid.substring(0, values.subsidiary_assid.length - 1);
                }
                values.apply_departid = this.form.departmentid;
                switch (this.formType) {
                    case "apply":
                        //报废申请
                        values.assid = this.info.assid;
                        values.scrapnum = this.info.scrapnum;
                        values.type = this.formType;
                        break;
                    case "edit":
                        //报废重审
                        values.borid = this.form.borid;
                        values.type = this.formType;
                        break;
                    case "end":
                        //报废结束进程
                        values.borid = this.form.borid;
                        values.type = this.formType;
                        break;
                }
                let _this = this;
                //发送请求
                submit(Qs.stringify(values)).then(response => {
                    if (response.status==1) {
                        Toast({
                            type:'success',//失败fail
                            duration:2000,//2秒
                            message: response.msg,
                            icon:'success',//失败cross
                            forbidClick:true,//是否禁止背景点击，避免重复提交
                            onClose(){
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Borrow/borrowAssetsList');
                            }
                        });
                }
                    //this.$router.push('/');
                })
            },
            changeFormType(type) {
                this.formType = type;//改变提交表单的formType
                this.$refs.borrowForm.submit();//执行提交
            },
            timeFormat(time) { // 时间格式化
                let year = time.getFullYear();
                let month = time.getMonth() + 1;
                let day = time.getDate();
                let hour = time.getHours();
                let minute = time.getMinutes();
                let back_start = this.page.apply_borrow_back_start_time.split(':');
                let back_end = this.page.apply_borrow_back_end_time.split(':');
                if (back_start[0] <= hour && hour <= back_end[0]) {
                    if (back_start[0]==hour&&(minute<back_start[1])) {
                        return false;
                    }
                    if (back_end[0]==hour&&(minute>back_end[1])) {
                        return false;
                    }
                }else{
                    return false;
                }
                return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.assid, this.$route.query.borid,this.$route.query.assnum);
        }
    }
</script>

<style scoped lang="scss">
    .van-button {
        margin-top: 10px;
    }
</style>
