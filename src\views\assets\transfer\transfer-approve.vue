<template>
    <div class="con_detail" v-if="page.is_display===1">
        <van-tabs :border="false">
            <van-tab title="设备基本信息">
                <Assetsinfo :info='info'/>
            </van-tab>
            <van-tab title="设备转科申请单信息" v-if="info.transfer!=[]&&info.transfer!=undefined">
                 <van-cell-group  >
                    <van-cell title="转科单号" :value="info.transfer.transfernum"/>
                    <van-cell title="申请人" :value="info.transfer.applicant_user"/>
                    <van-cell title="申请时间" :value="info.transfer.transfer_date"/>
                    <van-cell title="转出科室" :value="info.transfer.tranout_depart_name"/>
                    <van-cell title="转入科室" :value="info.transfer.tranin_depart_name"/>
                    <van-cell title="转科原因" :value="info.transfer.tran_reason"/>
                </van-cell-group>
            </van-tab>
        </van-tabs>

        <div style="margin-bottom: 20px;">
            <div class="card-header">
                <h2 class="detailTitle">审批记录</h2>
                <div class="bl1px"></div>
            </div>
            <Approves :info='info.approves'/>
        </div>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">审批表单</h2>
        </div>
        <addApprove :info="page" @values="onSubmit" v-if="page.show_form==1" />
        <div class="cus_loading" v-show="show_loading"><van-loading type="spinner" color="#1989fa" /></div>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px" />
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Divider, Cell, CellGroup, Icon, Field,Toast, Form, Button, Tab, Tabs,Popup,Picker,DatetimePicker,RadioGroup, Radio,Loading} from 'vant';
    import {getInfo,submit} from "@/api/assets/transfer/approve";
    import Assetsinfo from "@/components/Assetsinfo";
    import Approves from "@/components/Approves";
    import addApprove from "@/components/addApprove";

    export default {
        name: 'approve',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Loading.name]: Loading,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [DatetimePicker.name]: DatetimePicker,
            Assetsinfo,
            Approves,
            addApprove,
        },
        data() {
            return {
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    approves: [],//审批记录
                    transfer: [],
                },
                page:{
                    approveDate:'',
                    approveUser:'',
                    is_display:0,
                    all_approver:'',
                    show_form:1,
                    active:'',
                },
                form:{
                    is_adopt:'1',
                    approveRemark:'',
                },
                show_loading:0
            }
        },
        methods: {
            onSubmit(values) {
                let _this = this;
                _this.show_loading = 1;

                values.atid = this.$route.query.atid;
                values.examine = values.approveRemark;
                values.res = values.is_adopt;
                values.transnum = this.info.transfer.transfernum;
                values.assid = this.info.assid;
                submit(Qs.stringify(values)).then(response => {
                    if (response.status==1) {
                    _this.show_loading = 0;
                    Toast({
                        type:'success',//失败fail
                        duration:2000,//2秒
                        message: response.msg,
                        icon:'success',//失败cross
                        forbidClick:true,//是否禁止背景点击，避免重复提交
                        onClose(){
                            //关闭后跳转到审批列表页
                            _this.$router.push(this.$store.getters.moduleName+'/Notin/approve');
                        }
                    });}
                });
            },
            getInfo(atid) {
                let params = {atid: atid};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.info.transfer = response.transfer;
                    this.info.approves = response.approves;
                    this.page.approveDate = response.approveDate;
                    this.page.approveUser = response.approveUser;
                    this.page.all_approver = response.transfer.all_approver;
                    this.page.active = response.transfer.app_user_num;
                    this.page.show_form = response.is_display;
                    this.page.is_display = 1;
                });
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.atid);
        }
    }
</script>
<style scoped lang="scss">
    .card-header {
        margin-top: 20px;
    }

    .app_pro {
        background: #fff;
    }
</style>
