<template>
  <div class="list">
    <div class="bgcf" style="margin-top: 20px;">
      <van-button color="#009688" block class="scanQrcode" size="normal" @click="qrCode" v-if="jugdePlatform()">扫码查询</van-button>
      <van-search
          v-model="keyword"
          shape="round"
          placeholder="搜索(计划名称)"
          @search="onSearch"
      />
    </div>
    <!--      扫码后选择设备的弹窗-->
    <van-dialog v-model="showModal" :title="showModalTitle" showCancelButton cancelButtonColor="red" overlay closeOnClickOverlay :showConfirmButton="false">
      <van-cell
          v-for="item in selectAssetsList"
          :title="item.patrol_name"
          is-link
          :to="{ path: $store.getters.moduleName+'/Patrol/tasksList', query: {patrid: item.patrid,operation:item.operation}}"
      />
    </van-dialog>
    <van-row>
      <van-col span="18">
        <div class="bgcf total-div">
          <div class="total-div-title">
            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
            巡查计划查询列表 <span class="num"> {{total}} </span> 个
          </div>
        </div>
      </van-col>
      <van-col span="6">
        <Sort @order="order" :option="option"/>
      </van-col>
    </van-row>
    <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="refreshList"
    >
      <van-card v-for="item in list" :key="item.assid">
        <template #desc>
          <ul>
            <li>
              <span class="detailText">计划名称：</span>
              <span class="text">{{item.patrol_name}}</span>
            </li>
            <li>
              <span class="detailText">计划级别：</span>
              <span class="text">{{item.patrol_level_name}}</span>
            </li>
            <li>
              <span class="detailText">计划日期：</span>
              <span class="text">{{item.patrol_date}}</span>
            </li>
            <li>
              <span class="detailText">计划状态：</span>
              <span class="text" v-html="item.patrol_status_name"></span>
            </li>
            <li>
              <span class="detailText">周期计划：</span>
              <span class="text" v-if="item.is_cycle ==1" style="color: red;">{{item.cycle_name}}</span>
              <span class="text" v-else>{{item.cycle_name}}</span>
            </li>
            <li v-if="item.is_cycle == 1">
              <span class="detailText">周期设置：</span>
              <span class="text">{{item.cycle_setting_name}}</span>
            </li>
            <li  v-if="item.is_cycle == 1">
              <span class="detailText">当前/总周期：</span>
              <span class="text">{{item.current_cycle}} / {{item.total_cycle}}</span>
            </li>
          </ul>
        </template>
        <template #footer>
          <van-button v-if="item.operation"
                      :type="item.hasOwnProperty('button_type') ? item.button_type : 'info'"
                      block
                      size="small"
                      class="detail-button"
                      :to="{ path: $store.getters.moduleName+'/Patrol/tasksList', query: {patrid: item.patrid,operation:item.operation}}">
            {{item.operation_name}}
          </van-button>
        </template>
      </van-card>
    </van-list>
    <ScrollTop/>
  </div>
</template>

<script>
import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem, Notify,Dialog} from "vant";
import ScrollTop from "@/components/ScrollTop";
import Sort from "@/components/Sort";
import {getPatrolList} from "@/api/patrol/list";
import wechatUtil from "@/utils/wechatUtil";
import feishuUtil from "@/utils/feishuUtil";
import {getInfo} from "@/api/patrol/operation";
import {getRegexAssnum} from "@/utils/regex";

export default {
  name: 'getAssetsList',
  components: {
    [List.name]: List,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Button.name]: Button,
    [Card.name]: Card,
    [Search.name]: Search,
    [Col.name]: Col,
    [Row.name]: Row,
    [Icon.name]: Icon,
    [Icon.name]: Icon,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    [Dialog.Component.name]: Dialog.Component,
    ScrollTop,
    Sort
  },
  data() {
    return {
      list: [],
      option: [
        {text: '按计划编号（降序）', value: 'patrolnum-desc'},
        {text: '按计划执行日期（降序）', value: 'executedate-desc'},
        {text: '按预计完成日期（降序）', value: 'expect_complete_date-desc'},
        {text: '按计划编号（升序）', value: 'patrolnum-asc'},
        {text: '按计划执行日期（升序）', value: 'executedate-asc'},
        {text: '按预计完成日期（升序）', value: 'expect_complete_date-asc'},
      ],
      keyword: '',
      loading: false,
      finished: false,
      total: 0,
      //全局列表搜索条件
      where: {
        page: 1
      },
      //扫码后选择设备
      selectAssetsList: [],
      showModal: false,
      showModalTitle: '',
      selectIndex: 0,
      selectAssnum: ''
    };
  },
  methods: {
    jugdePlatform(){
      return (process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX)
    },
    //刷新列表 第一次进入自动触发
    refreshList() {
      // 异步更新数据
      setTimeout(() => {
        getPatrolList(this.where).then(response => {
          //先判断如果返回的总数为0 则把状态设置为停止加载
          if (response.total === 0) {
            this.finished = true;
          }
          //判断数据键值是否存在
          if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
            this.list.push(...response.rows);
          }
          //仅第一页赋值总数
          if (this.where.page === 1) {
            this.total = response.total;
          }
          //数据获取到了 停止加载
          this.loading = false;
          //全部加载了 且当前列表数量大于总数 设置完成状态
          if (this.list.length >= this.total) {
            this.finished = true;
          }
          //页数加1
          this.where.page++;
        });
      }, 100);
    },
    onSearch(keyword) {
      this.resetWhere();
      this.finished = false;
      //重新加搜索条件
      this.where.search = keyword;
      this.refreshList();
    },
    order(value) {
      this.resetWhere(false);
      //重新加搜索条件
      this.where.page = 1;
      this.where.sort = value.split('-')[0];
      this.where.order = value.split('-')[1];
      this.refreshList();
    },
    getcatid(id) {
      this.resetWhere();
      //重新加搜索条件
      this.where.catid = id;
      this.refreshList();
    },
    getdepartid(id) {
      this.resetWhere();
      //重新加搜索条件
      this.where.departid = id;
      this.refreshList();
    },
    //重置列表条件
    resetWhere(clearWhere = true) {
      this.list = [];//清空列表
      if (clearWhere) {
        //清空搜索条件
        this.where = {};
      }
      //重置为第一页
      this.where.page = 1;
      //重置表格为加载状态
      this.loading = true;
      this.finished = false;
    },
    detail(assid) {
      this.$router.push({
        name: 'showAssets',
        query: {
          assid: assid
        }
      });
    },
    async qrCode() {
      var _this = this;
      switch (parseInt(process.env.VUE_APP_VERSION)) {
        case 1:
          // 微信版本
          wechatUtil.init([
            'scanQRCode',//扫一扫
          ]).then((wx) => {
            // 这里写微信的接口
            wx.scanQRCode({
              needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
              scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
              async success(res) {
                // var assnum = res.resultStr;// 当needResult 为 1 时，扫码返回的结果
                // if (assnum.indexOf("ODE_") > 0) {
                //   assnum = res.resultStr.substr(9);
                // }
                const assnum = await getRegexAssnum(res.resultStr)

                //鉴别设备是否存在
                // let currentInfo  = []
                // currentInfo = _this.list.filter(v => v.patrol_assnums.indexOf(assnum) !== -1);
                // if (currentInfo.length === 0) {
                //   currentInfo = _this.list.filter(v => v.patrol_assorignum.indexOf(assnum) !== -1);
                //   if (currentInfo.length === 0) {
                //     currentInfo = _this.list.filter(v => v.patrol_assorignum_spare.indexOf(assnum) !== -1);
                //     if (currentInfo.length === 0) {
                //       Notify({type: 'danger', message: '该设备暂无相关巡查保养计划'});
                //       return false;
                //     }
                //   }
                // }
                let properties = ['patrol_assnums', 'patrol_assorignum', 'patrol_assorignum_spare'];

                let findInProperty = (property) => {
                  let currentInfo = _this.list.filter(v => v[property].indexOf(assnum) !== -1);
                  return currentInfo.length > 0 ? currentInfo : null;
                }

                let currentInfo;
                for (let property of properties) {
                  currentInfo = findInProperty(property);
                  if (currentInfo) break;
                }

                if (!currentInfo) {
                  Notify({type: 'danger', message: '该设备暂无相关巡查保养计划'});
                  return false;
                }

                /*弹窗赋值 S*/
                _this.selectAssetsList = currentInfo;
                _this.showModal = true;
                _this.showModalTitle = '有' + currentInfo.length + '个巡查计划包含该设备，请选择查看';
                _this.selectAssnum = assnum
                /*弹窗赋值 E*/
              }
            });
          })
          break;
        case 2:
          // 飞书版本
          await feishuUtil.init(['scanCode']);
          window.h5sdk.ready(() => {
            window.tt.scanCode({
              success(res) {
                // alert(JSON.stringify(`${res.result}`));
                //鉴别设备是否存在
                // let currentInfo = _this.list.filter(v => v.patrol_assnums.indexOf(res.result) !== -1);
                // if (currentInfo.length === 0) {
                //   Notify({type: 'danger', message: '该设备暂无相关巡查保养计划'});
                //   return false;
                // }
                let properties = ['patrol_assnums', 'patrol_assorignum', 'patrol_assorignum_spare'];

                let findInProperty = (property) => {
                  let currentInfo = _this.list.filter(v => v[property].indexOf(assnum) !== -1);
                  return currentInfo.length > 0 ? currentInfo : null;
                }

                let currentInfo;
                for (let property of properties) {
                  currentInfo = findInProperty(property);
                  if (currentInfo) break;
                }

                if (!currentInfo) {
                  Notify({type: 'danger', message: '该设备暂无相关巡查保养计划'});
                  return false;
                }

                /*弹窗赋值 S*/
                _this.selectAssetsList = currentInfo;
                _this.showModal = true;
                _this.showModalTitle = '有' + currentInfo.length + '个巡查计划包含该设备，请选择查看';
                _this.selectAssnum = res.result
                /*弹窗赋值 E*/
              },
              fail(res) {
                alert(`调用失败`);
              }
            });
          });
          break;
      }
    },
  },
}
</script>

<style scoped lang="scss">
.list {
  margin-top: 0;

  .van-list .van-card {
    li {
      display: flex;
    }

    .detailText {
      width: 6rem;
    }

    .text {
      flex: 1;
    }
  }

  .van-search {
    margin-bottom: 0;
  }
}

.my-swipe {
  ::v-deep .van-swipe-item ul {
    margin: 30px;
  }
  ::v-deep .van-swipe-item ul li span{
    color:red;
  }

  ::v-deep .van-swipe__indicator {
    background-color: red;
  }
}

::v-deep .my-swipe{
  margin: 10px;
}
::v-deep .my-swipe li{
  margin-left: 20px;
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>
<style>
.van-grid-item__content {
  padding: 6px;
}
</style>