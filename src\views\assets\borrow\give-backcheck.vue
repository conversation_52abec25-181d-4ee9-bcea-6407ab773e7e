<template>
    <div class="con_detail">
        <van-tabs :border="false">
            <van-tab title="设备基本信息">
                <AssetsInfo :info='info'/>
            </van-tab>
            <van-tab title="借调申请信息">
                <van-cell-group>
                    <van-cell title="流水号" :value="info.borrowdata.borrow_num"/>
                    <van-cell title="申请科室" :value="info.borrowdata.department"/>
                    <van-cell title="申请人" :value="info.borrowdata.apply_username"/>
                    <van-cell title="申请时间" :value="info.borrowdata.apply_time"/>
                    <van-cell title="借调原因" :value="info.borrowdata.borrow_reason"/>
                    <van-cell title="预计归还时间" :value="info.borrowdata.estimate_back"/>
                    <van-cell title="借出时间" :value="info.borrowdata.borrow_in_time"/>
                </van-cell-group>
            </van-tab>
            <van-tab title="借调审批信息">
                <Approves :info='info.approves'/>
            </van-tab>
        </van-tabs>
        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">借调归还验收表单</h2>
        </div>
        <van-form ref="borrowForm" @submit="onSubmit"  v-if="page.is_display">
            <van-cell-group title="一同借调附属设备"  v-if="page.subsidiary.length>0">
              <van-cell v-for="item in page.subsidiary" :key="item.assid" :title="item.assets" :value="item.model" />
          </van-cell-group>
            <van-field name="give_back_time" label="确定归还时间" required :value="form.give_back_time" placeholder="点击选择时间"
                       @click="page.showSelectTransfer = true"
                       :rules="[{ required: true, message: '请选择时间',trigger:'onChange' }]"/>
            <van-popup v-model="page.showSelectTransfer" position="bottom">
                <van-datetime-picker
                        v-model="page.currentDate"
                        type="datetime"
                        title="选择时间"
                        @confirm="onConfirmTransferDate"
                        @cancel="page.showSelectTransfer = false"

                />
            </van-popup>
            <van-field
                    v-model="form.borrow_reason"
                    name="borrow_reason"
                    rows="1"
                    autosize
                    label="补充备注"
                    type="textarea"
                    placeholder="补充备注"
            />
            <div style="padding: 16px;background: #fff;">
                <van-button color="#009688" block type="warning" native-type="submit">确认设备完好无损并结束流程</van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Divider, Cell, CellGroup, Icon, Field, Toast, Form, Button, Tab, Tabs, Popup, Picker, DatetimePicker} from 'vant';
    import {getInfo, submit} from "@/api/assets/borrow/show";
    import AssetsInfo from "@/components/Assetsinfo";
    import Approves from '@/components/Approves';

    export default {
        name: 'applyScrap',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [DatetimePicker.name]: DatetimePicker,
            AssetsInfo,
            Approves,
        },
        data() {
            return {
                scrap_reason: '',
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    approves: [],
                    borrowdata: [],
                },
                page: {
                    currentDate: new Date(),//当前日期
                    showSelectTransfer: false,//时间控件显示
                    is_display:true,
                    subsidiary:[],
                    apply_borrow_back_start_time:'',
                    apply_borrow_back_end_time:'',
                },
                form: {
                    give_back_time: '',
                    borrow_reason: '',
                }
            }
        },
        methods: {
            onConfirmTransferDate(time) {
                let give_back_time = this.timeFormat(time);
                if (!give_back_time) {
                    Toast.fail('归还时间范围 ' + this.page.apply_borrow_back_start_time + ' 至 ' + this.page.apply_borrow_back_end_time);
                    return false;
                }
                this.form.give_back_time = give_back_time;
                this.page.showSelectTransfer = false;
            },
            onSubmit(values) {
                values.borid = this.$route.query.borid;
                let _this = this;
                //发送请求
                submit(Qs.stringify(values)).then(response => {
                    if (response.status==1) {
                     Toast({
                            type:'success',//失败fail
                            duration:2000,//2秒
                            message: response.msg,
                            icon:'success',//失败cross
                            forbidClick:true,//是否禁止背景点击，避免重复提交
                            onClose(){
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Borrow/giveBackCheckList');
                            }
                        });

                 }
                    //this.$router.push('/');
                })
            },
            timeFormat(time) { // 时间格式化
                let year = time.getFullYear();
                let month = time.getMonth() + 1;
                let day = time.getDate();
                let hour = time.getHours();
                let minute = time.getMinutes();
                let back_start = this.page.apply_borrow_back_start_time.split(':');
                let back_end = this.page.apply_borrow_back_end_time.split(':');
                if (back_start[0] <= hour && hour <= back_end[0]) {
                    if (back_start[0]==hour&&(minute<back_start[1])) {
                        return false;
                    }
                    if (back_end[0]==hour&&(minute>back_end[1])) {
                        return false;
                    }
                }else{
                    return false;
                }
                return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
            },
            getInfo(borid) {
                let params = {borid: borid};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.info.approves = response.approve;
                    this.info.borrowdata = response.borrow;
                    this.page.is_display = response.is_display;
                    this.page.subsidiary = response.subsidiary;
                    this.page.apply_borrow_back_start_time = response.apply_borrow_back_start_time;
                    this.page.apply_borrow_back_end_time = response.apply_borrow_back_end_time;
                });
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.borid);
        }
    }
</script>
<style scoped lang="scss">
    .card-header{margin-top: 20px;}
</style>
