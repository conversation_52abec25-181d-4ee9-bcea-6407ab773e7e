<template>
    <div class="list">
        <div v-if="show.show_repair==1">
            <van-row>
                <van-col span="18" @click="is_display.repair=is_display.repair?false:true;">
                    <div class="bgcf total-div">
                        <div class="total-div-title" id="repair">
                            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                            维修待验收 <span class="num"> {{total.repair}} </span>台
                            <span class="tips" v-show="tips.repair.up" @click="show_hidden('repair')">收起<van-icon name="arrow-up" /></span>
                            <span class="tips" v-show="tips.repair.down" @click="show_hidden('repair')">展开<van-icon name="arrow-down" /></span>
                        </div>
                    </div>

                </van-col>
                <van-col span="6">
                    <Sort @order="repair_order" :option="option.repair"/>
                </van-col>
            </van-row>
            <van-list
                    v-model="loading.repair"
                    v-show="is_display.repair"
                    :finished="finished.repair"
                    @load="repairList"
            >
                <van-card v-for="item in list.repair_list" :key="item.assid">
                    <template #desc>
                    <div class="jumpButton">
                        <van-button color="#009688" size="small" :to="{ path: $store.getters.moduleName+'/Repair/checkRepair', query: { repid: item.repid }}">验收</van-button>
                    </div>
                    <van-row>
                        <van-col span="6">
                            <van-grid column-num="1" center :border="false" icon-size="3.2rem">
                            <van-grid-item :icon="item.headimgurl" :text="item.appUser" />
                            </van-grid>
                        </van-col>
                        <van-col span="18">
                            <van-cell :border="false" :value="item.repnum" />
                            <van-cell :border="false" :value="item.assets" />
                            <van-cell :border="false" :title="item.department" :value="item.appTime" />
                        </van-col>
                    </van-row>
                </template>
                </van-card>
            </van-list>
            <van-pagination v-model="page.repair" :total-items="total.repair" :items-per-page="5" v-show="is_display.repair&&total.repair>5" @change="repair_change"/>
            <van-divider/>
            <van-divider/>
        </div>

        <div v-if="show.show_transfer==1">
            <van-row>
                <van-col span="18" @click="is_display.transfer=is_display.transfer?false:true;">
                    <div class="bgcf total-div">
                        <div class="total-div-title" id="transfer">
                            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                            转科待验收 <span class="num"> {{total.transfer}} </span>台
                            <span class="tips" v-show="tips.transfer.up" @click="show_hidden('transfer')">收起<van-icon name="arrow-up" /></span>
                            <span class="tips" v-show="tips.transfer.down" @click="show_hidden('transfer')">展开<van-icon name="arrow-down" /></span>
                        </div>
                    </div>
                </van-col>
                <van-col span="6">
                    <Sort @order="transfer_order" :option="option.transfer"/>
                </van-col>
            </van-row>
            <van-list
                    v-model="loading.transfer"
                    v-show="is_display.transfer"
                    :finished="finished.transfer"
                    @load="transferList"
            >
                <van-card v-for="item in list.transfer_list" :key="item.assid">
                    <template #desc>
                    <ul>
                        <li>
                            <span class="detailText">设备名称：</span>
                            <span class="text">{{item.assets}}</span>
                        </li>
                        <li>
                            <span class="detailText">转科单号：</span>
                            <span class="text">{{item.transfernum}}</span>
                        </li>
                        <li>
                            <span class="detailText">规格型号：</span>
                            <span class="text">{{item.model}}</span>
                        </li>
                        <li>
                            <span class="detailText">设备编号：</span>
                            <span class="text">{{item.assnum}}</span>
                        </li>
                        <li>
                            <span class="detailText">转出科室：</span>
                            <span v-html="item.tranout_depart_name"/>
                        </li>
                        <li>
                            <span class="detailText">转入科室：</span>
                            <span v-html="item.tranin_depart_name"/>
                        </li>
                        <li>
                            <span class="detailText">进程状态：</span>
                            <span v-html="item.show_status_name"/>
                        </li>
                    </ul>
                </template>
                <template #footer>
                    <van-button type="info" block size="small" class="detail-button" :to="{ path: $store.getters.moduleName+'/Transfer/check', query: { atid: item.atid }}">验收</van-button>
                </template>
                </van-card>
            </van-list>
            <van-divider/>
        </div>

         <div v-if="show.show_in_borrow==1">
            <van-row>
                <van-col span="18" @click="is_display.in_borrow=is_display.in_borrow?false:true;">
                    <div class="bgcf total-div">
                        <div class="total-div-title"  id="in_borrow">
                            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                            借入验收 <span class="num"> {{total.in_borrow}} </span>台
                            <span class="tips" v-show="tips.in_borrow.up" @click="show_hidden('in_borrow')">收起<van-icon name="arrow-up" /></span>
                            <span class="tips" v-show="tips.in_borrow.down" @click="show_hidden('in_borrow')">展开<van-icon name="arrow-down" /></span>
                        </div>
                    </div>
                </van-col>
                <van-col span="6">
                    <Sort @order="borrow_in_order" :option="option.in_borrow"/>
                </van-col>
            </van-row>
            <van-list
                    v-model="loading.in_borrow"
                    v-show="is_display.in_borrow"
                    :finished="finished.in_borrow"
                    @load="borrowinList"
            >
                <van-card v-for="item in list.borrow_in_list" :key="item.assid" @click="tourl(item.borid)">
                    <template #title>
                    <van-cell>
                        <!-- 使用 title 插槽来自定义标题 -->
                        <template #title>
                            <span class="custom-title">借调单号：{{item.borrow_num}}</span>
                        </template>
                        <template #right-icon>
                            <van-tag type="primary"><span>待借入验收</span></van-tag>
                        </template>
                    </van-cell>
                </template>
                <template #desc>
                    <div class="atn">
                        <span class="ct">设备名称：</span>
                        <span class="cc">{{item.assets}}</span>
                    </div>
                    <div class="atn">
                        <span class="ct">设备编号：</span>
                        <span class="cc">{{item.assnum}}</span>
                    </div>
                    <div class="atn">
                        <span class="ct">被借科室：</span>
                        <span class="cc">{{item.department}}</span>
                    </div>
                </template>
                <template #footer>
                    <div class="gt">
                        <van-row>
                            <van-col span="10">申请人：{{item.apply_user}}</van-col>
                            <van-col span="14">申请时间：{{item.apply_time}}</van-col>
                        </van-row>
                    </div>
                </template>
                </van-card>
            </van-list>
            <van-divider/>
        </div>
        <div v-if="show.show_give_borrow==1">
            <van-row>
                <van-col span="18" @click="is_display.give_borrow=is_display.give_borrow?false:true;">
                    <div class="bgcf total-div">
                        <div class="total-div-title"  id="give_borrow">
                            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                            归还验收 <span class="num"> {{total.give_borrow}} </span>台
                            <span class="tips" v-show="tips.give_borrow.up" @click="show_hidden('give_borrow')">收起<van-icon name="arrow-up" /></span>
                            <span class="tips" v-show="tips.give_borrow.down" @click="show_hidden('give_borrow')">展开<van-icon name="arrow-down" /></span>
                        </div>
                    </div>
                </van-col>
                <van-col span="6">
                    <Sort @order="borrow_give_order" :option="option.give_borrow"/>
                </van-col>
            </van-row>
            <van-list
                    v-model="loading.give_borrow"
                    v-show="is_display.give_borrow"
                    :finished="finished.give_borrow"
                    @load="borrowgiveList"
            >
                <van-card v-for="item in list.borrow_give_list" :key="item.assid">
                    <template #desc>
                    <div class="jumpButton">
                        <van-button type="info" size="small" :to="{ path: $store.getters.moduleName+'/Borrow/giveBackCheck', query: { borid: item.borid }}">归还验收</van-button>
                    </div>
                    <ul>
                        <li class="list_li_width">
                            <span class="detailText">设备名称：</span>
                            <span class="text">{{item.assets}}</span>
                        </li>
                        <li>
                            <span class="detailText">借调单号：</span>
                            <span class="text">{{item.borrow_num}}</span>
                        </li>
                        <li class="list_li_width">
                            <span class="detailText">申请科室：</span>
                            <span class="text">{{item.apply_department}}</span>
                        </li>
                        <li>
                            <span class="detailText">申请人：</span>
                            <span class="text">{{item.apply_user}}</span>
                        </li>
                        <li>
                            <span class="detailText">预计归还：</span>
                            <span class="text">{{item.estimate_back}}</span>
                        </li>
                    </ul>
                </template>
                </van-card>
            </van-list>
            <van-divider/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {
        Cell,
        CellGroup,
        List,
        Button,
        Card,
        Search,
        Col,
        Row,
        Icon,
        DropdownMenu,
        DropdownItem,
        Divider,
        Grid,
        GridItem,
        Tag,
        Pagination
    } from "vant";
    import {getCheckList, postCheckList} from "@/api/common/check";
    import Sort from "@/components/Sort";

    export default {
        name: 'getCheckList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Divider.name]: Divider,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [Grid.name]: Grid,
            [Tag.name]: Tag,
            [Pagination.name]: Pagination,
            [GridItem.name]: GridItem,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            Sort,
        },
        data() {
            return {
                list: {
                    repair_list: [],
                    borrow_in_list: [],
                    borrow_give_list: [],
                    transfer_list: [],
                },
                show: {
                    show_in_borrow: 0,
                    show_repair: 0,
                    show_give_borrow: 0,
                    show_transfer: 0,
                },
                is_display: {
                    repair: true,
                    in_borrow: true,
                    give_borrow: true,
                    transfer: true,
                },
                tips: {
                    repair: {
                        up:true,
                        down:false
                    },
                    in_borrow: {
                        up:true,
                        down:false
                    },
                    give_borrow: {
                        up:true,
                        down:false
                    },
                    transfer: {
                        up:true,
                        down:false
                    },
                },
                keyword: '',
                loading: {
                    repair: false,
                    in_borrow: false,
                    give_borrow: false,
                    transfer: false,
                },
                finished: {
                    repair: false,
                    in_borrow: false,
                    give_borrow: false,
                    transfer: false,
                },
                option: {
                    repair: [
                        {text: '按报修时间（降序）', value: 'applicant_time-desc'},
                        {text: '按报修时间（升序）', value: 'applicant_time-asc'},],
                    in_borrow: [
                        {text: '按借调申请时间（降序）', value: 'apply_time-desc'},
                        {text: '按申请科室（降序）', value: 'apply_departid-desc'},
                        {text: '按借调申请时间（升序）', value: 'apply_time-asc'},
                        {text: '按申请科室（升序）', value: 'apply_departid-asc'},],
                    transfer: [
                        {text: '按申请时间（降序）', value: 'applicant_time-desc'},
                        {text: '按申请时间（升序）', value: 'applicant_time-asc'},],
                    give_borrow: [
                        {text: '按预计归还时间（降序）', value: 'estimate_back-desc'},
                        {text: '按申请科室（降序）', value: 'apply_departid-desc'},
                        {text: '按预计归还时间（升序）', value: 'estimate_back-asc'},
                        {text: '按申请科室（升序）', value: 'apply_departid-asc'},],

                },
                total: {
                    repair: 0,
                    in_borrow: 0,
                    give_borrow: 0,
                    transfer: 0,
                },
                page:{
                    repair: 1,
                    in_borrow: 1,
                    give_borrow: 1,
                    transfer: 1,
                },
                //全局列表搜索条件
                where: {}
            };
        },
        methods: {
            tourl(borid){
                this.$router.push({
                    name:'borrowInCheck',
                    query:{
                        borid:borid
                    }
                });
            },
            transfer_change(value){
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: value};
                this.transferList();
            },
            repair_change(value){
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: value};
                this.repairList();
            },
            borrowin_change(value){
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: value};
                this.borrowinList();
            },
            borrowgive_change(value){
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: value};
                this.borrowgiveList();
            },
            repairList() {
                let data = [];
                if (this.where.order) {
                    data.order = this.where.order;
                    data.sort = this.where.sort;
                }
                data.action = "repair";
                postCheckList(Qs.stringify(data),this.where.page).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished.repair = true;
                    }
                    this.list.repair_list = [];
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.repair_list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    this.total.repair = response.total;
                    //数据获取到了 停止加载
                    this.loading.repair = false;
                    this.finished.repair = true;
                    if (this.where.page) {
                        document.getElementById("repair").scrollIntoView();
                    }
                    this.where = [];
                })
            },
            borrowinList() {
                let data = [];
                if (this.where.order) {
                    data.order = this.where.order;
                    data.sort = this.where.sort;
                }
                data.action = "in_borrow";
                postCheckList(Qs.stringify(data),this.where.page).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished.in_borrow = true;
                    }
                    this.list.borrow_in_list = [];
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.borrow_in_list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    this.total.in_borrow = response.total;
                    //数据获取到了 停止加载
                    this.loading.in_borrow = false;
                    this.finished.in_borrow = true;
                    if (this.where.page) {
                        document.getElementById("in_borrow").scrollIntoView();
                    }
                    this.where = [];
                })
            },
            transferList() {
                let data = [];
                if (this.where.order) {
                    data.order = this.where.order;
                    data.sort = this.where.sort;
                }
                data.action = "transfer";
                postCheckList(Qs.stringify(data),this.where.page).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished.transfer = true;
                    }
                    this.list.transfer_list = [];
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.transfer_list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    this.total.transfer = response.total;
                    //数据获取到了 停止加载
                    this.loading.transfer = false;
                    this.finished.transfer = true;
                    if (this.where.page) {
                        document.getElementById("transfer").scrollIntoView();
                    }
                    this.where = [];
                });
            },
            borrowgiveList() {
                let data = [];
                if (this.where.order) {
                    data.order = this.where.order;
                    data.sort = this.where.sort;
                }
                data.action = "give_borrow";
                postCheckList(Qs.stringify(data),this.where.page).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished.give_borrow = true;
                    }
                    this.list.borrow_give_list = [];
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.borrow_give_list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    this.total.give_borrow = response.total;
                    //数据获取到了 停止加载
                    this.loading.give_borrow = false;
                    this.finished.give_borrow = true;
                    if (this.where.page) {
                        document.getElementById("give_borrow").scrollIntoView();
                    }
                    this.where = [];
                })
            },
            getcompetence() {
                getCheckList(this.where).then(response => {
                    this.show = response.data;
                });
            },
            repair_order(value) {
                this.list.repair_list = [];
                this.loading.repair = true;
                this.finished.repair = false;
                //重新加搜索条件
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.repairList();
            },
            borrow_in_order(value) {
                this.list.borrow_in_list = [];
                this.loading.in_borrow = true;
                this.finished.in_borrow = false;
                //重新加搜索条件
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.borrowinList();
            },
            borrow_give_order(value) {
                this.list.borrow_give_list = [];
                this.loading.give_borrow = true;
                this.finished.give_borrow = false;
                //重新加搜索条件
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.borrowgiveList();
            },
            transfer_order(value) {
                this.list.transfer_list = [];
                this.loading.transfer = true;
                this.finished.transfer = false;
                //重新加搜索条件
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.transferList();
            },
            show_hidden(module){
                if(this.is_display[module]){
                    this.tips[module].up = false;
                    this.tips[module].down = true;
                }else{
                    this.tips[module].down = false;
                    this.tips[module].up = true;
                }
            }
        }, mounted() {
            //获取相关设备信息
            this.getcompetence();
        }
    }
</script>

<style scoped lang="scss">
.tips{
    color:#DEDEDE;
    margin-left: 20px;
}
.tips{
    .van-icon{
        top:2px;
        padding-left: 8px;
    }
}
.van-cell {
        padding: 5px 2px;
        color: #555;
        font-size: 1rem;
    }
    .list .van-list .van-card .jumpButton {
        bottom: 4rem;
    }
    .van-grid{
        border-right: 1px solid #c9c9c9;
        margin-right: 10px;
    }
    ::v-deep .van-icon__image{
        border-radius:25px;
    }
    .cc{font-size: 14px;color:#B0B9BD;}
    .gt{height: 45px;line-height: 45px;font-size: 14px;color:#B0B9BD;overflow: hidden;}
    .van-card__footer{
            text-align: left;
        }
</style>
