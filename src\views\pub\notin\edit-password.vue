<template>
  <div class="list">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field
            v-model="formData.username"
            name="用户名"
            label="用户名"
            placeholder="用户名"
            readonly
        />
        <van-field
            v-model="formData.password"
            type="password"
            name="原密码"
            label="原密码"
            placeholder="原密码"
            required
            :rules="[{ required: true, message: '请填写原密码' }]"
        />
        <van-field
            v-model="formData.new_password"
            type="password"
            name="新密码"
            label="新密码"
            placeholder="新密码"
            required
            :rules="[{ required: true, message: '请填写新密码' }]"
        />
        <van-field
            v-model="formData.confirm_password"
            type="password"
            name="确认新密码"
            label="确认新密码"
            placeholder="确认新密码"
            required
            :rules="[{ validator:confirm_password, message: '确认新密码与新密码不一致' }]"
        />
      </van-cell-group>
      <div style="margin: 16px;">
        <van-button round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>

  </div>
</template>

<script>
import Qs from 'qs';
import {
  Form,
  Field,
  CellGroup,
  Button, Toast,
} from "vant";
import { editPassword } from "@/api/common/notin";
import {getKey} from "@/api/login/login";
import {JSEncrypt} from 'jsencrypt';

export default {
  name: 'editPassword',
  components: {
    [Form.name]: Form,
    [Field.name]: Field,
    [CellGroup.name]: CellGroup,
    [Button.name]: Button,
  },
  data() {
    return {
      pub_key: '',
      formData:{
        userid:0,
        username:'',
        password:'',
        new_password:'',
        confirm_password:'',
      }
    };
  },
  methods: {
    getPubKey() {
      getKey().then(response => {
        this.pub_key = response.data;
      });
    },
    confirm_password() {
      if(this.formData.new_password != this.formData.confirm_password){
        return false;
      }else{
        return true;
      }
    },
    onSubmit() {
      this.getPubKey();
      var e = this;
      var interval = setInterval(function () {
        if (e.pub_key) {
          clearInterval(interval);
          var params = {};
          params.username = e.formData.username;
          params.userid = e.formData.userid;
          params.new_password = e.formData.new_password;
          params.confirm_password = e.formData.confirm_password;
          params.token = '123';
          params.publickey = e.pub_key;
          var encrypt = new JSEncrypt();
          encrypt.setPublicKey(params.publickey);
          params.password = encrypt.encrypt(e.formData.password);
          editPassword(Qs.stringify(params)).then(response => {
            if (response.status === 1) {
              localStorage.setItem("token", response.token);
              Toast({
                type: 'success',//失败fail
                duration: 2000,//2秒
                message: response.msg,
                icon: 'success',//失败cross
                forbidClick: true,//是否禁止背景点击，避免重复提交
                onClose() {
                  //关闭后跳转到登录页
                  e.$router.push('/login');
                }
              });
              //
            }
          })
        }
      }, 500);
    },
  }, mounted() {
    //获取相关设备信息
    this.formData.userid = this.$route.query.userid;
    this.formData.username = this.$route.query.username;
  }
}
</script>

<style scoped lang="scss">
.tips{
  color:#DEDEDE;
  margin-left: 20px;
}
.tips{
  .van-icon{
    top:2px;
    padding-left: 8px;
  }
}
</style>
