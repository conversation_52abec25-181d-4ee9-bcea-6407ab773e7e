<template>
    <div class="showAssets" v-if="page.is_display">
        <van-divider>设备图片</van-divider>
        <van-swipe class="image-swipe" :autoplay="3000" indicator-color="white" height="220" v-if="info.pic_url" @change="onChange">
            <van-swipe-item v-for="(item,index) in info.pic_url" :key="index" @click="showImage">
                <van-image height="220" :src="item" fit="fill"/>
            </van-swipe-item>
        </van-swipe>
        <div class="no-images-tips" v-else>
           <p>{{page.pic_text}}</p>
            <van-uploader v-show="page.addAssets" max-size="10485760" @oversize="oversize" v-model="fileList" multiple :after-read="afterRead" :before-delete="beforedelete"/>        </div>
        <van-divider>设备相关信息</van-divider>
        <van-tabs swipe-threshold="3" :border="false">
            <van-tab title="设备基本信息">
                <AssetsInfo :info='info'/>
            </van-tab>
          <van-tab title="资质信息">
            <FactoryInfo :info='info'/>
          </van-tab>
          <van-tab title="技术资料">
            <TechnicalInfo :info='info'/>
          </van-tab>
          <van-tab title="档案资料">
            <Assetsfile :info='info'/>
          </van-tab>
          <van-tab title="维保信息">
            <AssetsInsurance  :insurance='insurance'/>
          </van-tab>
            <van-tab title="设备生命历程" class="lifeInfo">
                <van-steps direction="vertical" :active="0">
                    <van-step v-for="(item,index) in info.life" :key="index">
                        <van-collapse v-model="page.activeNames" :border="false">
                            <van-collapse-item :title="item.title" :name="index">
                                <div v-html="item.html"/>
                                <div class="timelineContentDiv">
                                    <label>该记录日期：</label>
                                    <span>{{item.sort_date}}</span>
                                </div>
                            </van-collapse-item>
                        </van-collapse>
                    </van-step>
                    <van-step>
                        <van-collapse v-model="page.activeNames" :border="false">
                            <van-collapse-item title="设备入库" name="999">
                                <div class="timelineContentDiv">
                                    <label>该记录日期：</label>
                                    <span>{{info.storage_date}}</span>
                                </div>
                            </van-collapse-item>
                        </van-collapse>
                    </van-step>
                </van-steps>
            </van-tab>
        </van-tabs>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Divider, Swipe, SwipeItem, Cell, CellGroup, Tab, Tabs, Toast, Step, Steps, ImagePreview,Notify, Image as VanImage, Overlay, Uploader, Loading, Collapse, CollapseItem} from 'vant';
    import { getAssetsInfo, getInsuranceInfo, upload } from "@/api/assets/lookup/show";
    import AssetsInfo from "@/components/Assetsinfo";
    import FactoryInfo from "./FactoryInfo";
    import TechnicalInfo from "./TechnicalInfo";
    import Assetsfile from "./Assetsfile";
    import AssetsInsurance from "./AssetsInsurance";

    export default {
        name: 'showAssets',
        components: {
            [Divider.name]: Divider,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Step.name]: Step,
            [Steps.name]: Steps,
            [Uploader.name]: Uploader,
            [Steps.Overlay]: Overlay,
            [VanImage.name]: VanImage,
            [Loading.name]: Loading,
            [Collapse.name]: Collapse,
            [CollapseItem.name]: CollapseItem,
            AssetsInfo,
          FactoryInfo,
          TechnicalInfo,
          Assetsfile,
          AssetsInsurance
        },
        methods: {
            getInfo(assid,assnum) {
                getAssetsInfo(
                    {assid: assid,assnum:assnum}
                ).then(response => {
                    this.info = response.data;
                    this.page.is_display = true;
                    this.page.addAssets = response.addAssets;
                    if (!response.addAssets) {
                        this.page.pic_text = "该设备暂无图片!";
                    }
                    if (response.status==-1) {
                        this.timer = setTimeout(()=>{
                            this.$router.go(-1);
                         },2000);
                    }
                })

                getInsuranceInfo(Qs.stringify({
                  assid: assid
                  , action: 'doRenewal'
                  , sort: 'insurid'
                  , order: 'asc'
                })).then(res=>{
                  if (res.code === 200){
                    this.insurance = res.rows
                  }
                })


            },
            oversize(){
                //超出限制大小
                Notify({ type: 'danger', message: '图片超出10M大小限制' });
                return false;
            },
            afterRead(file, detail) {
                // 此时可以自行将文件上传至服务器
                let values = {};
                values.action = 'upload';
                values.t = 'assets_info';
                values.assid = this.info.assid;
                let fileList = this.fileList;
                if (Object.keys(file)[0]==0) {
                    //多图上传
                    let a = detail.index;
                    file.forEach(function(value, index){
                        values.base64 = value.content;
                        values.index = a;
                        upload(Qs.stringify(values)).then(response => {
                            Toast.success(response.msg);
                        fileList[response.index].url = response.url;
                        });
                        a = a+1;

                    });

                }else{
                    //单图上传
                    values.base64 = file.content;
                    upload(Qs.stringify(values)).then(response => {
                    Toast.success(response.msg);
                    this.fileList[detail.index].url = response.url;
                  })
                }


            },
            beforedelete(file, detail) {
                let values = [];
                values.action = 'deleteAssetsPic';
                values.assid = this.info.assid;
                values.src = this.fileList[detail.index].url;
                upload(Qs.stringify(values)).then(response => {
                    Toast.success(response.msg);
                    /*this.$router.push('/');*/
                });
                return new Promise((resolve) => {
                    resolve();
                })
            },
            onChange(index) {
                this.positions = index;
            },
            showImage() {
                ImagePreview({
                    images: this.info.pic_url,
                    startPosition: this.positions,
                });
            },
        },
        data() {
            return {
                page: {
                    is_display: false,
                    pic_text:'该设备暂无图片，请点击按钮上传！',
                    activeNames: [0],
                    addAssets:true,
                },
                position: 0,
                info: {
                    // factoryInfo: [],
                    // supplierInfo: [],
                    // repairInfo: [],
                    life: []
                },
                fileList: [],
              insurance:[]
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.assid,this.$route.query.assnum);
        }
    }
</script>

<style scoped lang="scss">
    .red {
        color: #FF5722
    }

    .green {
        color: #85AB70
    }

    .no-images-tips {
        text-align: center;
    }

    ::v-deep .van-step--vertical .van-step__circle-container {
        font-size: 16px;
    }

    .lifeInfo {
        ::v-deep .van-cell {
            color: inherit;
        }

        ::v-deep .timelineContentDiv {
            overflow: hidden;
        }

        ::v-deep .timelineContentDiv label {
            float: left;
            margin-right: 0.2rem;
            min-width: 7em;
            text-align: right;
        }

        ::v-deep .timelineContentDiv span {
            display: block;
            overflow: hidden;
            word-break: normal;
            word-wrap: break-word;
        }
    }

</style>
