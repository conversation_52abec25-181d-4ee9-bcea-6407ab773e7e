<template>
    <div class="list">
        <div class="bgcf">
            <van-search
                    v-model="keyword"
                    shape="round"
                    placeholder="搜索（设备名称、编号、科室）"
                    @search="onSearch"
            />
        </div>
        <van-row>
            <van-col span="18">
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        转科进程 <span class="num"> {{total}} </span> 台次
                    </div>
                </div>
            </van-col>
            <van-col span="6">
                <van-dropdown-menu class="filter">
                    <van-dropdown-item title="排序" v-model="orderValue" :options="option" @change="order"/>
                </van-dropdown-menu>
            </van-col>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="refreshList"
        >
            <van-card v-for="item in list" :key="item.assid" @click="tourl(item.atid)">
                <template #title>
                    <van-cell>
                        <!-- 使用 title 插槽来自定义标题 -->
                        <template #title>
                            <span class="custom-title">单号：{{item.transfernum}}</span>
                        </template>
                        <template #right-icon>
                            <van-tag :type="item.type"><span v-html="item.show_status_name"></span></van-tag>
                        </template>
                    </van-cell>
                </template>
                <template #desc>
                    <div class="atn">
                        <span class="ct">设备名称：</span>
                        <span class="cc">{{item.assets}}</span>
                    </div>
                    <div class="atn">
                        <span class="ct">设备编号：</span>
                        <span class="cc">{{item.assnum}}</span>
                    </div>
                    <div class="atn">
                        <span class="ct">转出科室：</span>
                        <span class="cc">{{item.tranout_depart_name}}<img :src="require('@/assets/images/list/right.png')" style="width: 24px;margin-left: 5px;margin-bottom: -7px;"></span>
                    </div>
                    <div class="atn">
                        <span class="ct">转入科室：</span>
                        <span class="cc">{{item.tranin_depart_name}}<img :src="require('@/assets/images/list/left.png')" style="width: 24px;margin-left: 5px;margin-bottom: -7px;"></span>
                    </div>
                </template>
                <template #footer>
                    <div class="gt">
                        <van-row>
                            <van-col span="10">申请人：{{item.applicant_user}}</van-col>
                            <van-col span="14">申请时间：{{item.applicant_time}}</van-col>
                        </van-row>
                    </div>
                </template>
            </van-card>
        </van-list>
        <ScrollTop/>
    </div>

</template>

<script>
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem, Tag ,NoticeBar } from "vant";
    import {getProgressList} from "@/api/assets/transfer/list";
    import ScrollTop from "@/components/ScrollTop";

    export default {
        name: 'getList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            [Tag.name]: Tag,
            [NoticeBar  .name]: NoticeBar  ,
            ScrollTop
        },
        data() {
            return {
                list: [],
                keyword: '',
                orderValue: '',
                loading: false,
                finished: false,
                total: '',
                option: [
                    {text: '按科室名称（降序）', value: 'department-desc'},
                    {text: '按科室设备数量（降序）', value: 'assetssum-desc'},
                    {text: '按设备状态（降序）', value: 'status-desc'},
                    {text: '按设备启用时间（降序）', value: 'opendate-desc'},
                    {text: '按科室名称（升序）', value: 'department-asc'},
                    {text: '按科室设备数量（升序）', value: 'assetssum-asc'},
                    {text: '按设备状态（升序）', value: 'status-asc'},
                    {text: '按设备启用时间（升序）', value: 'opendate-asc'},
                ],
                //全局列表搜索条件
                where: {
                    page: 1
                }
            };
        },
        methods: {
            tourl(atid){
                this.$router.push({
                    name:'transfer_progress_detail',
                    query:{
                        atid:atid
                    }
                });
            },
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                getProgressList(this.where).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished = true;
                    }
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    if (this.where.page === 1) {
                        this.total = response.total;
                    }
                    //数据获取到了 停止加载
                    this.loading = false;
                    //全部加载了 且当前列表数量大于总数 设置完成状态
                    if (this.list.length >= this.total) {
                        this.finished = true;
                    }
                    //页数加1
                    this.where.page++;
                })
            },
            onSearch(keyword) {
                this.resetWhere();
                this.finished = false;
                //重新加搜索条件
                this.where.search = keyword;
                this.refreshList();
            },
            order(value) {
                this.resetWhere(false);
                //重新加搜索条件
                this.where.page = 1;
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.refreshList();
            },
            getcatid(id) {
                this.resetWhere();
                //重新加搜索条件
                this.where.catid = id;
                this.refreshList();
            },
            getdepartid(id) {
                this.resetWhere();
                //重新加搜索条件
                this.where.departid = id;
                this.refreshList();
            },
            //重置列表条件
            resetWhere(clearWhere = true) {
                this.list = [];//清空列表
                if (clearWhere) {
                    //清空搜索条件
                    this.where = {};
                }
                //重置为第一页
                this.where.page = 1;
                //重置表格为加载状态
                this.loading = true;
                this.finished = false;
            }
        }
    }
</script>

<style scoped lang="scss">
    .list {
        .total-div {
            margin-top: 0;
        }
        ::v-deep .filter {
            margin-top: 0;
        }
        .custom-title{
            font-size: 18px;
        }

        .van-card{
            padding: 8px 0;
        }
        .van-cell {
            padding: 10px 0;
        }
        .van-card__header{padding: 8px 16px;}
        .van-card__footer{background:#FAFAFA;padding-left: 16px;}
        .atn{margin-top: 5px;}
        .ct{font-size: 14px;}
        .cc{font-size: 14px;color:#B0B9BD;}
        .gt{height: 45px;line-height: 45px;font-size: 14px;color:#B0B9BD;overflow: hidden;}
        .van-card__footer{
            text-align: left;
        }
    }
</style>
