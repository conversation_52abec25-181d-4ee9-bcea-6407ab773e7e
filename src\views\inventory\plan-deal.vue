<template>
  <div class="con_detail">
    <div class="card-header">
      <h2 class="detailTitle">设备基本信息</h2>
      <div class="bl1px"></div>
    </div>

    <van-cell-group>
      <van-cell title="设备名称" :value="info.assets"/>
      <van-cell title="设备编码" :value="info.assetnum"/>
      <van-cell title="使用科室" :value="info.department"/>
    </van-cell-group>


    <div class="card-header">
      <h2 class="detailTitle">盘点信息</h2>
      <div class="bl1px"></div>
    </div>
    <van-cell-group>
      <van-field placeholder="请选择" v-model="form.status" label="盘点状态" @click="showInvent = true"/>
      <van-popup v-model="showInvent" position="bottom">
        <van-picker title="请选择盘点状态" show-toolbar :columns="columns" @confirm="onConfirm"/>
      </van-popup>

      <van-field v-if="form.status === '异常'" v-model="form.reason" label="原因" placeholder="请输入"/>
      <van-field v-if="form.status === '异常'" v-model="form.result" label="处理结果：" placeholder="请输入"/>
      <!--      <van-field v-model="form.value" label="盘点员：" placeholder="请输入"/>-->
      <!-- <van-field placeholder="请选择" v-model="form.inventory_user" label="盘点员" @click="showInvent1 = true"/>
      <van-popup v-model="showInvent1" position="bottom">
        <van-picker title="请选择盘点员" show-toolbar :columns="columns1" @confirm="onConfirm1"/>
      </van-popup> -->

      <div style="display: flex;justify-content: space-around;margin-top: 50px;">
        <van-button type="info" @click="submitFrom">确定</van-button>
        <van-button type="info" @click="goBack">取消</van-button>
      </div>
    </van-cell-group>


  </div>
</template>

<script>
import {Cell, CellGroup, Field, Button, Card, Notify, Picker, Popup, DropdownMenu, DropdownItem} from 'vant';
import {clientHandDeal, showInventoryPlan} from "@/api/common/inventory";

export default {
  name: 'plan-deal',
  components: {
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Field.name]: Field,
    [Button.name]: Button,
    [Picker.name]: Picker,
    [Notify.name]: Notify,
    [Card.name]: Card,
    [Popup.name]: Popup,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
  },
  data() {
    return {
      data: {
        inventory_plan_assets_id: 1
      },
      info: {
        assetnum: '',
        assets: '',
        department: ''
      },
      showInvent: false,
      showInvent1: false,
      columns: ["正常", '异常'],
      columns1: [],
      form: {
        inventory_plan_assets_id: 0,
        status: '正常',
        reason: '',
        result: '',
        inventory_user: localStorage.getItem('uname')
      },
    }
  },
  methods: {
    getInfo() {
      let params = {inventory_plan_id: this.$route.query.inventory_plan_id};
      showInventoryPlan(params).then(response => {
        this.columns1 = response.inventory_plan.inventory_users
        const item = response.inventory_plan_assets.find(item => item.assetnum === this.$route.query.assetnum)
        console.log(item)
        this.form.inventory_plan_assets_id = item.inventory_plan_assets_id
        this.info.assetnum = item.assetnum
        this.info.assets = item.assets
        this.info.department = item.department
      })
    },
    submitFrom() {
      let params = Object.assign({},this.form)
      switch (this.form.status){
        case '正常':
          params.status = 1
          break
        case '异常':
          params.status = 2
          break
      }
      clientHandDeal(params).then(response => {
        Notify({type: 'success', message: response.msg});
        this.goBack()
      })
    },
    onConfirm(value, key) {
      this.form.status = value
      this.showInvent = false
    }, onConfirm1(value, key) {
      this.form.inventory_user = value
      this.showInvent1 = false
    },
    goBack() {
      this.$router.go(-1);
    }
  }, mounted() {
    this.getInfo();
  }
}
</script>

<style scoped lang="scss">
.van-button {
  margin-top: 10px;
}

::v-deep .gray {
  color: #666;
  margin-left: 10px;
}

::v-deep .red {
  color: #FF5722
}

.van-tabs--line .van-tabs__wrap {
  height: 0;
}
</style>
