<template>
    <div class="con_detail">
        <van-tabs :border="false">
            <van-tab title="设备基本信息">
                <AssetsInfo :info='info'/>
            </van-tab>
            <van-tab title="报修信息">
                <van-cell-group>
                    <van-cell title="报修人" :value="info.repairdata.applicant"/>
                    <van-cell title="报修时间" :value="info.repairdata.applicant_time"/>
                    <van-cell title="维修单号" :value="info.repairdata.repnum"/>
                    <van-cell title="故障描述" :value="info.repairdata.breakdown"/>
                    <div v-if="jugdePlatform()">
                        <van-cell title="语音描述" v-if="info.repairdata.wxTapeAmr==''" value="无"/>
                            <van-cell title="语音描述" v-else>
                                <template #right-icon>
                                    <van-icon class-prefix="wx-icon" :name="page.icon_name" style="line-height: inherit;"
                                            @click="bofang"/>
                                    <span @click="bofang">{{info.repairdata.seconds}}〞 点击播放</span>
                                </template>
                            </van-cell>
                    </div>

                    <van-cell title="故障照片" v-if="!info.repairdata.pic_url" value="无"/>
                    <van-cell title="故障照片" v-else>
                        <template #right-icon>
                            <a href="javascript:" @click="img">查看(总共{{info.repairdata.imgCount}}张)</a>
                        </template>
                    </van-cell>
                </van-cell-group>
            </van-tab>
        </van-tabs>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">接单表单</h2>
        </div>
        <van-form ref="repairForm" @submit="onSubmit" v-if="page.show_form==1">
            <van-field
                    v-model="form.expect_arrive"
                    name="expect_arrive"
                    rows="1"
                    autosize
                    label="预计"
                    required
                    type="digit"
                    placeholder="几分钟可以到场"
                    :rules="[{ required: true, message: '请输入预测到场时间',trigger:'onChange' }]"
            />
            <van-field
                    v-model="form.reponse_remark"
                    name="reponse_remark"
                    rows="1"
                    autosize
                    label="备注"
                    type="textarea"
                    placeholder="备注"
            />
            <van-field label="接单人" :value="page.response" readonly/>
            <van-field label="接单时间" :value="page.response_date" readonly/>
            <div style="margin: 16px;">
                <van-button round block type="primary" native-type="submit">
                    确认并提交
                </van-button>
            </div>
            <audio :src="info.repairdata.wxTapeAmr" id="audio"></audio>
        </van-form>
        <div v-else>
            <van-field label="接单人" :value="info.repairdata.response" readonly/>
            <van-field label="接单时间" :value="info.repairdata.response_date" readonly/>
            <van-field label="预测到场时间" :value="info.repairdata.expect_arrive" readonly/>
            <van-field label="备注" :value="info.repairdata.reponse_remark" readonly/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Field,
        Toast,
        Form,
        Button,
        Tab,
        Tabs,
        Popup,
        Picker,
        DatetimePicker,
        ImagePreview
    } from 'vant';
    import {getInfo, submit} from "@/api/repair/accept";
    import AssetsInfo from "@/components/Assetsinfo";

    export default {
        name: 'accept',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [ImagePreview.name]: ImagePreview,
            [DatetimePicker.name]: DatetimePicker,
            AssetsInfo,
        },
        data() {
            return {
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    repairdata: [],
                },
                page: {
                    response: '',
                    response_date: '',
                    show_form: 1,
                    is_show: true,
                    icon_name: 'yuyin',
                    voiceTime: 0,
                    img: [],
                },
                form: {
                    expect_arrive: '',
                    reponse_remark: '',
                },
                audio: {
                    src: ''
                },
            }
        },
        methods: {
            jugdePlatform(){
                return (process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX)
            },
            onSubmit(values) {
                if (this.form.formType === 'yes') {
                    values.status = 2;
                } else {
                    values.status = 4;
                }
                values.repid = this.$route.query.repid;
                values.action = 'accept';
                let _this = this;
                submit(Qs.stringify(values)).then(response => {
                    if (response.status == 1) {
                        Toast({
                            type: 'success',//失败fail
                            duration: 2000,//2秒
                            message: response.msg,
                            icon: 'success',//失败cross
                            forbidClick: true,//是否禁止背景点击，避免重复提交
                            onClose() {
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Repair/ordersLists');
                            }
                        });
                    }
                })
            },
            img() {
                ImagePreview(this.page.img);
            },
            getInfo(repid) {
                let params = {repid: repid};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.info.repairdata = response.repArr;
                    this.page.response_date = response.response_date;
                    this.page.response = response.response;
                    this.page.show_form = response.is_display;
                    if (this.info.repairdata.wxTapeAmr != "") {
                        this.info.repairdata.wxTapeAmr = process.env.VUE_APP_BASE_PROXY_URL + this.info.repairdata.wxTapeAmr;
                        this.page.voiceTime = this.info.repairdata.seconds;
                    }
                    for (let i in this.info.repairdata.pic_url) {
                        this.page.img.push(process.env.VUE_APP_BASE_PROXY_URL + this.info.repairdata.pic_url[i]);
                    }
                });
            },
            bofang() {
                let audio = document.getElementById("audio");
                if (audio.paused) { //判断当前的状态是否为暂停，若是则点击播放，否则暂停
                    this.page.icon_name = 'zanting';
                    audio.play();
                    let sec = this.page.voiceTime * 1000;
                    //变更图标
                    setTimeout(() => {
                        this.page.icon_name = 'yuyin';
                    }, sec);
                } else {
                    this.page.icon_name = 'yuyin';
                    audio.pause();
                }
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.repid);
        }
    }
</script>
<style lang="scss" scoped>
    .card-header {
        margin-top: 20px;
    }
</style>

