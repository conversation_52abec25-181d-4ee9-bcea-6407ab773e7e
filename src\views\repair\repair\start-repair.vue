<template>
    <div class="con_detail" v-if="page.is_display===1">
        <van-tabs :border="false">
            <van-tab title="维修单信息">
                <RepairInfo :info='info'/>
            </van-tab>
            <van-tab title="审批信息" v-if="info.approves!=[]&&info.approves!=undefined&&info.approves.length>0">
                <Approves :info='info.approves'/>
            </van-tab>
            <van-tab title="报价信息" v-if="info.company!=[]&&info.company!=undefined">
                <van-cell-group v-for="item in info.company" :key="item.apprid">
                    <van-cell title="公司名称" :value="item.offer_company"/>
                    <van-cell title="公司资质">
                        <template #right-icon><span v-html="item.aptitude"/></template>
                    </van-cell>
                    <van-cell title="联系人" :value="item.offer_contacts"/>
                    <van-cell title="联系人电话" :value="item.telphone"/>
                    <van-cell title="维修周期" :value="item.cycle"/>
                    <van-cell title="发票" :value="item.invoice"/>
                    <van-cell title="费用" :value="item.total_price"/>
                </van-cell-group>
            </van-tab>
        </van-tabs>
        <div class="card-header">
            <h2 class="detailTitle">配件信息</h2>
            <div class="bl1px"></div>
        </div>
        <!--添加配件-->
        <div class="partinfo">
            <Table :table="table_parts"/>
            <van-row type="flex" justify="center">
                <van-button type="default" @click="page.showSelectparts = true" native-type="button">添加配件</van-button>
            </van-row>
            <van-popup v-model="page.showSelectparts" position="top" :style="{ height: '100%' }" closeable>
                <h2 style="padding-left: 10px;">添加配件</h2>
                <van-field
                        readonly
                        clickable
                        :value="page.partsSelectTips"
                        label="配件名称"
                        placeholder="点击选择添加配件"
                        @click="page.showPartsPage = true"
                />
                <van-popup v-model="page.showPartsPage" position="bottom">
                    <van-field v-model="page.partsKeyword" label="搜索" placeholder="请输入配件名称"/>
                    <van-tree-select :items="this.partsFilter" :active-id.sync="page.partsActiveIds"
                                     main-active-index.sync="0" @click-item="onClickPartsItem"/>
                </van-popup>
                <van-cell-group>
                    <van-cell :title="item.text" v-for="item in page.partsActiveStepper" :key="item.id">
                        <template #default>
                            <van-stepper v-model="item.count" :min="item.defaultnum" theme="round" button-size="22" disable-input/>
                        </template>
                    </van-cell>
                </van-cell-group>
                <div style="margin-top: 20px;text-align: center;">
                    <van-button native-type="button" type="info" @click="page.showSelectparts = false">确定并关闭弹窗</van-button>
                </div>
            </van-popup>
        </div>
        <!--添加配件-->

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">维修处理表单</h2>
        </div>
        <div class="repinfo">
            <div class="van-doc-demo-block">
                <Table :table="table_follow"/>

                <van-row type="flex" justify="center">
                    <van-button type="default" native-type="button" @click="page.showSelectdetail = true">添加维修处理跟进详情
                    </van-button>
                </van-row>
                <van-popup v-model="page.showSelectdetail" position="bottom">
                    <van-cell-group>
                        <van-field name="editFollowTime" label="跟进日期" v-model="page.editFollowTime" placeholder="点击选择跟进日期"
                                   required
                                   readonly
                                   @click="page.showSelecttime = true;page.showSelectdetail=false"/>
                        <van-field required row="2" maxlength="60" show-word-limit label="跟进详情" placeholder="跟进详情" type="textarea" rows="1" v-model="page.editFollowDetail"/>
                        <van-button style="width:90%;margin:0 auto 20px;" block type="primary" @click="adddetail" native-type="button">保存</van-button>
                    </van-cell-group>
                </van-popup>
                <van-popup v-model="page.showSelecttime" position="bottom">
                    <van-datetime-picker
                            v-model="page.currentDate"
                            type="date"
                            title="选择跟进日期"
                            :max-date="page.maxDate"
                            @confirm="onConfirmDate"
                            @cancel="page.showSelecttime = false;page.showSelectdetail=true"
                    />
                </van-popup>
            </div>
            <div class="van-doc-demo-block">
                <h2 class="van-doc-demo-block__title">相关维修文件</h2>
                <van-uploader multiple v-model="form.fileList" image-fit="cover" max-size="10485760" @oversize="oversize" :before-delete="del_file" :after-read="afterRead" :before-read="beforeRead" :max-count="page.max_count" />
            </div>
        </div>
        <van-form ref="repairForm" @submit="onSubmit">
            <van-cell-group>
                <van-field label="维修工程师" :value="info.response" readonly/>
                <van-field name="username_tel" type="tel" label="联系电话" readonly :value="info.response_tel"/>
                <van-field name="assist_engineer" readonly label="协助工程师" v-model="info.assist_engineer" placeholder="协助工程师"
                           @click="page.showSelectengineer = true;"/>
                <van-popup v-model="page.showSelectengineer" position="bottom">
                    <van-picker
                            title="选择协助工程师"
                            show-toolbar
                            :columns="page.engineercolumns"
                            @confirm="onengineerConfirm"
                            @cancel="page.showSelectengineer = false;"
                    />
                </van-popup>
                <van-field name="assist_engineer_tel" type="tel" label="联系电话" placeholder="协助工程师电话" v-model="info.assist_engineer_tel"/>
                <van-field name="other_price" type="number" label="其他费用" placeholder="其他费用（元）" v-model="info.other_price"/>
                <van-field name="actual_price" type="number" label="总维修费用" placeholder="总维修费用（元）" v-model="info.actual_price"/>
                <van-field v-if="page.service_date == 1" label="维修开始时间" value="系统生成" readonly/>
                <van-field readonly v-else name="service_date" label="维修开始时间" v-model="page.engineer_time"
                           placeholder="点击选择维修开始时间" required
                           @click="page.showSelectservice = true;"/>
                <van-popup v-model="page.showSelectservice" position="bottom">
                    <van-datetime-picker
                            v-model="page.serviceDate"
                            type="datetime"
                            title="选择开始维修时间"
                            :max-date="page.maxDate"
                            @confirm="onConfirmserviceDate"
                            @cancel="page.showSelectservice = false;"
                    />
                </van-popup>
                <van-field v-if="page.service_working == 1" label="维修工时" v-model="info.working_hours" readonly/>
                <van-field v-else name="working_hours" required label="维修工时" placeholder="维修工时（小时）" v-model="info.working_hours"/>
                <van-field name="dispose_detail" row="3" maxlength="60" show-word-limit type="textarea" label="维修结果" placeholder="维修结果" v-model="info.dispose_detail"/>
            </van-cell-group>
            <div style="margin: 16px;">
                <van-button block color="#FFB800" native-type="button" @click="changeFormType(0)">
                    暂时保存
                </van-button>
                <p></p>
                <van-button block type="primary" native-type="button" @click="changeFormType(1)">
                    确认并提交
                </van-button>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>
<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Loading,
        Popup,
        Picker,
        DatetimePicker,
        Step,
        Steps,
        Row,
        Uploader,
        Toast,
        TreeSelect,
        Stepper,
        Col,
        Notify
    } from 'vant';
    import {getInfo, submit} from "@/api/repair/startRepair";
    import RepairInfo from "@/components/RepairInfo";
    import Approves from '@/components/Approves';
    import Table from '@/components/Table';

    export default {
        name: 'start',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [Col.name]: Col,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Loading.name]: Loading,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [Step.name]: Step,
            [Steps.name]: Steps,
            [Stepper.name]: Stepper,
            [Row.name]: Row,
            [Uploader.name]: Uploader,
            [DatetimePicker.name]: DatetimePicker,
            [TreeSelect.name]: TreeSelect,
            [Notify.name]: Notify,
            RepairInfo,
            Approves,
            Table,
        },
        data() {
            return {
                info: {
                    company: [],
                    parts: [],
                    approves: [],
                    users:[]
                },
                page: {
                    is_display: 0,//用于判断是否加载完数据
                    showSelectparts: false,
                    showSelectpartsnum: false,
                    showPartsSelectPage: false,
                    showSelectdetail: false,
                    showFollowDetail: false,
                    showFile: false,
                    showFollowDate: false,
                    partscolumns: [],
                    parts: [],
                    editFollowTime: "",
                    showSelecttime: false,
                    currentDate: new Date(),//当前日期
                    editFollowDetail: "",
                    engineercolumns: [],
                    showSelectengineer: false,
                    service_date: 0,
                    serviceDate: new Date(),//当前日期
                    showSelectservice: false,
                    engineer_time: '',
                    service_working: 0,
                    partsSelectTips: '',//添加配件弹窗用
                    showPartsPage: false,//添加配件弹窗用
                    partsKeyword: '',//添加配件弹窗用
                    partsActiveIds: [],//添加配件弹窗用
                    partsActiveStepper: [],//添加配件弹窗用
                    partstemporary: {},//已缓存配件
                    partsindex: [],
                    partsindexnum: [],
                    maxDate: new Date(),
                  max_count:3
                },
                form: {
                    detail: [],
                    file:[],
                    fileList: [],
                    parts: [],
                    overEngineer: 0,
                },
                table_parts: {
                    headerData: [
                        {text: '配件名称', field: 'parts', width: 40},
                        {text: '配件型号', field: 'parts_model', width: 40},
                        {text: '数量', field: 'count'},
                    ],
                    noData:'暂无相关配件信息',
                    bodyData: []
                },
                table_follow: {
                    headerData: [
                        {text: '跟进日期', field: 'followdate', width: 30},
                        {text: '处理详情', field: 'detail'},
                    ],
                    noData:'暂无相关跟进信息',
                    bodyData: []
                },
            }
        },
        computed: {
            partsFilter() {
                //添加配件弹窗用
                return [
                    {
                        text: '配件',
                        children: this.page.partscolumns.filter(v => {
                            return v['text'].indexOf(this.page.partsKeyword.trim()) >= 0
                        }),
                        badge: 0
                    }
                ];
            },
        },
        methods: {
            oversize(){
                //超出限制大小
                Notify({ type: 'danger', message: '图片超出10M大小限制' });
                return false;
            },
            changeFormType(tmp_save) {
                this.form.overEngineer = tmp_save;//改变提交表单的formType
                this.$refs.repairForm.submit();
            },
            onConfirmserviceDate(time) {
                this.page.engineer_time = this.timeFormat(time);
                this.page.showSelectservice = false;
            },
            onengineerConfirm(value) {
                this.info.assist_engineer = value;
                for(let i in this.info.users){
                    if(this.info.users[i]['username'] === value){
                        this.info.assist_engineer_tel = this.info.users[i]['telephone'];
                    }
                }
                this.page.showSelectengineer = false;
            },
            del_file(index) {
                if (index.file[0]) {
                    let file_id = index.file[0].file_id;
                if (this.form.fileList.length > 0) {
                    let index = this.form.fileList.findIndex(v => {
                        return v.file_id === file_id
                    })
                    let values = [];
                    values.action = 'del_file';
                    values.file_id = file_id;
                    submit(Qs.stringify(values)).then(response => {
                       Toast.success(response.msg);
                    })
                    this.form.fileList.splice(index, 1);
                }
                }else{
                let name = index['file']['name'];
                if (this.form.file.length > 0 && this.form.fileList.length > 0) {
                    let index = this.form.file.findIndex(v => {
                        return v.old_name === name
                    })
                    this.form.file.splice(index, 1);
                    this.form.fileList.splice(index, 1);
                }
               }
            },
          beforeRead(file){
            if (file.length > this.page.max_count){
              Notify({ type: 'danger', message: `最多只能选择${this.page.max_count}个图片` });
              return  false
            }
            return true
          },
            afterRead(file) {
                let values = [];
                values.action = 'upload';
                values.base64 = file.content;
                values.fileName = file.file.name;
                submit(Qs.stringify(values)).then(response => {
                    let file = {
                        file_name: response.data.formerly,
                        save_name: response.data.title,
                        file_url: response.data.src,
                        src: process.env.VUE_APP_BASE_PROXY_URL + response.data.src,
                        file_type: response.data.ext,
                        file_size: response.data.size
                    };
                    this.form.file.push(file);
                })

            },
            adddetail() {
                if(!this.page.editFollowTime){
                    Notify('请选择跟进日期');
                    return false;
                }
                this.page.editFollowDetail = this.page.editFollowDetail.replace(/(^\s*)|(\s*$)/g, "");
                if(!this.page.editFollowDetail){
                    Notify('请填写跟进详情');
                    return false;
                }
                this.form.detail.push({
                    editFollowTime: this.page.editFollowTime,
                    editFollowDetail: this.page.editFollowDetail,
                    nextdate: '-'
                });
                //添加一行表格数据
                this.table_follow.bodyData.push({
                    followdate: this.page.editFollowTime,
                    detail: this.page.editFollowDetail,
                });

                this.page.editFollowTime = "";
                this.page.editFollowDetail = "";
                this.page.showSelectdetail = false;
            },
            onConfirmDate(time) {
                let year = time.getFullYear();
                let month = time.getMonth() + 1;
                let day = time.getDate();
                month = month < 10 ? '0'+month : month;
                day = day < 10 ? '0'+day : day;
                this.page.editFollowTime = year + '-' + month + '-' + day;
                this.page.showSelecttime = false;
                this.page.showSelectdetail = true;
            },
            timeFormat(time) { // 时间格式化
                let year = time.getFullYear();
                let month = time.getMonth() + 1;
                let day = time.getDate();
                let hours = time.getHours();
                let minutes = time.getMinutes();
                month = month < 10 ? '0'+month : month;
                day = day < 10 ? '0'+day : day;
                hours = hours < 10 ? '0'+hours : hours;
                minutes = minutes < 10 ? '0'+minutes : minutes;
                return year + '-' + month + '-' + day+' '+hours+':'+minutes;
            },
            onpartsCancel() {
                this.page.showSelectparts = false;
            },
            onSubmit(values) {
                values.overEngineer = this.form.overEngineer;
                if (values.assist_engineer == "") {
                    values.assist_engineer = -1;
                }
                //跟进详情
                values.followdate = [];
                values.remark = [];
                values.nextdate = [];
                for (let i in this.form.detail) {
                    values.followdate.push(this.form.detail[i].editFollowTime);
                    values.remark.push(this.form.detail[i].editFollowDetail);
                    values.nextdate.push(this.form.detail[i].nextdate);
                }
                //配件
                values.partname = "";
                values.model = "";
                values.num = "";
                values.partid = "";
                values.is_scene = this.form.is_scene;
                let addParts = [];
                this.page.partsActiveStepper.forEach(v => {
                    this.page.partscolumns.forEach(v1 => {
                        if (v.id === v1.id && this.page.partsindex.indexOf(parseInt(v1.id)) == -1) {
                            addParts.push({
                                num: v.count,
                                parts: v1.parts,
                                model: v1.parts_model,
                                partid: v1.partid,
                            })
                        } else if (v.id === v1.id && parseInt(v.count) - parseInt(v.defaultnum) > 0) {
                            addParts.push({
                                num: parseInt(v.count) - parseInt(v.defaultnum),
                                parts: v1.parts,
                                model: v1.parts_model,
                                partid: v1.partid,
                            })
                        }
                    })
                });
                for (let i in addParts) {
                    if (addParts[i].num > 0) {
                        values.partname += addParts[i].parts + '|';
                        values.model += addParts[i].model + '|';
                        values.num += addParts[i].num + '|';
                        values.partid += addParts[i].partid + '|';
                    }
                }
                //文件上传
                values.file_name = "";
                values.save_name = "";
                values.file_url = "";
                values.file_type = "";
                values.file_size = "";
                for(let n in this.form.fileList){
                    for (let i in this.form.file) {
                        if(this.form.fileList[n].file.name === this.form.file[i].file_name){
                            values.file_name += this.form.file[i].file_name + '|';
                            values.save_name += this.form.file[i].save_name + '|';
                            values.file_url += this.form.file[i].file_url + '|';
                            values.file_type += this.form.file[i].file_type + '|';
                            values.file_size += this.form.file[i].file_size + '|';
                        }
                    }
                }
                values.repid = this.$route.query.repid;
                let _this = this;
                submit(Qs.stringify(values)).then(response => {
                    Toast({
                        type:'success',//失败fail
                        duration:2000,//2秒
                        message: response.msg,
                        icon:'success',//失败cross
                        forbidClick:true,//是否禁止背景点击，避免重复提交
                        onClose(){
                            //关闭后跳转到首页
                            _this.$router.push(this.$store.getters.moduleName+'/Repair/getRepairLists');
                        }
                    });
                })
            },
            onClickPartsItem(data) {
                //添加配件用
                if (this.page.partsActiveIds.length === 0) {
                    this.page.partsSelectTips = '点击选择配件';
                } else {
                    this.page.partsSelectTips = `共${this.page.partsActiveIds.length}项，点击查看`;
                }
                //统计选项徽章
                this.partsFilter[0]['badge'] = this.page.partsActiveIds.length;
                //用于步进器及历史记录
                let noExist = true;
                if (this.page.partsActiveStepper.length > 0) {
                    this.page.partsActiveStepper.forEach((v, k) => {
                        if (v.id === data.id) {
                            noExist = false;
                            this.page.partsActiveStepper.splice(k, 1);
                        }
                    });
                }
                if (noExist) {
                    let obj = {
                        id: data.id,
                        text: data.text,
                        count: data.num ? data.num : 1,
                        defaultnum: data.num ? data.num : 1,
                        parts: data.parts,
                        parts_model: data.parts_model
                    };
                    this.page.partsActiveStepper.push(obj);
                }
                this.table_parts.bodyData = this.page.partsActiveStepper;
            },
            getInfo(repid) {
                let params = {repid: repid};
                getInfo(params).then(response => {
                    this.info = response.repArr;
                    this.info.users = response.user;
                    this.table_follow.bodyData = response.follow;
                    this.page.partsAll = response.partsAll;
                    this.page.engineer_time = response.repArr.engineer_time;
                    for (let i in response.partsAll) {
                        let partsObj = {
                            id: i,
                            text: response.partsAll[i].parts + '(规格型号:' + response.partsAll[i].parts_model + ')',
                            parts: response.partsAll[i].parts,
                            parts_model: response.partsAll[i].parts_model
                        };
                        this.page.partscolumns.push(partsObj);
                    }
                    for (let i in response.user) {
                        this.page.engineercolumns.push(response.user[i].username);
                    }
                    this.info.company = response.company;
                    for (let i in response.parts) {
                        let index = this.partsFilter[0].children.findIndex(v => {
                            if (v.parts == response.parts[i].parts && v.part_model == response.parts[i].parts_model) {
                                return true;
                            }
                        });
                        if (this.page.partsindex.indexOf(index) == -1) {
                            this.page.partsindex.push(index);
                            this.page.partsActiveIds.push(String(index));
                            this.partsFilter[0].children[index].disabled = true;
                            this.partsFilter[0].children[index].num = response.parts[i].part_num;
                            this.page.partsindexnum.push(response.parts[i].part_num);
                            this.onClickPartsItem(this.partsFilter[0].children[index]);
                        } else {
                            let A_Index = this.page.partsActiveStepper.findIndex(v => {
                                if (v.id == index) {
                                    return true;
                                }
                            });
                            this.page.partsActiveStepper[A_Index]['count'] = parseInt(this.page.partsActiveStepper[A_Index]['count']) + parseInt(response.parts[i].part_num);
                            this.page.partsActiveStepper[A_Index]['defaultnum'] = parseInt(this.page.partsActiveStepper[A_Index]['defaultnum']) + parseInt(response.parts[i].part_num);

                        }
                    }
                    this.table_parts.bodyData = this.page.partsActiveStepper;
                    this.info.approves = response.approves;
                    this.info.life = response.life;
                    this.page.service_date = response.service_date;
                    this.page.service_working = response.service_working;
                    for (let i in response.follow) {
                        this.form.detail.push({
                            editFollowTime: response.follow[i].followdate,
                            editFollowDetail: response.follow[i].detail,
                            nextdate: response.follow[i].nextdate
                        });
                    }
                    //文件
                        for (let i in response.files) {
                            response.files[i].url = process.env.VUE_APP_BASE_PROXY_URL + response.files[i].file_url;
                            response.files[i].file = [];
                            response.files[i].file.push({name:response.files[i].file_name,file_id:response.files[i].file_id});

                            this.form.fileList.push(response.files[i]);
                        }
                    this.page.is_display = 1;

                })
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.repid);
        }
    }
</script>

<style scoped lang="scss">
    .van-button {
        margin-top: 10px;
    }

    .showPartsTable {
        .th {
            background-color: #e6e6e654;
        }

        ::v-deep .van-col {
            text-align: center;
        }
    }

    .card-header {
        margin-top: 20px;
    }

    .partinfo {
        background: #fff;
        padding-bottom: 20px;
    }

    .repinfo {
        background: #fff;
    }

</style>

