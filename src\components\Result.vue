<template>
    <div class="result">
        <van-empty class="custom-image" :image="imageUrl" :description="tips">
            <div v-if="btn" >
                <van-button type="primary" block :url_data="url_data" class="bottom-button" v-on:click="tourl">{{ btn }}</van-button>
            </div>
            <div v-else>
                <van-button type="primary" block class="bottom-button" @click="back">返回上一步</van-button>
            </div>
            <van-button type="default" block class="bottom-button" to="/">返回首页</van-button>
        </van-empty>
    </div>
</template>

<script>
    import {Empty, Button} from "vant";

    export default {
        name: "Result",
        data() {
            return {
                fail: require('@/assets/images/fail.png'),
                suc: require('@/assets/images/success.png'),
                imageUrl: '',
                tips: '出错了！',
                btn: false,
                url_data: ''
            }
        },
        props: {
            type: String
        },
        mounted() {
            if(this.$route.params.tips){
                var obj = JSON.parse(this.$route.params.tips);
                this.tips = obj.tips;
                this.btn = obj.btn;
                this.url_data = obj.url;
            }
            if (this.type === 'suc') {
                this.imageUrl = this.suc;
            } else {
                this.imageUrl = this.fail;
            }
        },
        methods:{
            tourl(){
                var t_url = this.url_data.replace(/-/g,"/");
                t_url = '/'+t_url;
                var n = t_url.indexOf(process.env.VUE_APP_BASE_AXIOS_URL);
                if(n >= 0){
                    //localtion跳转到新地址
                    window.location.href = process.env.VUE_APP_BASE_PROXY_URL+t_url;
                }else{
                    //新路由
                    this.$router.push(t_url);
                }
            },
            back(){
                this.$router.go(-2);//返回上一层
            },
        },
        components: {
            [Button.name]: Button,
            [Empty.name]: Empty,
        },
    }
</script>

<style scoped lang="scss">
    .bottom-button {
        margin-bottom: 1rem;
    }

    .custom-image ::v-deep .van-empty__bottom {
        width: 90%;
    }
</style>
