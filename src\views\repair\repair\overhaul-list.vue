<template>
  <div class="list">
    <div class="bgcf">
      <van-button color="#009688" block class="scanQrcode" size="normal" @click="qrCode" v-if="jugdePlatform()">扫码检修</van-button>
      <van-search
          v-model="keyword"
          shape="round"
          placeholder="搜索（设备名称、编号、科室）"
          @search="onSearch"
      />
    </div>
    <van-row>
      <van-col span="18">
        <div class="bgcf total-div">
          <div class="total-div-title">
            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
            待检修设备 <span class="num"> {{ total }} </span> 台
          </div>
        </div>
      </van-col>
      <van-col span="6">
        <Sort @order="order" :option="option"/>
      </van-col>
    </van-row>
    <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="refreshList"
    >
      <van-card :thumb="item.pic_url" v-for="item in list" :key="item.assid">
        <template #desc>
          <ul>
            <li class="list_li_width">
              <span class="detailText">设备名称：</span>
              <span class="text">{{ item.assets }}</span>
            </li>
            <li>
              <span class="detailText">维修单号：</span>
              <span class="text">{{ item.repnum }}</span>
            </li>
            <li class="list_li_width">
              <span class="detailText">使用科室：</span>
              <span class="text">{{ item.department }}</span>
            </li>
            <li class="list_li_width">
              <span class="detailText">设备编号：</span>
              <span class="text">{{ item.assnum }}</span>
            </li>
            <li class="list_li_width">
              <span class="detailText">产品序列：</span>
              <span class="text">{{ item.serialnum }}</span>
            </li>
            <li class="list_li_width">
              <span class="detailText">规格型号：</span>
              <span class="text">{{ item.model }}</span>
            </li>
            <li class="list_li_width">
              <span class="detailText">接单时间：</span>
              <span class="text">{{ item.response_date }}</span>
            </li>
          </ul>
        </template>
        <template #footer>
          <van-button type="info" block size="small" class="detail-button"
                      :to="{ path: $store.getters.moduleName+'/Repair/overhaul', query: { repid: item.repid }}">检修
          </van-button>
        </template>
      </van-card>
    </van-list>
    <ScrollTop/>
  </div>
</template>

<script>
import { Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem } from "vant";
import { getOrdersList } from "@/api/repair/list";
import ScrollTop from "@/components/ScrollTop";
import Sort from "@/components/Sort";
import wechatUtil from '@/utils/wechatUtil';
import feishuUtil from "@/utils/feishuUtil";
import {getRegexAssnum} from "@/utils/regex";

export default {
  name: 'getOrdersList',
  components: {
    [List.name]: List,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Button.name]: Button,
    [Card.name]: Card,
    [Search.name]: Search,
    [Col.name]: Col,
    [Row.name]: Row,
    [Icon.name]: Icon,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    ScrollTop,
    Sort
  },
  data() {
    return {
      list: [],
      option: [
        {text: '按报修时间（降序）', value: 'applicant_time-desc'},
        {text: '按报修时间（升序）', value: 'applicant_time-asc'},
      ],
      keyword: '',
      orderValue: '',
      loading: false,
      finished: false,
      total: 0,
      //全局列表搜索条件
      where: {
        page: 1,
        action: 'overhaulLists',
      },
    };
  },

  methods: {
    jugdePlatform(){
      return (process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX)
    },
    //二维码
    async qrCode() {
      //alert(location.href.split('#')[0]);
      var _this = this;
      switch (parseInt(process.env.VUE_APP_VERSION)) {
        case 1:
          // 微信版本
          wechatUtil.init([
            'scanQRCode',//扫一扫
          ]).then((wx) => {
            // 这里写微信的接口
            wx.scanQRCode({
              needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
              scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
              async success(res) {
                // var assnum = res.resultStr;// 当needResult 为 1 时，扫码返回的结果
                // if (assnum.indexOf("ODE_") > 0) {
                //   assnum = res.resultStr.substr(9);
                // }
                const assnum = await getRegexAssnum(res.resultStr)

                _this.$router.push({
                  name: 'overhaul',
                  query: {
                    assnum: assnum
                  }
                });
              }
            });
          })
          break;
        case 2:
          // 飞书版本
          await feishuUtil.init(['scanCode']);
          window.h5sdk.ready(() => {
            window.tt.scanCode({
              success(res) {
                // alert(JSON.stringify(`${res.result}`));
                _this.$router.push({
                  name: 'overhaul',
                  query: {
                    assnum: res.result
                  }
                });
              },
              fail(res) {
                alert(`调用失败`);
              }
            });
          });
          break;
      }

    },
    //刷新列表 第一次进入自动触发
    refreshList() {
      // 异步更新数据
      setTimeout(() => {
        getOrdersList(this.where).then(response => {
          for (let x in response.rows) {
            if (Object.prototype.hasOwnProperty.call(response.rows, x)) {
              this.list.push(response.rows[x]);
            }
          }
          this.total = response.total;
          //数据获取到了 停止加载
          this.loading = false;
          this.where.page = this.where.page + 1;
          //全部加载了 后台已经没有数据可反 就置为完成状态
          if (this.list.length >= this.total) {
            this.finished = true;
          }
        });
      }, 100);

    },
    onSearch(keyword) {
      this.list = [];
      //清空搜索条件
      this.where = {};
      //重新加搜索条件
      this.where = {page: 1, search: keyword, action: 'overhaulLists'};
      this.refreshList();
    },
    order(value) {
      this.list = [];
      //清空搜索条件
      this.where = {};
      //重新加搜索条件
      this.where = {page: 1, sort: value.split('-')[0], order: value.split('-')[1], action: 'overhaulLists'};
      this.refreshList();
    },
    getcatid(id) {
      this.list = [];
      //清空搜索条件
      this.where = {};
      //重新加搜索条件
      this.where = {page: 1, catid: id, action: 'overhaulLists'};
      this.refreshList();
    },
    getdepartid(id) {
      this.list = [];
      //清空搜索条件
      this.where = {};
      //重新加搜索条件
      this.where = {page: 1, departid: id, action: 'overhaulLists'};
      this.refreshList();
    }
  }
}
</script>

<style scoped lang="scss">

</style>
