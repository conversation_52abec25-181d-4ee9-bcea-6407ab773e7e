import wx from 'weixin-js-sdk';
import {getSignature} from "@/api/wechat";

export default {
    /* 初始化wxjsdk各种接口 */
    init(apiList = [
        'scanQRCode',
        'startRecord',
        'stopRecord',
        'chooseImage',
        'uploadImage',
        'downloadImage',
        'getLocalImgData',
        'startRecord',
        'stopRecord',
        'onVoiceRecordEnd',
        'playVoice',
        'pauseVoice',
        'stopVoice',
        'onVoicePlayEnd',
        'uploadVoice',
        'getLocation'
    ], url) {
        //需要使用的api列表
        return new Promise((resolve, reject) => {
            getSignature().then(response => {
                var res = response.signPackage;
                if (res.appId) {
                    wx.config({
                        debug: false,
                        appId: res.appId,
                        timestamp: res.timestamp,
                        nonceStr: res.nonceStr,
                        signature: res.signature,
                        jsApiList: apiList
                    });
                    wx.ready(res => {
                        // 微信SDK准备就绪后执行的回调。
                        resolve(wx, res)
                    })
                } else {
                    reject(res)
                }
            })
        })
    }
}
