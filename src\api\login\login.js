import request from '@/utils/request'


export function login(data) {
    return request({
        url: '/Login/login',
        method: 'post',
        data
    })
}
export function getKey() {
    return request({
        url: '/Notin/getKey',
        method: 'post',
    })
}
export function getOpenid() {
    return request({
        url: '/Login/login',
        method: 'get'
    })
}
export function logout() {
    return request({
        url: '/Login/logout',
        method: 'post'
    })
}