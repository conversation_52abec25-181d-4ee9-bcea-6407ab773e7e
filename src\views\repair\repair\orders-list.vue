<template>
    <div class="list" style="margin-top: 20px;">
        <van-search
                v-model="keyword"
                shape="round"
                placeholder="搜索（设备名称、编号、科室）"
                @search="onSearch"
        />
        <van-row>
            <van-col span="18">
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        待接单设备 <span class="num"> {{total}} </span> 台
                    </div>
                </div>
            </van-col>
            <van-col span="6">
                <Sort @order="order" :option="option"/>
            </van-col>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="refreshList"
        >
            <van-card :thumb="item.pic_url" v-for="item in list" :key="item.assid">
                <template #desc>
                    <ul>
                        <li class="list_li_width">
                            <span class="detailText">设备名称：</span>
                            <span class="text">{{item.assets}}</span>
                        </li>
                        <li>
                            <span class="detailText">使用科室：</span>
                            <span class="text">{{item.department}}</span>
                        </li>
                        <li class="list_li_width">
                            <span class="detailText">设备编码：</span>
                            <span class="text">{{item.assnum}}</span>
                        </li>
                        <li class="list_li_width">
                            <span class="detailText">规格型号：</span>
                            <span class="text">{{item.model}}</span>
                        </li>
                        <li class="list_li_width">
                            <span class="detailText">报 修 人：</span>
                            <span class="text">{{item.applicant}}</span>
                        </li>
                        <li class="list_li_width">
                            <span class="detailText">报修时间：</span>
                            <span class="text">{{item.applicant_time}}</span>
                        </li>
                    </ul>
                </template>
                <template #footer>
                    <van-button type="info" block size="small" class="detail-button" :to="{ path: $store.getters.moduleName+'/Repair/accept', query: { repid: item.repid }}">接单</van-button>
                </template>
            </van-card>
        </van-list>
        <ScrollTop/>
    </div>
</template>

<script>
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem} from "vant";
    import {getOrdersList} from "@/api/repair/list";
    import ScrollTop from "@/components/ScrollTop";
    import Sort from "@/components/Sort";

    export default {
        name: 'getOrdersList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            ScrollTop,
            Sort
        },
        data() {
            return {
                list: [],
                option: [
                    {text: '按报修时间（降序）', value: 'applicant_time-desc'},
                    {text: '按报修时间（升序）', value: 'applicant_time-asc'},
                ],
                keyword: '',
                orderValue: '',
                loading: false,
                finished: false,
                total: 0,
                //全局列表搜索条件
                where: {
                    page: 1
                }
            };
        },
        methods: {
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                setTimeout(() => {
                    getOrdersList(this.where).then(response => {
                        //先判断如果返回的总数为0 则把状态设置为停止加载
                        if (response.total === 0) {
                            this.finished = true;
                        }
                        //判断数据键值是否存在
                        if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                            this.list.push(...response.rows);
                        }
                        //仅第一页赋值总数
                        if (this.where.page === 1) {
                            this.total = response.total;
                        }
                        //数据获取到了 停止加载
                        this.loading = false;
                        //全部加载了 且当前列表数量大于总数 设置完成状态
                        if (this.list.length >= this.total) {
                            this.finished = true;
                        }
                        //页数加1
                        this.where.page++;
                    })
                }, 100);
            },
            onSearch(keyword) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, search: keyword};
                this.refreshList();
            },
            order(value) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, sort: value.split('-')[0], order: value.split('-')[1]};
                this.refreshList();
            },
            getcatid(id) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, catid: id};
                this.refreshList();
            },
            getdepartid(id) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, departid: id};
                this.refreshList();
            }
        }
    }
</script>

<style scoped lang="scss">

</style>
