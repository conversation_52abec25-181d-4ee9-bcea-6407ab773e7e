<template>
    <div>
        <div class="title">
            <span>请选择要登录的用户</span>
        </div>
        <van-radio-group v-model="picked" @change="onChange">
            <van-cell-group>
                <van-cell v-for="(item,index) in users" :key="index" :title="item.username" clickable @click="onClick(index,item.userid)">
                    <van-radio slot="right-icon" :name="index"/>
                </van-cell>
            </van-cell-group>
        </van-radio-group>
        <van-form @submit="onSubmit">
            <van-field
                    type="hidden"
                    name="userid"
                    :value="userid"
            />
            <div class="submit-button" style="width: 90%;margin: 0 auto;">
                <van-button block type="info" native-type="submit">
                    确定登录
                </van-button>
            </div>
            <div class="submit-button" style="width: 90%;margin: 20px auto 0;">
                <van-button block type="primary" native-type="button" :to="{ path: '/login'}">
                    登录其他账号
                </van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
    import {
        Radio,
        RadioGroup,
        Cell,
        CellGroup,
        Button,
        Form,
        Field,
        Notify
    } from "vant";

    import {getUsers,setSession} from "@/api/common/user";
    export default {
        name: "chan_user",
        data() {
            return{
                picked: '',
                users: [],
                userid:''
            }
        },
        components: {
            [Button.name]: Button,
            [Radio.name]: Radio,
            [RadioGroup.name]: RadioGroup,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Form.name]: Form,
            [Field.name]: Field,
        },
        mounted () {
            getUsers().then(res => {
                this.users = res.data;
            })
        },
        methods:{
            onChange(num) {
                this.picked = num;
            },
            onClick(num,userid) {
                this.picked = num;
                this.userid = userid;
            },
            onSubmit(values) {
                if(values.userid){
                    setSession(values.userid,1).then(res => {
                        if(res.status === 1){
                            this.$router.push('/');
                        }
                    });
                }else{
                    Notify({ type: 'warning', message: '请选择要登录的用户' });
                }
            },
        }
    }
</script>

<style scoped lang="scss">
    .title{
        margin: 30px auto;
        text-align: center;
        color: rgba(69, 90, 100, 0.6);
        font-weight: normal;
        font-size: 16px;
        line-height: 16px;
    }
    .bt{
        margin: 20px auto;
        width: 94%;
    }
</style>
