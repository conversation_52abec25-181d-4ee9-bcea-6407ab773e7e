<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1007365" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon wx-icon">&#xe638;</span>
                <div class="name">历史记录</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe613;</span>
                <div class="name">历史记录</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe63a;</span>
                <div class="name">开机</div>
                <div class="code-name">&amp;#xe63a;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe626;</span>
                <div class="name">开机</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe63b;</span>
                <div class="name">开机测试</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe633;</span>
                <div class="name">录入</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe602;</span>
                <div class="name">录入</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe603;</span>
                <div class="name">录入</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe621;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe738;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe738;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe66e;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe60a;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe641;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe695;</span>
                <div class="name">传输_上传</div>
                <div class="code-name">&amp;#xe695;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe905;</span>
                <div class="name">报警</div>
                <div class="code-name">&amp;#xe905;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe612;</span>
                <div class="name">一键</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe667;</span>
                <div class="name">公告</div>
                <div class="code-name">&amp;#xe667;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe620;</span>
                <div class="name">请输入设备编码</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe703;</span>
                <div class="name">科室</div>
                <div class="code-name">&amp;#xe703;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe639;</span>
                <div class="name">设备名称</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe61b;</span>
                <div class="name">搜索-设备编码</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe64e;</span>
                <div class="name">设备名称</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe720;</span>
                <div class="name">科室</div>
                <div class="code-name">&amp;#xe720;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe62e;</span>
                <div class="name">全部</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe608;</span>
                <div class="name">全部</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe6b4;</span>
                <div class="name">通过</div>
                <div class="code-name">&amp;#xe6b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe646;</span>
                <div class="name">通过</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe6a1;</span>
                <div class="name">不通过</div>
                <div class="code-name">&amp;#xe6a1;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe687;</span>
                <div class="name">暂停</div>
                <div class="code-name">&amp;#xe687;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe636;</span>
                <div class="name">播放</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe64d;</span>
                <div class="name">语音</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe600;</span>
                <div class="name">语音</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe67e;</span>
                <div class="name">语音</div>
                <div class="code-name">&amp;#xe67e;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe6ff;</span>
                <div class="name">语音</div>
                <div class="code-name">&amp;#xe6ff;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe64f;</span>
                <div class="name">语音</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe76b;</span>
                <div class="name">语音</div>
                <div class="code-name">&amp;#xe76b;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe7bb;</span>
                <div class="name">录音</div>
                <div class="code-name">&amp;#xe7bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe645;</span>
                <div class="name">录音</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe60d;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe624;</span>
                <div class="name">箭头</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe832;</span>
                <div class="name">箭头</div>
                <div class="code-name">&amp;#xe832;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe683;</span>
                <div class="name">相机</div>
                <div class="code-name">&amp;#xe683;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe609;</span>
                <div class="name">灯泡</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe65d;</span>
                <div class="name">APP管理</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe65b;</span>
                <div class="name">科室管理</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon wx-icon">&#xe601;</span>
                <div class="name">scan</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'wx-icon';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#wx-icon') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.wx-icon {
  font-family: "wx-icon" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="wx-icon"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"wx-icon" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-lishijilu"></span>
            <div class="name">
              历史记录
            </div>
            <div class="code-name">.wx-icon-lishijilu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-lishijilu1"></span>
            <div class="name">
              历史记录
            </div>
            <div class="code-name">.wx-icon-lishijilu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-kaiji"></span>
            <div class="name">
              开机
            </div>
            <div class="code-name">.wx-icon-kaiji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-kaiji1"></span>
            <div class="name">
              开机
            </div>
            <div class="code-name">.wx-icon-kaiji1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-kaijiceshi"></span>
            <div class="name">
              开机测试
            </div>
            <div class="code-name">.wx-icon-kaijiceshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-luru"></span>
            <div class="name">
              录入
            </div>
            <div class="code-name">.wx-icon-luru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-luru1"></span>
            <div class="name">
              录入
            </div>
            <div class="code-name">.wx-icon-luru1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-luru2"></span>
            <div class="name">
              录入
            </div>
            <div class="code-name">.wx-icon-luru2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-chakan1"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.wx-icon-chakan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-chakan2"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.wx-icon-chakan2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-chakan3"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.wx-icon-chakan3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-chakan"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.wx-icon-chakan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-view"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.wx-icon-view
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-shangchuan"></span>
            <div class="name">
              传输_上传
            </div>
            <div class="code-name">.wx-icon-shangchuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-alert"></span>
            <div class="name">
              报警
            </div>
            <div class="code-name">.wx-icon-alert
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-yijian"></span>
            <div class="name">
              一键
            </div>
            <div class="code-name">.wx-icon-yijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-gonggao"></span>
            <div class="name">
              公告
            </div>
            <div class="code-name">.wx-icon-gonggao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-qingshurushebeibianma"></span>
            <div class="name">
              请输入设备编码
            </div>
            <div class="code-name">.wx-icon-qingshurushebeibianma
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-keshi"></span>
            <div class="name">
              科室
            </div>
            <div class="code-name">.wx-icon-keshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-shebeimingcheng"></span>
            <div class="name">
              设备名称
            </div>
            <div class="code-name">.wx-icon-shebeimingcheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-bianma"></span>
            <div class="name">
              搜索-设备编码
            </div>
            <div class="code-name">.wx-icon-bianma
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-shebeimingcheng1"></span>
            <div class="name">
              设备名称
            </div>
            <div class="code-name">.wx-icon-shebeimingcheng1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-keshi1"></span>
            <div class="name">
              科室
            </div>
            <div class="code-name">.wx-icon-keshi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-quanbu"></span>
            <div class="name">
              全部
            </div>
            <div class="code-name">.wx-icon-quanbu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-quanbu1"></span>
            <div class="name">
              全部
            </div>
            <div class="code-name">.wx-icon-quanbu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-tongguo1"></span>
            <div class="name">
              通过
            </div>
            <div class="code-name">.wx-icon-tongguo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-tongguo"></span>
            <div class="name">
              通过
            </div>
            <div class="code-name">.wx-icon-tongguo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-butongguo"></span>
            <div class="name">
              不通过
            </div>
            <div class="code-name">.wx-icon-butongguo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-zanting"></span>
            <div class="name">
              暂停
            </div>
            <div class="code-name">.wx-icon-zanting
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-bofang"></span>
            <div class="name">
              播放
            </div>
            <div class="code-name">.wx-icon-bofang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-yuyin"></span>
            <div class="name">
              语音
            </div>
            <div class="code-name">.wx-icon-yuyin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-yuyin-copy"></span>
            <div class="name">
              语音
            </div>
            <div class="code-name">.wx-icon-yuyin-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-yuyin1"></span>
            <div class="name">
              语音
            </div>
            <div class="code-name">.wx-icon-yuyin1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-yuyin2"></span>
            <div class="name">
              语音
            </div>
            <div class="code-name">.wx-icon-yuyin2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-icon-"></span>
            <div class="name">
              语音
            </div>
            <div class="code-name">.wx-icon-icon-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-yuyin3"></span>
            <div class="name">
              语音
            </div>
            <div class="code-name">.wx-icon-yuyin3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-ziyuan"></span>
            <div class="name">
              录音
            </div>
            <div class="code-name">.wx-icon-ziyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-luyin1"></span>
            <div class="name">
              录音
            </div>
            <div class="code-name">.wx-icon-luyin1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-sort"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.wx-icon-sort
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-zhuan-chu"></span>
            <div class="name">
              箭头
            </div>
            <div class="code-name">.wx-icon-zhuan-chu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-zhuan-ru"></span>
            <div class="name">
              箭头
            </div>
            <div class="code-name">.wx-icon-zhuan-ru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-camera"></span>
            <div class="name">
              相机
            </div>
            <div class="code-name">.wx-icon-camera
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-dengpao"></span>
            <div class="name">
              灯泡
            </div>
            <div class="code-name">.wx-icon-dengpao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-shebeifenlei"></span>
            <div class="name">
              APP管理
            </div>
            <div class="code-name">.wx-icon-shebeifenlei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-keshiguanli"></span>
            <div class="name">
              科室管理
            </div>
            <div class="code-name">.wx-icon-keshiguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon wx-icon wx-icon-scan"></span>
            <div class="name">
              scan
            </div>
            <div class="code-name">.wx-icon-scan
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="wx-icon wx-icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            wx-icon" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-lishijilu"></use>
                </svg>
                <div class="name">历史记录</div>
                <div class="code-name">#wx-icon-lishijilu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-lishijilu1"></use>
                </svg>
                <div class="name">历史记录</div>
                <div class="code-name">#wx-icon-lishijilu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-kaiji"></use>
                </svg>
                <div class="name">开机</div>
                <div class="code-name">#wx-icon-kaiji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-kaiji1"></use>
                </svg>
                <div class="name">开机</div>
                <div class="code-name">#wx-icon-kaiji1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-kaijiceshi"></use>
                </svg>
                <div class="name">开机测试</div>
                <div class="code-name">#wx-icon-kaijiceshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-luru"></use>
                </svg>
                <div class="name">录入</div>
                <div class="code-name">#wx-icon-luru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-luru1"></use>
                </svg>
                <div class="name">录入</div>
                <div class="code-name">#wx-icon-luru1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-luru2"></use>
                </svg>
                <div class="name">录入</div>
                <div class="code-name">#wx-icon-luru2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-chakan1"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#wx-icon-chakan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-chakan2"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#wx-icon-chakan2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-chakan3"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#wx-icon-chakan3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-chakan"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#wx-icon-chakan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-view"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#wx-icon-view</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-shangchuan"></use>
                </svg>
                <div class="name">传输_上传</div>
                <div class="code-name">#wx-icon-shangchuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-alert"></use>
                </svg>
                <div class="name">报警</div>
                <div class="code-name">#wx-icon-alert</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-yijian"></use>
                </svg>
                <div class="name">一键</div>
                <div class="code-name">#wx-icon-yijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-gonggao"></use>
                </svg>
                <div class="name">公告</div>
                <div class="code-name">#wx-icon-gonggao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-qingshurushebeibianma"></use>
                </svg>
                <div class="name">请输入设备编码</div>
                <div class="code-name">#wx-icon-qingshurushebeibianma</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-keshi"></use>
                </svg>
                <div class="name">科室</div>
                <div class="code-name">#wx-icon-keshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-shebeimingcheng"></use>
                </svg>
                <div class="name">设备名称</div>
                <div class="code-name">#wx-icon-shebeimingcheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-bianma"></use>
                </svg>
                <div class="name">搜索-设备编码</div>
                <div class="code-name">#wx-icon-bianma</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-shebeimingcheng1"></use>
                </svg>
                <div class="name">设备名称</div>
                <div class="code-name">#wx-icon-shebeimingcheng1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-keshi1"></use>
                </svg>
                <div class="name">科室</div>
                <div class="code-name">#wx-icon-keshi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-quanbu"></use>
                </svg>
                <div class="name">全部</div>
                <div class="code-name">#wx-icon-quanbu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-quanbu1"></use>
                </svg>
                <div class="name">全部</div>
                <div class="code-name">#wx-icon-quanbu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-tongguo1"></use>
                </svg>
                <div class="name">通过</div>
                <div class="code-name">#wx-icon-tongguo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-tongguo"></use>
                </svg>
                <div class="name">通过</div>
                <div class="code-name">#wx-icon-tongguo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-butongguo"></use>
                </svg>
                <div class="name">不通过</div>
                <div class="code-name">#wx-icon-butongguo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-zanting"></use>
                </svg>
                <div class="name">暂停</div>
                <div class="code-name">#wx-icon-zanting</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-bofang"></use>
                </svg>
                <div class="name">播放</div>
                <div class="code-name">#wx-icon-bofang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-yuyin"></use>
                </svg>
                <div class="name">语音</div>
                <div class="code-name">#wx-icon-yuyin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-yuyin-copy"></use>
                </svg>
                <div class="name">语音</div>
                <div class="code-name">#wx-icon-yuyin-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-yuyin1"></use>
                </svg>
                <div class="name">语音</div>
                <div class="code-name">#wx-icon-yuyin1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-yuyin2"></use>
                </svg>
                <div class="name">语音</div>
                <div class="code-name">#wx-icon-yuyin2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-icon-"></use>
                </svg>
                <div class="name">语音</div>
                <div class="code-name">#wx-icon-icon-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-yuyin3"></use>
                </svg>
                <div class="name">语音</div>
                <div class="code-name">#wx-icon-yuyin3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-ziyuan"></use>
                </svg>
                <div class="name">录音</div>
                <div class="code-name">#wx-icon-ziyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-luyin1"></use>
                </svg>
                <div class="name">录音</div>
                <div class="code-name">#wx-icon-luyin1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-sort"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#wx-icon-sort</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-zhuan-chu"></use>
                </svg>
                <div class="name">箭头</div>
                <div class="code-name">#wx-icon-zhuan-chu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-zhuan-ru"></use>
                </svg>
                <div class="name">箭头</div>
                <div class="code-name">#wx-icon-zhuan-ru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-camera"></use>
                </svg>
                <div class="name">相机</div>
                <div class="code-name">#wx-icon-camera</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-dengpao"></use>
                </svg>
                <div class="name">灯泡</div>
                <div class="code-name">#wx-icon-dengpao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-shebeifenlei"></use>
                </svg>
                <div class="name">APP管理</div>
                <div class="code-name">#wx-icon-shebeifenlei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-keshiguanli"></use>
                </svg>
                <div class="name">科室管理</div>
                <div class="code-name">#wx-icon-keshiguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#wx-icon-scan"></use>
                </svg>
                <div class="name">scan</div>
                <div class="code-name">#wx-icon-scan</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
