<template>
  <div>
    <van-cell-group title="生产厂商资质信息">
      <van-cell title="生产厂商名称 " :value="info.factoryInfo.sup_name"/>
      <van-cell title="业务员 " :value="info.factoryInfo.salesman_name"/>
      <van-cell title="业务员联系电话 " :value="info.factoryInfo.salesman_phone"/>
      <van-cell title="技术人员 " :value="info.factoryInfo.artisan_name"/>
      <van-cell title="技术人员联系电话 " :value="info.factoryInfo.artisan_phone"/>
    </van-cell-group>
    <van-cell-group title="生产厂商证照信息">
      <div v-for="item in info.factoryFile" :key="item.fileid">
        <van-cell title="文件名称 ">
          <template #default>
            <a style="color: #4994df" size="small" type="info" v-if="item.ext === 'pdf'"
               @click="scan_file(item.url)">{{item.name}}
            </a>
            <a style="color: #4994df" size="small" type="info" v-if="item.ext === 'jpg'||item.ext === 'png'"
               @click="img(item.url)">{{item.name}}
            </a>
          </template>
        </van-cell>
        <van-cell title="上传日期 " :value="item.adddate"></van-cell>
        <van-cell title="发证(备案)日期 " :value="item.record_date"/>
        <van-cell title="有效期限" :value="item.term_date"/>
      </div>
    </van-cell-group>
<!--    供应商-->
    <van-cell-group title="供应商名称">
      <van-cell title="生产厂商名称 " :value="info.supplierInfo.sup_name"/>
      <van-cell title="业务员 " :value="info.supplierInfo.salesman_name"/>
      <van-cell title="业务员联系电话 " :value="info.supplierInfo.salesman_phone"/>
      <van-cell title="技术人员 " :value="info.supplierInfo.artisan_name"/>
      <van-cell title="技术人员联系电话 " :value="info.supplierInfo.artisan_phone"/>
    </van-cell-group>
    <van-cell-group title="供应商证照信息">
      <div v-for="item in info.supplierFile" :key="item.fileid">
        <van-cell title="文件名称 ">
          <template #default>
                      <a style="color: #4994df" size="small" type="info" v-if="item.ext === 'pdf'"
                                  @click="scan_file(item.url)">{{item.name}}
                      </a>
                      <a style="color: #4994df" size="small" type="info" v-if="item.ext === 'jpg'||item.ext === 'png'"
                                  @click="img(item.url)">{{item.name}}
                      </a>
          </template>
        </van-cell>
        <van-cell title="上传日期 " :value="item.adddate"></van-cell>
        <van-cell title="发证(备案)日期 " :value="item.record_date"/>
        <van-cell title="有效期限" :value="item.term_date"/>
      </div>
    </van-cell-group>
<!--    维修商-->
    <van-cell-group title="维修商资质信息">
      <van-cell title="维修商名称 " :value="info.factoryInfo.sup_name"/>
      <van-cell title="业务员 " :value="info.factoryInfo.salesman_name"/>
      <van-cell title="业务员联系电话 " :value="info.factoryInfo.salesman_phone"/>
      <van-cell title="技术人员 " :value="info.factoryInfo.artisan_name"/>
      <van-cell title="技术人员联系电话 " :value="info.factoryInfo.artisan_phone"/>
    </van-cell-group>
    <van-cell-group title="维修商证照信息">
      <div v-for="item in info.repairFile" :key="item.fileid">
        <van-cell title="文件名称 ">
          <template #default>
            <a style="color: #4994df" size="small" type="info" v-if="item.ext === 'pdf'"
               @click="scan_file(item.url)">{{item.name}}
            </a>
            <a style="color: #4994df" size="small" type="info" v-if="item.ext === 'jpg'||item.ext === 'png'"
               @click="img(item.url)">{{item.name}}
            </a>
          </template>
        </van-cell>
        <van-cell title="上传日期 " :value="item.adddate"></van-cell>
        <van-cell title="发证(备案)日期 " :value="item.record_date"/>
        <van-cell title="有效期限" :value="item.term_date"/>
      </div>
    </van-cell-group>
  </div>
</template>
<script>
import { Cell, CellGroup, ImagePreview } from 'vant';

export default {
  components: {
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
  },
  methods: {
    img(url){
      ImagePreview([process.env.VUE_APP_BASE_PROXY_URL+url]);
    },
    scan_file(path) {
      path = process.env.VUE_APP_BASE_PROXY_URL+path;
      this.$router.push({
        name: 'showFile',
        query: {
          path: path
        }
      });
    },
  },
  props: ['info'],
}
</script>

