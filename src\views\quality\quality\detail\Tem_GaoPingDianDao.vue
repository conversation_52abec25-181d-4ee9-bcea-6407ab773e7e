<template>
    <div class="con_detail" v-if="page.is_display===1">
        <Quality :info="info"/>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">质控检测明细（模板：{{page.templatename}}）</h2>
        </div>

        <van-form ref="qualityForm" @submit="onSubmit">
            <van-field name="lookslike" label="外观功能：">
                <template #input>
                    <van-radio-group @change="looklike_change" v-model="form.lookslike" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field v-show="show_lookslike_desc" name="lookslike_desc" type="textarea" rows="2" autosize v-model="form.lookslike_desc" placeholder="外观不符合的请说明情况" style="margin-bottom: 10px;"/>

            <van-collapse v-model="page.activeNames">
            <van-collapse-item :title="page.Unipolar_cutting" name="1">
                <van-row v-for="(item,key) in page.setting.Unipolar_cutting" :key="key">
                    <van-field :label="'设定值：'+item" v-model="page.Unipolar_cuttingvalue[key]" type="number"  placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            <van-collapse-item :title="page.Unipolar_coagulation" name="2">
                <van-row v-for="(item,key) in page.setting.Unipolar_coagulation"
                         :key="key">
                    <van-field :label="'设定值：'+item" v-model="page.Unipolar_coagulationvalue[key]" type="number" placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            <van-collapse-item :title="page.Bipolar_resection" name="3">
                <van-row v-for="(item,key) in page.setting.Bipolar_resection"
                         :key="key">
                    <van-field :label="'设定值：'+item" v-model="page.Bipolar_resectionvalue[key]" type="number"
                               placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            <van-collapse-item :title="page.Bipolar_coagulation" name="4">
                <van-row v-for="(item,key) in page.setting.Bipolar_coagulation"
                         :key="key">
                    <van-field :label="'设定值：'+item" v-model="page.Bipolar_coagulationvalue[key]" type="number"
                               placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            <van-collapse-item :title="page.Unipolar_mode" name="5">
                <van-row>
                    <van-field label="（单极电切)(电极高频漏电)：" v-model="page.Unipolar_modevalue[0]" type="number"
                               placeholder="测量值"/>
                </van-row>
                <van-row>
                    <van-field label="（单极电凝)(电极高频漏电)：" v-model="page.Unipolar_modevalue[1]" type="number"
                               placeholder="测量值"/>
                </van-row>
                <van-row>
                    <van-field label="（单极电切)(中性电极高频漏电)：" v-model="page.Unipolar_modevalue[2]" type="number"
                               placeholder="测量值"/>
                </van-row>
                <van-row>
                    <van-field label="（单极电凝)(中性电极高频漏电)：" v-model="page.Unipolar_modevalue[3]" type="number"
                               placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            <van-collapse-item :title="page.Unipolar_mode" name="6">
                <van-row>
                    <van-field label="（双极电切)(电极1)：" v-model="page.Bipolar_modevalue[0]" type="number"
                               placeholder="测量值"/>
                </van-row>
                <van-row>
                    <van-field label="（双极电凝)(电极1)：" v-model="page.Bipolar_modevalue[1]" type="number"
                               placeholder="测量值"/>
                </van-row>
                <van-row>
                    <van-field label="（双极电切)(电极2)：" v-model="page.Bipolar_modevalue[2]" type="number"
                               placeholder="测量值"/>
                </van-row>
                <van-row>
                    <van-field label="（双极电凝)(电极2)：" v-model="page.Bipolar_modevalue[3]" type="number"
                               placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            </van-collapse>


            <van-field name="Sound_and_light_alarm_function" label="声光报警:">
                <template #input>
                    <van-radio-group v-model="form.Sound_and_light_alarm_function" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field name="Contact_resistance_monitoring" label="接触电阻监:">
                <template #input>
                    <van-radio-group v-model="form.Contact_resistance_monitoring" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field name="total_desc" type="textarea" rows="3" show-word-limit maxlength="120" label="检测备注：" v-model="form.total_desc" placeholder="请填写检测备注（如偏离情况说明）"/>

           <div class="mp">
               <van-divider>设备铭牌照片</van-divider>
               <van-row type="flex" justify="center">
                   <van-uploader multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" :before-read="beforeRead" v-model="form.nameplate_fileList" :after-read="nameplate_afterRead"
                                 :before-delete="del_nameplate">
                   </van-uploader>
               </van-row>
               <van-divider>检测仪器视图照片</van-divider>
               <van-row type="flex" justify="center">
                   <van-uploader multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" :before-read="beforeRead" v-model="form.instrument_fileList" :after-read="instrument_afterRead"
                                 :before-delete="del_instrument">
                   </van-uploader>
               </van-row>
               <div style="margin: 16px;">
                   <van-button round block color="#FFB800" native-type="button" @click="changeFormType('keepquality')"
                               style="margin-bottom: 16px;">
                       暂时保存
                   </van-button>
                   <van-button round block type="primary" native-type="button" @click="changeFormType('end')">
                       确认并提交
                   </van-button>
               </div>
           </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>
<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Row,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Toast,
        Loading,
        Uploader,
        ImagePreview,
        Image as VanImage,
        Swipe,
        SwipeItem,
        RadioGroup,
        Radio,
        Col,
        Stepper,
        Notify,
        Collapse,
        CollapseItem
    } from 'vant';
    import {getInfo, submit} from "@/api/quality/detail";
    import Quality from "@/components/Quality";

    export default {
        name: 'ChuChanYi',
        components: {
            [Divider.name]: Divider,
            [Collapse.name]: Collapse,
            [CollapseItem.name]: CollapseItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Row.name]: Row,
            [Col.name]: Col,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Uploader.name]: Uploader,
            [Tab.name]: Tab,
            [Stepper.name]: Stepper,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Tabs.name]: Tabs,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [VanImage.name]: VanImage,
            [Loading.name]: Loading,
            Quality,
        },
        data() {
            return {
                show_lookslike_desc:false,
                info: {
                    asInfo: [],
                    qsInfo: [],
                },
                page: {
                    Unipolar_cutting: '单极电切：最大允差：',
                    Unipolar_coagulation: '单极电凝：最大允差：',
                    Bipolar_resection: '双极电切：最大允差：',
                    Bipolar_coagulation: '双极电凝：最大允差：',
                    Unipolar_mode: '高频漏电( 单极模式)(mA)',
                    Bipolar_mode: '高频漏电( 双极模式)(mA)',
                    activeNames:[],
                    file_data: '',
                    Unipolar_cuttingvalue: [],
                    Unipolar_coagulationvalue: [],
                    Bipolar_resectionvalue: [],
                    Bipolar_coagulationvalue: [],
                    Unipolar_modevalue: [],
                    Bipolar_modevalue: [],
                    setting: {
                        Unipolar_cutting: [],
                        Unipolar_coagulation: [],
                        Bipolar_resection: [],
                        Bipolar_coagulation: [],
                        Unipolar_mode: [],
                        Bipolar_mode: [],
                    },
                    templatename: '',
                    nameplate: [],
                    instrument_view: [],
                    detail_result: [],
                    positions: 0,
                    is_display: 0,
                  max_count:3
                },
                form: {
                    lookslike: "1",
                    Contact_resistance_monitoring: '1',
                    Sound_and_light_alarm_function: '1',
                    type: 'keepquality',
                    total_desc: "",
                    charge: "",
                    nameplate_fileList: [],
                    instrument_fileList: [],

                },
            }
        },
        methods: {
            looklike_change(value){
                if(value == 2){
                    this.show_lookslike_desc = true;
                }else{
                    this.show_lookslike_desc = false;
                    this.form.lookslike_desc = '';
                }
            },
            onChange(item, key) {
                let tolerance = Math.abs(item - this.page.energesisvalue[key]);
                this.page.tolerancevalue[key] = tolerance;
            },
            show_nameplate_Image(key) {
                ImagePreview({
                    images: this.page.nameplate,
                    startPosition: key,
                });
            },
            show_instrument_Image(key) {
                ImagePreview({
                    images: this.page.instrument_view,
                    startPosition: key,
                });
            },
            on_nameplate_Change(index) {
                this.page.positions = index;
            },
            on_instrument_Change(index) {
                this.page.positions = index;
            },
            nameplate_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                //上传图片
                let values = [];
                values.type = 'nameplate';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';

                        this.form.nameplate_fileList.pop();
                        this.form.nameplate_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                });
            },
          beforeRead(file){
            if (file.length > this.page.max_count){
              Notify({ type: 'danger', message: `最多只能选择${this.page.max_count}个图片` });
              return  false
            }
            return true
          },
            instrument_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                let values = [];
                values.type = 'instrument_view';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';

                        this.form.instrument_fileList.pop();
                        this.form.instrument_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                });
            },
            changeFormType(type) {
                this.form.type = type;
                this.$refs.qualityForm.submit();
            },
            del_nameplate(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            oversize(){
                //超出限制大小
                Notify({ type: 'danger', message: '图片超出10M大小限制' });
                return false;
            },
            del_instrument(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            onSubmit(values) {
                if(values.lookslike == 2){
                    //外观不符合的要说明情况
                    if(!values.lookslike_desc.trim()){
                        Notify({ type: 'danger', message: '外观不符合的请说明情况' });
                        return false;
                    }
                }
                values.Unipolar_cutting = this.page.Unipolar_cuttingvalue;
                values.Unipolar_coagulation = this.page.Unipolar_coagulationvalue;
                values.Bipolar_resection = this.page.Bipolar_resectionvalue;
                values.Bipolar_coagulation = this.page.Bipolar_coagulationvalue;
                values.Unipolar_mode = this.page.Unipolar_modevalue;
                values.Bipolar_mode = this.page.Bipolar_modevalue;
                values.qsid = this.$route.query.qsid;
                values.action = this.form.type;

                //单极电切
                for(let i = 0;i < this.page.setting.Unipolar_cutting.length;i++){
                    if(!values.Unipolar_cutting[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的单极电切测量值' });
                        return false;
                    }else if (!values.Unipolar_cutting[i]) {
                        values.Unipolar_cutting[i] = "";
                    }
                }
                //单极电凝
                for(let i = 0;i < this.page.setting.Unipolar_coagulation.length;i++){
                    if(!values.Unipolar_coagulation[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的单极电凝测量值' });
                        return false;
                    }else if (!values.Unipolar_coagulation[i]) {
                        values.Unipolar_coagulation[i] = "";
                    }
                }

                //双极电切
                for(let i = 0;i < this.page.setting.Bipolar_resection.length;i++){
                    if(!values.Bipolar_resection[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的双极电切测量值' });
                        return false;
                    }else if (!values.Bipolar_resection[i]) {
                        values.Bipolar_resection[i] = "";
                    }
                }

                //双极电凝
                for(let i = 0;i < this.page.setting.Bipolar_coagulation.length;i++){
                    if(!values.Bipolar_coagulation[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的双极电凝测量值' });
                        return false;
                    }else if (!values.Bipolar_coagulation[i]) {
                        values.Bipolar_coagulation[i] = "";
                    }
                }

                //高频漏电（单极模式）
                for(let i = 0;i < this.page.setting.Unipolar_mode.length;i++){
                    if(!values.Unipolar_mode[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的单极高频漏电测量值' });
                        return false;
                    }else if (!values.Unipolar_mode[i]) {
                        values.Unipolar_mode[i] = "";
                    }
                }

                //高频漏电（双极模式）
                for(let i = 0;i < this.page.setting.Bipolar_mode.length;i++){
                    if(!values.Bipolar_mode[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的双极高频漏电测量值' });
                        return false;
                    }else if (!values.Bipolar_mode[i]) {
                        values.Bipolar_mode[i] = "";
                    }
                }

                let _this = this;
                submit(Qs.stringify(values)).then(res => {
                    if (res.status == 1) {
                        Toast({
                            type: 'success',//失败fail
                            duration: 2000,//2秒
                            message: res.msg,
                            icon: 'success',//失败cross
                            forbidClick: true,//是否禁止背景点击，避免重复提交
                            onClose() {
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Quality/qualityDetailList');
                            }
                        });
                    }
                })
            },
            getInfo(qsid) {
                let params = {qsid: qsid};
                getInfo(params).then(response => {
                    this.info.asInfo = response.asInfo;
                    this.info.qsInfo = response.qsInfo;
                    this.page.templatename = response.templatename;

                    for (let i in response.setting) {
                        if (response.setting[i].detection_Ename == 'Unipolar_cutting') {
                            this.page.setting.Unipolar_cutting = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.Unipolar_cuttingvalue[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'Unipolar_coagulation') {
                            this.page.setting.Unipolar_coagulation = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.Unipolar_coagulationvalue[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'Bipolar_resection') {
                            this.page.setting.Bipolar_resection = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.Bipolar_resectionvalue[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'Bipolar_coagulation') {
                            this.page.setting.Bipolar_coagulation = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.Bipolar_coagulationvalue[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'Unipolar_mode') {
                            this.page.setting.Unipolar_mode = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.Unipolar_modevalue[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'Bipolar_mode') {
                            this.page.setting.Bipolar_mode = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.Bipolar_modevalue[j] = "";
                            }
                        }
                    }
                    if (response.detail_data != null && response.detail_data.fixed_detection) {
                        this.page.Unipolar_cuttingvalue = response.detail_data.fixed_detection.Unipolar_cutting;
                        this.page.Unipolar_coagulationvalue = response.detail_data.fixed_detection.Unipolar_coagulation;
                        this.page.Bipolar_resectionvalue = response.detail_data.fixed_detection.Bipolar_resection;
                        this.page.Bipolar_coagulationvalue = response.detail_data.fixed_detection.Bipolar_coagulation;
                        this.page.Unipolar_modevalue = response.detail_data.fixed_detection.Unipolar_mode;
                        this.page.Bipolar_modevalue = response.detail_data.fixed_detection.Bipolar_mode;
                        this.form.charge_result = response.detail_data.fixed_detection.charge_result;
                        this.form.lookslike = response.detail_data.fixed_detection.lookslike;
                        if(response.detail_data.fixed_detection.lookslike == 2){
                            this.show_lookslike_desc = true;
                            this.form.lookslike_desc = response.detail_data.fixed_detection.lookslike_desc;
                        }
                        this.form.internal_discharge = response.detail_data.fixed_detection.internal_discharge;
                        this.form.charge = response.detail_data.fixed_detection.charge;
                        this.form.total_desc = response.detail_data.fixed_detection.total_desc;
                    }
                    if (response.file_data) {
                        for (let i in response.file_data.nameplate) {
                            this.form.nameplate_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.nameplate[i].file_url,
                                id: response.file_data.nameplate[i].file_id
                            });
                        }
                        for (let i in response.file_data.instrument_view) {
                            this.form.instrument_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.instrument_view[i].file_url,
                                id: response.file_data.instrument_view[i].file_id
                            });
                        }

                    }
                    this.page.Unipolar_cutting = this.page.Unipolar_cutting + response.tolerance.Unipolar_cutting;
                    this.page.Unipolar_coagulation = this.page.Unipolar_coagulation + response.tolerance.Unipolar_coagulation;
                    this.page.Bipolar_resection = this.page.Bipolar_resection + response.tolerance.Bipolar_resection;
                    this.page.Bipolar_coagulation = this.page.Bipolar_coagulation + response.tolerance.Bipolar_coagulation;
                    this.page.Unipolar_mode = this.page.Unipolar_mode + response.tolerance.Unipolar_mode;
                    this.page.Bipolar_mode = this.page.Bipolar_mode + response.tolerance.Bipolar_mode;
                    this.page.is_display = 1;
                });
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.qsid);
        }
    }
</script>
<style scoped lang="scss">
    .background {
        background: rgba(69, 90, 100, 0.1);
    }

    ::v-deep .text_length .van-cell__title {
        width: 95px
    }

    .van-row{
        ::v-deep .van-field__label {
            width: 60%;
        }
    }


    ::v-deep .long .van-field__label {
        width: 70%
    }
    .card-header{margin-top: 20px;}
    .mp{
        margin-top: 20px;
        padding-top: 10px;
        background: #fff;
    }
</style>
