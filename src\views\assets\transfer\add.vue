<template>
    <div class="con_detail" v-if="page.is_display===1">
        <div class="card-header">
            <h2 class="detailTitle">设备基本信息</h2>
            <div class="bl1px"></div>
        </div>
        <AssetsInfo :info='info'/>
        <div v-if="page.show_form === 1" style="margin-bottom: 20px;">
            <div class="card-header">
                <h2 class="detailTitle">审批记录</h2>
                <div class="bl1px"></div>
            </div>
            <Approves :info='info.approves'/>
        </div>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">转科申请表单</h2>
        </div>

        <van-form ref="scrapForm" @submit="onSubmit">
            <van-cell-group>
                <van-field
                        readonly
                        clickable
                        required
                        :rules="[{ required: true, message: '' }]"
                        name="transferdate"
                        :value="form.transferdate"
                        label="转科日期"
                        placeholder="点击转科日期"
                        @click="page.showSelectTransfer = true"
                />
                <van-popup v-model="page.showSelectTransfer" position="bottom">
                    <van-datetime-picker
                            v-model="page.currentDate"
                            type="date"
                            title="选择年月日"
                            @confirm="onConfirmTransferDate"
                            @cancel="page.showSelectTransfer = false"
                    />
                </van-popup>
                <van-field
                        readonly
                        clickable
                        required
                        :error="false"
                        :rules="[{ required: true, message: '选择转入科室' }]"
                        name="department"
                        :value="page.tranin_depart_name"
                        label="转入科室"
                        placeholder="选择转入科室"
                        @click="page.showSelectDepartment = true"
                />
                <van-popup v-model="page.showSelectDepartment" position="bottom">
                    <van-field v-model="page.departmentKeyword" label="搜索" placeholder="请输入科室名称"/>
                    <van-picker
                            show-toolbar
                            :columns="this.departmentFilter"
                            @confirm="onConfirmDepartment"
                            @cancel="page.showSelectDepartment = false"
                    />
                </van-popup>
                <van-field
                        name="docnum"
                        v-model="form.docnum"
                        label="转科文号"
                        placeholder="请输入转科文号"
                />
                <van-field
                        name="tranreason"
                        v-model="form.tranreason"
                        label="转科原因"
                        rows="3"
                        maxlength="60"
                        show-word-limit
                        placeholder="请输入转科原因"
                />
                <van-field
                        name="applicant_user"
                        label="申请人"
                        :value="page.username"
                        readonly
                />
                <van-field
                        name="applicant_time"
                        label="申请时间"
                        :value="this.nowTime()"
                        readonly
                />
            </van-cell-group>
            <div style="margin: 16px;">
                <van-button color="#009688" block type="warning" native-type="button" @click="changeFormType('edit')" v-show="form.atid>0">申请重审</van-button>
                <van-button block type="danger" native-type="button" @click="changeFormType('end')" v-show="form.atid >0">结束进程</van-button>
                <van-button color="#009688" block type="info" native-type="button" @click="changeFormType('apply')" v-show="form.atid == ''">确认并提交</van-button>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Divider, Cell, CellGroup, Icon, Field, Form, Button, Tab, Tabs, DatetimePicker, Popup, Picker, Toast, Loading,Notify} from 'vant';
    import {getInfo, submit} from "@/api/assets/transfer/add";
    import AssetsInfo from "@/components/Assetsinfo";
    import Approves from '@/components/Approves';

    export default {
        name: 'addTransfer',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [DatetimePicker.name]: DatetimePicker,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [Loading.name]: Loading,
            AssetsInfo,
            Approves,
        },
        data() {
            return {
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    scrapInfo: [],//是否有历史转科信息
                    approves: [],
                },
                form: {
                    atid: 0,
                    docnum: '',//转科文号
                    tranreason: '',//转科原因
                    transferdate: '',//转科日期
                    departmentid: '',
                    formType: 'apply',//apply 转科申请 edit 转科重审 end 转科结束进程
                },
                page: {
                    is_display: 0,
                    show_form: 0,
                    username: '',
                    showSelectTransfer: false,
                    showSelectDepartment: false,
                    departments: '',//转入科室列表
                    tranin_depart_name: '',//转入科室
                    departmentColumns: [],//科室选项
                    departmentKeyword: '',//科室搜索栏
                    currentDate: new Date(),//当前日期
                },

            }
        },
        computed: {
            departmentFilter() {
                return this.page.departmentColumns.filter(val => {
                    return val.indexOf(this.page.departmentKeyword.trim()) >= 0
                })
            }
        },
        methods: {
            //选择转科日期
            onConfirmTransferDate(time) {
                this.form.transferdate = this.timeFormat(time);
                this.page.showSelectTransfer = false;
            },
            //选择科室选项
            onConfirmDepartment(value) {
                this.page.depart = value;
                for (var i in this.departments) {
                    if (this.departments[i].title === value) {
                        this.form.departmentid = this.departments[i].value;
                        this.page.tranin_depart_name = this.departments[i].title;
                    }
                }
                this.page.showSelectDepartment = false;
            },
            validator(val) {
                if (this.form.atid > 0) {
                    return true;
                } else {
                    if (typeof val === 'undefined' || val.replace(/(^\s*)|(\s*$)/g, "") === '') {
                        this.scrap_reason = '';
                        return false;
                    }
                }
            },
            getInfo(assnum,action) {
                let params = {assnum: assnum,action:action};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.page.username = response.username;
                    this.form.transferdate = response.transferdate;
                    this.departments = response.departments;
                    this.page.departmentColumns = response.departmentColumns;
                    if (response.transinfo === undefined) {
                        response.transinfo = [];
                    }else{
                        this.form.departmentid = response.transinfo.tranin_departid;
                        this.form.atid = response.transinfo.atid;
                        this.page.tranin_depart_name = response.transinfo.tranin_depart_name;
                        this.form.docnum = response.transinfo.tran_docnum;
                        this.form.tranreason = response.transinfo.tran_reason;
                        this.page.show_form = response.show_form;

                    }
                    this.page.is_display = 1;
                })
            },
            onSubmit(values) {
                switch (this.form.formType) {
                    case "apply":
                        //转科申请
                        values.assids = this.info.assid;
                        values.scrapnum = this.info.scrapnum;
                        values.type = this.form.formType;
                        break;
                    case "edit":
                        //转科重审
                        values.atid = this.atid;
                        values.type = this.form.formType;
                        break;
                    case "end":
                        //转科结束进程
                        values.atid = this.atid;
                        values.type = this.form.formType;
                        break;
                }
                values.traOutId = this.info.departid;
                values.departid = this.form.departmentid;
                values.atid = this.form.atid;
                if(!values.departid){
                    Notify('请选择转入科室');
                    return false;
                }
                //发送请求
                let _this = this;
                submit(Qs.stringify(values)).then(response => {
                    if(response.status === 1){
                        Toast({
                            type:'success',//失败fail
                            duration:2000,//2秒
                            message: response.msg,
                            icon:'success',//失败cross
                            forbidClick:true,//是否禁止背景点击，避免重复提交
                            onClose(){
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Transfer/getList');
                            }
                        });
                    }
                })
            },
            changeFormType(type) {
                this.form.formType = type;//改变提交表单的formType
                this.$refs.scrapForm.submit();//执行提交
            },
            nowTime() {
                var d = new Date();
                var year = d.getFullYear();
                var month = change(d.getMonth() + 1);
                var day = change(d.getDate());
                var hour = change(d.getHours());
                var minute = change(d.getMinutes());

                // var second=change(d.getSeconds());
                function change(t) {
                    if (t < 10) {
                        return "0" + t;
                    } else {
                        return t;
                    }
                }

                return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
            },
            timeFormat(time) { // 时间格式化
                let year = time.getFullYear();
                let month = time.getMonth() + 1;
                let day = time.getDate();
                month = month < 10 ? '0'+month : month;
                day = day < 10 ? '0'+day : day;
                return year + '-' + month + '-' + day
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.assnum,this.$route.query.action);
        }
    }
</script>

<style scoped lang="scss">
    .van-button {
        margin-top: 10px;
    }
</style>
