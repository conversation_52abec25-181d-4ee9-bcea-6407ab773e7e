<template>
  <div class="con_detail">
    <div class="card-header">
      <h2 class="detailTitle">计划基本信息</h2>
      <div class="bl1px"></div>
    </div>

    <van-cell-group>
      <van-cell title="状态" :value="info.inventory_plan.inventory_plan_status_name"/>
      <van-cell title="盘点名称" :value="info.inventory_plan.inventory_plan_name"/>
      <van-cell title="盘点单号" :value="info.inventory_plan.inventory_plan_no"/>
    </van-cell-group>

    <div style="display: flex; justify-content: space-around;margin-bottom: 8px">

      <van-button style="height: 30px;" type="info" @click="sm" v-if="jugdePlatform()">扫码盘点</van-button>
      <van-button style="height: 30px;" type="info" @click="yijian">一键盘点</van-button>
    </div>


    <van-cell-group style="padding:10px 16px;font-size: 14px;">
      <van-row gutter="20" style="line-height: 40px;">
        <van-col>计划数：<span>{{ this.info.inventory_plan_assets.length }}</span></van-col>
      </van-row>
      <van-row gutter="20" style="line-height: 40px;">
        <van-col span="6">已盘数: <span>{{ this.finishList.data.length }}</span></van-col>
        <van-col span="6">正常: <span
            style="color: green;">{{ this.finishList.data.filter(item => item.inventory_status === '1').length }}</span>
        </van-col>
        <van-col span="6">异常: <span
            style="color: red;">{{ this.finishList.data.filter(item => item.inventory_status === '2').length }}</span>
        </van-col>
      </van-row>
      <van-row gutter="20" style="line-height: 40px;">
        <van-col>未盘数：<span style="color: orange;">{{ this.undoneList.data.length }}</span></van-col>
      </van-row>
    </van-cell-group>

    <van-cell-group style="text-align:center">
      <!--      <van-button type="primary" style="border: none;background-color: rgba(0, 155, 137, 1);width: 70%;margin: 10px 0;"-->
      <!--                  @click="savePlan">暂时保存-->
      <!--      </van-button>-->
      <van-button type="info" style="border: none;background-color: rgba(255, 180, 3, 1);width: 80%;margin: 10px 0;"
                  @click="endPlan">结束盘点
      </van-button>
    </van-cell-group>


    <div class="card-header">
      <h2 class="detailTitle">未盘设备列表 ({{ this.undoneList.data.length }}) 台</h2>
      <div class="bl1px"></div>
    </div>
    <van-card style="padding: 0" v-for="item in undoneList.data" :key="item.inventory_plan_assets_id">
      <template #desc>
        <van-cell-group>
          <van-cell title="设备编号" :value="item.assetnum"/>
          <van-cell title="设备名称" :value="item.assets"/>
          <van-cell title="设备使用位置" :value="item.address"/>
          <van-cell title="使用科室" :value="item.department"/>
          <van-cell title="是否计划内" :value="item.is_plan === '1' ? '是' : '否'"/>
          <van-cell title="实盘状态" value="未盘"/>
          <van-cell title="原因" :value="item.reason"/>
          <van-cell title="处理结果" :value="item.result"/>
          <van-cell>
            <van-button type="info" block size="small" class="detail-button"
                        :to="{ path: $store.getters.moduleName+'/Inventory/assetDeal', query: { inventory_plan_id: item.inventory_plan_id,assetnum:item.assetnum}}">
              手工处理
            </van-button>
          </van-cell>
        </van-cell-group>
      </template>
    </van-card>


    <div class="card-header">
      <h2 class="detailTitle">已盘设备列表 ({{ this.finishList.data.length }}) 台</h2>
      <div class="bl1px"></div>
    </div>
    <van-card style="padding: 0" v-for="item in finishList.data" :key="item.inventory_plan_assets_id">
      <template #desc>
        <van-cell-group>
          <van-cell title="设备编号" :value="item.assetnum"/>
          <van-cell title="设备名称" :value="item.assets"/>
          <van-cell title="设备使用位置" :value="item.address"/>
          <van-cell title="使用科室" :value="item.department"/>
          <van-cell title="是否计划内" :value="item.is_plan === '1' ? '是' : '否'"/>
          <van-cell title="实盘状态" :value="item.inventory_status === '1' ? '正常' : '异常'"/>
          <van-cell title="原因" :value="item.reason"/>
          <van-cell title="处理结果" :value="item.result"/>
          <!--          <van-cell>-->
          <!--            <van-button type="info" block size="small" class="detail-button"-->
          <!--                        :to="{ path: $store.getters.moduleName+'/Inventory/assetDeal', query: { inventory_plan_assets_id: item.inventory_plan_assets_id}}">-->
          <!--              手工处理-->
          <!--            </van-button>-->
          <!--          </van-cell>-->
        </van-cell-group>
      </template>
    </van-card>


  </div>
</template>

<script>
import {
  Divider, Cell, CellGroup, Icon, Field, Form, Button, Tab, Tabs, Loading, Popup, Card,
  Picker, DatetimePicker, Step, Steps, ImagePreview, Col, Row, Notify, List, Pagination,
  Dialog, Toast
} from 'vant';
import {
  showInventoryPlan,
  saveOrEndInventoryPlan,
  clientGetUndoneList,
  clientGetFinishList, getInventoryList, handleAllAsset, clientHandDeal, getAssetInfo
} from "@/api/common/inventory";
import RepairInfo from "@/components/RepairInfo";
import Approves from '@/components/Approves';
import wechatUtil from "@/utils/wechatUtil";
import {getRegexAssnum} from "@/utils/regex";

export default {
  name: 'check',
  components: {
    [Divider.name]: Divider,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [Form.name]: Form,
    [Button.name]: Button,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [Loading.name]: Loading,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [Step.name]: Step,
    [ImagePreview.name]: ImagePreview,
    [Steps.name]: Steps,
    [DatetimePicker.name]: DatetimePicker,
    [Col.name]: Col,
    [Row.name]: Row,
    [Notify.name]: Notify,
    [List.name]: List,
    [Pagination.name]: Pagination,
    [Card.name]: Card,
    [Dialog.name]: Dialog,
    [Toast.name]: Toast,
    RepairInfo,
    Approves,
  },
  data() {
    return {
      info: {
        app: [],
        inventory_plan: {},
        inventory_plan_assets: {},
        status: 1
      },
      form: {},
      finishList: {
        data: [],
        total: 0,
        page: 1,
        loading: true,
        finished: false,
        limit: 5
      },
      undoneList: {
        data: [],
        total: 0,
        page: 1,
        limit: 5,
        loading: false,
        finished: true,
      },
      inventory_plan_assets_id: null,
      inventory_plan_id: null
    }
  },
  methods: {
    jugdePlatform() {
      //   return (process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX)
      return true
    },
    getInfo(inventory_plan_id) {
      let params = {inventory_plan_id: inventory_plan_id};
      showInventoryPlan(params).then(response => {
        this.info = response;
        this.undoneList.data = this.info.inventory_plan_assets.filter(item => item.inventory_status === "0")
        this.finishList.data = this.info.inventory_plan_assets.filter(item => item.inventory_status !== "0")
      })
    },
    savePlan() {
      let params = {
        inventory_plan_id: this.$route.query.inventory_plan_id,
        operate: "save",
        inventory_plan_assets: this.form
      }
      saveOrEndInventoryPlan(params).then(response => {
        Notify({type: 'success', message: response.msg});
      })
    },
    endPlan() {
      let params = {
        inventory_plan_id: this.$route.query.inventory_plan_id,
        operate: "end",
        inventory_plan_assets: this.undoneList.data
      }
      saveOrEndInventoryPlan(params).then(response => {
        Notify({type: 'success', message: response.msg});
        this.$router.go(-1);
      })
    },
    yijian() {
      if (this.undoneList.data.length === 0) {
        Notify({type: 'danger', message: '没有未盘设备'});
        return false
      }
      let params = {
        inventory_plan_id: this.$route.query.inventory_plan_id,
        inventory_plan_assets: this.undoneList.data,
        operate:'save'
      }
      saveOrEndInventoryPlan(params).then(response => {
        Notify({type: 'success', message: response.msg});
        Object.assign(this.$data, this.$options.data()); // 重置data
        this.getInfo(this.$route.query.inventory_plan_id)
      })
    },
    sm() {
      // 微信版本
      wechatUtil.init([
        'scanQRCode',//扫一扫
      ]).then((wx) => {
        // 这里写微信的接口
        const vm = this
        wx.scanQRCode({
          needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
          scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
          async success(res) {
            if (!res.resultStr) {
              Toast.fail("该二维码缺少设备信息")
              setTimeout(() => {
                vm.sm(); // 盘点成功后，重新调用sm函数
              }, 2000)
              return false
            }

            // let assnum = res.resultStr;// 当needResult 为 1 时，扫码返回的结果
            const assnum = await getRegexAssnum(res.resultStr)

            console.log(vm.info)
            let value = vm.info.inventory_plan_assets.find(item => item.assetnum === assnum || item.assorignum === assnum || item.assorignum_spare === assnum )
            // 不在计划内
            if (!value) {
              // 尝试去找一下是否有这个设备在系统里面
              const res = await getAssetInfo(assnum)
              if (!res.data){
                Dialog({message: `查找不到编码为【${assnum}】的设备信息`,confirmButtonText:"关闭",beforeClose: (action, done) => {
                  if (action === 'confirm') {
                    done();
                    vm.sm(); // 盘点成功后，重新调用sm函数
                  }
                }});
                return false
              }
              Dialog.confirm({
                title: '新增盘点设备？',
                message: `是否对非盘点计划内的设备（${res.data.assnum}，${res.data.assets}，${res.data.department}）进行新增？`,
              }).then(() => {
                vm.headDeal({
                  status: 1,
                  inventory_user: localStorage.getItem('uname'),
                  inventory_plan_id: vm.$route.query.inventory_plan_id,
                  assetnum: res.data.assnum,
                  inventory_plan_assets_id: null
                })
                // vm.$router.go(0)
              }).catch(() => {
                  vm.sm(); // 盘点成功后，重新调用sm函数
                return false
              });
              // setTimeout(() => {
              //   vm.sm(); // 盘点成功后，重新调用sm函数
              // }, 2000)
              return false
            }
            if (value.inventory_status !== '0') {
              Toast.fail('该设备已盘点，请勿重复盘点');
              setTimeout(() => {
                vm.sm(); // 盘点成功后，重新调用sm函数
              }, 2000)
              return false
            }
            vm.headDeal({
              status: 1,
              inventory_user: localStorage.getItem('uname'),
              inventory_plan_id: vm.$route.query.inventory_plan_id,
              assetnum: value['assetnum'],
              inventory_plan_assets_id: value.inventory_plan_assets_id
            })
            // vm.$router.go(0)
          }
        });
      })
    },
    headDeal(params) {
      const vm = this; // 保持对Vue实例的引用
      clientHandDeal(params).then(response => {
        if (response.status === 1) {
          Toast('盘点成功');
          Object.assign(vm.$data, vm.$options.data()); // 重置data
          vm.getInfo(this.$route.query.inventory_plan_id);
          vm.sm(); // 盘点成功后，重新调用sm函数
        } else {
          Toast('盘点失败');
        }
      })
    }
  }, mounted() {
    this.getInfo(this.$route.query.inventory_plan_id);
    // this.getUndoneList()
    // this.getFinishList()
  }
}
</script>

<style scoped lang="scss">
.van-button {
  margin-top: 10px;
}

::v-deep .gray {
  color: #666;
  margin-left: 10px;
}

::v-deep .red {
  color: #FF5722
}

.van-tabs--line .van-tabs__wrap {
  height: 0;
}
</style>
