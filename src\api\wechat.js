import request from '@/utils/request';

export function use_wx() {
    return request({
        url: '/Notin/wxStatus'
    })
}
export function getUserInfo() {
    return request({
        url: '/Notin/getUserInfo'
    })
}
export function getSignature() {
    return request({
        url: '/Notin/getSignature',
        method: 'post',
    })
}
export function downRecord(data) {
    return request({
        url: '/Notin/wxRecordDown',
        method: 'post',
        data
    })
}
export function uploadImg(data) {
    return request({
        url: '/Repair/addRepair',
        method: 'post',
        data
    })
}
