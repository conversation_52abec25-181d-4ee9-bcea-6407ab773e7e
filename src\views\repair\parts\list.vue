<template>
    <div class="list">
        <van-search
                v-model="keyword"
                shape="round"
                placeholder="请输入搜索关键词"
                @search="onSearch"
        />
        <van-row>
            <van-col span="18">
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        配件列表 <span class="num"> {{total}} </span> 款
                    </div>
                </div>
            </van-col>
            <van-col span="6">
                <Sort @order="order" :option="option"/>
            </van-col>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="refreshList"
        >
            <van-card :thumb="item.pic_url" v-for="item in list" :key="item.assid">
                <template #desc>
                    <ul>
                        <li class="list_li_width">
                            <span class="detailText">配件名称：</span>
                            <span class="text">{{item.parts}}</span>
                        </li>
                        <li>
                            <span class="detailText">配件型号：</span>
                            <span class="text">{{item.parts_model}}</span>
                        </li>
                        <li class="list_li_width">
                            <span class="detailText">库存数量：</span>
                            <span class="text">{{item.stock_nums}}{{item.unit}}{{item.need_nums}}</span>
                        </li>
                        <li class="list_li_width">
                            <span class="detailText">配件单价：</span>
                            <span class="text">{{item.price}}</span>
                        </li>
                        <li class="list_li_width">
                            <span class="detailText">供 应 商：</span>
                            <span class="text">{{item.supplier_name}}</span>
                        </li>
                    </ul>
                </template>
                <template #footer>
                    <van-button v-if="parseInt(item.type) === 1" type="warning" block size="small" class="detail-button" :to="{ path: $store.getters.moduleName+'/RepairParts/partsInWare', query: { repid: item.repid,sname:item.supplier_name,sid:item.supplier_id,stock:item.stock_nums,parts:item.parts,model:item.parts_model,price:item.price }}">补充库存</van-button>
                    <van-button v-else-if="parseInt(item.type) === 2" type="info" block size="small" class="detail-button" :to="{ path: $store.getters.moduleName+'/RepairParts/partsOutWare', query: { repid: item.repid}}">有待出库</van-button>
                </template>
            </van-card>
        </van-list>
        <ScrollTop/>
    </div>
</template>

<script>
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem} from "vant";
    import {partStockList} from "@/api/repair/list";
    import ScrollTop from "@/components/ScrollTop";
    import Sort from "@/components/Sort";

    export default {
        name: 'partStockList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            ScrollTop,
            Sort
        },
        data() {
            return {
                list: [],
                option: [
                    {text: '按配件价格（降序）', value: 'price-DESC'},
                    {text: '按配件库存（降序）', value: 'stock_nums-DESC'},
                    {text: '按配件名称（降序）', value: 'parts-DESC'},
                    {text: '按配件价格（升序）', value: 'price-asc'},
                    {text: '按配件库存（升序）', value: 'stock_nums-asc'},
                    {text: '按配件名称（升序）', value: 'parts-asc'},
                ],
                keyword: '',
                orderValue: '',
                loading: false,
                finished: false,
                total: 0,
                //全局列表搜索条件
                where: {
                    page: 1,
                }
            };
        },
        methods: {
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                setTimeout(() => {
                    partStockList(this.where).then(response => {
                        for (let x in response.rows) {
                            if (Object.prototype.hasOwnProperty.call(response.rows, x)) {
                                this.list.push(response.rows[x]);
                            }
                        }
                        if (response.total!=0) {
                             this.total = response.total;
                        }
                        //数据获取到了 停止加载
                        this.loading = false;
                        this.where.page = this.where.page + 1;
                        //全部加载了 后台已经没有数据可反 就置为完成状态
                        if (this.list.length >= this.total) {
                            this.finished = true;
                        }
                    })
                }, 1000);
            },
            onSearch(keyword) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, search: keyword};
                this.refreshList();
            },
            order(value) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, sort: value.split('-')[0], order: value.split('-')[1]};
                this.refreshList();
            },
            getcatid(id) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, catid: id};
                this.refreshList();
            },
            getdepartid(id) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, departid: id};
                this.refreshList();
            }
        }
    }
</script>

<style scoped lang="scss">

</style>
