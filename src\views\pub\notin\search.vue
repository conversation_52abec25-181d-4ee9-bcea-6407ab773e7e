<template>
    <div class="con_detail">
        <div class="qj_search">
            <div class="search-div">
                <div class="search_title">
                    全局搜索
                </div>
                <div class="search-input">
                    <van-search v-model="keyword" placeholder="请输入设备名称、编号、科室" @search="onSearch"/>
                </div>
                <div class="tips">搜索结果：<span style="color:red;">{{total}}</span> 条</div>
            </div>
        </div>
        <div class="conti">
            <van-tabbar v-model="active">
                <van-tabbar-item @click="change_type('')">
                    全部
                    <template #icon>
                        <van-icon class-prefix="wx-icon" name="quanbu" />
                    </template>
                </van-tabbar-item>
                <van-tabbar-item @click="change_type('assets')">
                    设备名称
                    <template #icon>
                        <van-icon class-prefix="wx-icon" name="shebeimingcheng" />
                    </template>
                </van-tabbar-item>
                <van-tabbar-item @click="change_type('assnum')">
                    设备编码
                    <template #icon>
                        <van-icon class-prefix="wx-icon" name="bianma" />
                    </template>
                </van-tabbar-item>
                <van-tabbar-item @click="change_type('department')">
                    所属科室
                    <template #icon>
                        <van-icon class-prefix="wx-icon" name="keshi" />
                    </template>
                </van-tabbar-item>
            </van-tabbar>
        </div>
        <div class="res">
            <van-list
                    v-model="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    offset="60"
                    @load="refreshList"
            >
                <van-cell v-for="(item,index) in list" :key="index" :title="item.assets" is-link :value="item.assnum" :to="{ name: 'todo', query: { assnum: item.assnum }}" />
            </van-list>
            <ScrollTop/>
        </div>
    </div>
</template>

<script>
    import {Search,List,Cell,Tabbar, TabbarItem,Icon } from 'vant';
    import ScrollTop from "@/components/ScrollTop";
    import {search} from "@/api/index/index";

    export default {
        name: "search",
        components:{
            [Search.name]: Search,
            [List.name]: List,
            [Cell.name]: Cell,
            [Tabbar.name]: Tabbar,
            [TabbarItem.name]: TabbarItem,
            [Icon.name]: Icon,
            ScrollTop
        },
        data(){
            return{
                active: 0,
                list: [],
                loading: false,
                finished: false,
                total: 0,
                keyword:'',
                type:'',
                //全局列表搜索条件
                where: {
                    page: 1,
                    limit: 20
                }
            }
        },
        methods:{
            change_type($target){
                //重置列表条件
                this.list = [];//清空列表
                //重置为第一页
                this.where.page = 1;
                //重置表格为加载状态
                this.loading = true;
                this.finished = false;

                switch ($target) {
                    case 'assets':
                        this.where.type = 'assets';
                        break;
                    case 'assnum':
                        this.where.type = 'assnum';
                        break;
                    case 'department':
                        this.where.type = 'department';
                        break;
                    default:
                        this.where.type = '';
                        break;
                }
                this.refreshList(this.where.keyword);
            },
            refreshList(values) {
                // 异步更新数据
                if(values){
                    this.where.keyword = values;
                    this.$route.query.keyword = values;
                }else{
                    this.keyword = this.$route.query.keyword;
                    this.where.keyword = this.$route.query.keyword;
                }
                setTimeout(() => {
                    search(this.where).then(res => {
                        if (res.total === 0) {
                            this.finished = true;
                        }
                        //判断数据键值是否存在
                        if (Object.prototype.hasOwnProperty.call(res, 'rows')) {
                            this.list.push(...res.rows);
                        }
                        //仅第一页赋值总数
                        if (this.where.page === 1) {
                            this.total = res.total;
                        }
                        //数据获取到了 停止加载
                        this.loading = false;
                        //全部加载了 且当前列表数量大于总数 设置完成状态
                        if (this.list.length >= this.total) {
                            this.finished = true;
                        }
                        //页数加1
                        this.where.page++;
                    });
                }, 100);
            },
            onSearch(keyword) {
                 this.resetWhere();
                 this.finished = false;
                 //重新加搜索条件
                 this.where.keyword = keyword;
                 this.refreshList(this.where.keyword);
            },
            //重置列表条件
            resetWhere(clearWhere = true) {
                this.list = [];//清空列表
                if (clearWhere) {
                    //清空搜索条件
                    this.where = {};
                }
                //重置为第一页
                this.where.page = 1;
                this.where.limit = 20;
                //重置表格为加载状态
                this.loading = true;
                this.finished = false;
            },
        }
    }
</script>

<style scoped lang="scss">
    .qj_search {
        height: 160px;
        background-size: cover;
        position: relative;
        background-image: url("../../../assets/images/index/bg.png");

        .search_title{
            color:#1989FA;
            text-align: center;
            margin:0.3rem auto;
            font-size: 20px;
        }

        /*主页搜索条 S*/
        .search-div {
            padding: 0.75rem;
        }

        .search-input {
            width: 100%;
            height: 36px;
            display: inline-block;
            margin-top: 0.3rem;

            .van-search {
                padding: 0;
                border-radius: 5px;
            }

            .van-search__content {
                background-color: #fff;
            }
        }
        .tips{
            margin-top: 25px;
            color:#a9a9a9;
        }
    }
    .conti{
        margin: 0.6rem 0;
        .van-tabbar--fixed{
            position: unset;
        }
    }
</style>
