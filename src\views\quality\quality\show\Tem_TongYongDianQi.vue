<template>
    <div class="con_detail" v-if="page.is_display===1">
        <Quality :info="info"/>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">质控检测明细（模板：{{page.templatename}}）</h2>
        </div>

        <van-form>
            <van-field label="外观功能" :value="info.detailInfo.exterior=='1'?'符合':'不符合'" readonly/>
            <van-cell-group>
                <van-cell title="保护接地阻抗(mΩ)" class="background"/>
                <van-cell title="允许值:≤200mΩ" :value="'测量值：'+info.detailInfo.preset_detection.protection['0']"/>
                <van-cell title="测量结论："
                          :value="page.detail_result.protection==1?'符合':page.detail_result.protection==2?'不符合':'未知'"/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="绝缘阻抗(电源—外壳)(MΩ)" class="background"/>
                <van-cell title="允许值:≥10mΩ" :value="'测量值：'+info.detailInfo.preset_detection.insulation['0']"/>
                <van-cell title="测量结论："
                          :value="page.detail_result.insulation==1?'符合':page.detail_result.insulation==2?'不符合':'未知'"/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="对地漏电流(正常状态)(μA)" class="background"/>
                <van-cell title="允许值:≤200mΩ" :value="'测量值：'+info.detailInfo.preset_detection.earthleakagecurrent['0']"/>
                <van-cell title="测量结论："
                          :value="page.detail_result.earthleakagecurrent==1?'符合':page.detail_result.earthleakagecurrent==2?'不符合':'未知'"/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="外壳漏电流(正常状态)(μA)" class="background"/>
                <van-cell title="允许值:≤500μA" :value="'测量值：'+info.detailInfo.preset_detection.Case_normal['0']"/>
                <van-cell title="测量结论："
                          :value="page.detail_result.Case_normal==1?'符合':page.detail_result.Case_normal==2?'不符合':'未知'"/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="其他信息" class="background"/>
                <van-cell v-if="info.detailInfo.fixed_detection.App_types==1" title="应用类型：" value="B型BF型"/>
                <van-cell v-else title="应用类型：" value="CF型"/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="外壳漏电流(地线断开)(μA)" class="background"/>
                <van-cell v-if="info.detailInfo.fixed_detection.App_types==1" title="允许值:≤500μA"
                          :value="'测量值：'+info.detailInfo.preset_detection.aid_abnormal['0']"/>
                <van-cell v-else title="允许值:≤50μA" :value="'测量值：'+info.detailInfo.preset_detection.aid_abnormal['0']"/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="患者漏电流(正常状态)(μA)" class="background"/>
                <van-cell v-if="info.detailInfo.fixed_detection.App_types==1" title="允许值:≤100μA"
                          :value="'测量值：'+info.detailInfo.preset_detection.patient_normal['0']"/>
                <van-cell v-else title="允许值:≤10μA"
                          :value="'测量值：'+info.detailInfo.preset_detection.patient_normal['0']"/>
                <van-cell title="测量结论："
                          :value="page.detail_result.patient_normal==1?'符合':page.detail_result.patient_normal==2?'不符合':'未知'"/>
            </van-cell-group>


            <van-cell-group>
                <van-cell title="患者漏电流(地线断开)(μA)" class="background"/>
                <van-cell v-if="info.detailInfo.fixed_detection.App_types==1" title="允许值:≤500μA"
                          :value="'测量值：'+info.detailInfo.preset_detection.patient_abnormal['0']"/>
                <van-cell v-else title="允许值:≤50μA"
                          :value="'测量值：'+info.detailInfo.preset_detection.patient_abnormal['0']"/>
                <van-cell title="测量结论："
                          :value="page.detail_result.patient_abnormal==1?'符合':page.detail_result.patient_abnormal==2?'不符合':'未知'"/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="外壳漏电流(正常状态)(μA)" class="background"/>
                <van-cell v-if="info.detailInfo.fixed_detection.App_types==1" title="允许值:≤100μA"
                          :value="'测量值：'+info.detailInfo.preset_detection.aid_normal['0']"/>
                <van-cell v-else title="允许值:≤10μA" :value="'测量值：'+info.detailInfo.preset_detection.aid_normal['0']"/>
                <van-cell title="测量结论："
                          :value="page.detail_result.aid_normal==1?'符合':page.detail_result.aid_normal==2?'不符合':'未知'"/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="外壳漏电流(地线断开)(μA)" class="background"/>
                <van-cell v-if="info.detailInfo.fixed_detection.App_types==1" title="允许值:≤500μA"
                          :value="'测量值：'+info.detailInfo.preset_detection.aid_abnormal['0']"/>
                <van-cell v-else title="允许值:≤50μA" :value="'测量值：'+info.detailInfo.preset_detection.aid_abnormal['0']"/>
                <van-cell title="测量结论："
                          :value="page.detail_result.aid_abnormal==1?'符合':page.detail_result.aid_abnormal==2?'不符合':'未知'"/>
            </van-cell-group>

            <div class="mp">
                <van-divider v-show="page.nameplate.length>0">设备铭牌照片</van-divider>
                <van-swipe class="image-swipe" :autoplay="3000" indicator-color="white" height="220"
                           v-show="page.nameplate.length>0" @change="onChange">
                    <van-swipe-item v-for="(item,index) in page.nameplate" :key="index" @click="showImage">
                        <van-image width="100%" height="220" :src="item" fit="fill"/>
                    </van-swipe-item>
                </van-swipe>

                <van-divider v-show="page.instrument_view.length>0">检测仪器视图照片</van-divider>
                <van-swipe class="image-swipe" :autoplay="3000" indicator-color="white" height="220"
                           v-show="page.instrument_view.length>0" @change="onChange">
                    <van-swipe-item v-for="(item,index) in page.instrument_view" :key="index" @click="showImg">
                        <van-image width="100%" height="220" :src="item" fit="fill"/>
                    </van-swipe-item>
                </van-swipe>

                <van-field label="检测结果" :value="info.detailInfo.result=='1'?'合格':'不合格'" readonly/>
                <van-cell v-show="info.detailInfo.remark" :title="'检测备注：'+info.detailInfo.remark"/>
                <van-divider v-show="page.file_data">质控报告</van-divider>
                <van-image v-show="page.file_data" width="100%" height="100%" :src="page.file_data"/>
                <van-row type="flex" justify="center">
                    <van-uploader max-size="10485760" multiple :after-read="afterRead">
                        <van-button type="primary" native-type="button">上传质控报告</van-button>
                    </van-uploader>
                </van-row>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>
<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Row,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Toast,
        Loading,
        Uploader,
        ImagePreview,
        Image as VanImage,
        Swipe,
        SwipeItem
    } from 'vant';
    import {getInfo, submit} from "@/api/quality/show";
    import Quality from "@/components/Quality";

    export default {
        name: 'ChuChanYi',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Row.name]: Row,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Uploader.name]: Uploader,
            [Tab.name]: Tab,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Tabs.name]: Tabs,
            [VanImage.name]: VanImage,
            [Loading.name]: Loading,
            Quality,
        },
        data() {
            return {
                info: {
                    asInfo: [],
                    qsInfo: [],
                    detailInfo: [],
                },
                page: {
                    heartRate: '心率（次/min）：最大允差：',
                    energesis: '释放能量（J）：最大允差：',
                    file_data: '',
                    setting: {
                        heartRate: [],
                    },
                    templatename: '',
                    nameplate: [],
                    instrument_view: [],
                    detail_result: [],
                    positions: 0,
                    is_display: 0,
                },
                form: {},
            }
        },
        methods: {
            onChange(index) {
                this.page.positions = index;
            },
            showImage() {
                ImagePreview({
                    images: this.page.nameplate,
                    startPosition: this.page.positions,
                });
            },
            showImg() {
                ImagePreview({
                    images: this.page.instrument_view,
                    startPosition: this.page.positions,
                });
            },
            afterRead(file) {
                let values = [];
                values.qsid = this.$route.query.qsid;
                values.base64 = file.content;
                values.action = 'upload';
                values.fileName = file.file.name;
                submit(Qs.stringify(values)).then(response => {
                    this.page.file_data = process.env.VUE_APP_BASE_PROXY_URL + response.path;
                    Toast.success(response.msg);
                    /*this.$router.push('/');*/
                })

            },
            getInfo(qsid) {
                let params = {qsid: qsid};
                getInfo(params).then(response => {
                    this.info.asInfo = response.asInfo;
                    this.info.qsInfo = response.qsInfo;
                    this.info.detailInfo = response.detailInfo;
                    console.log(this.info.detailInfo.preset_detection.protection[0]);
                    this.page.setting = response.setting;
                    if (response.detailInfo.report) {
                        this.page.file_data = process.env.VUE_APP_BASE_PROXY_URL + response.detailInfo.report;
                    }
                    this.page.detail_result = response.detail_result;
                    for (let i in response.file_data.nameplate) {
                        this.page.nameplate.push(process.env.VUE_APP_BASE_PROXY_URL + response.file_data.nameplate[i].file_url);
                    }
                    for (let i in response.file_data.instrument_view) {
                        this.page.instrument_view.push(process.env.VUE_APP_BASE_PROXY_URL + response.file_data.instrument_view[i].file_url);
                    }
                    this.page.templatename = response.templatename;
                    this.page.is_display = 1;
                })
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.qsid);
        }
    }
</script>
<style scoped lang="scss">
    ::v-deep .van-field__control {
        color: red;
    }

    ::v-deep .van-field__label {
        width: 86%;
    }

    .background {
        background: rgba(69, 90, 100, 0.1);
    }

    .card-header {
        margin-top: 20px;
    }

    .mp {
        margin-top: 20px;
        padding-top: 10px;
        background: #fff;
    }

    .van-row {
        margin: 20px 0;
    }
</style>
