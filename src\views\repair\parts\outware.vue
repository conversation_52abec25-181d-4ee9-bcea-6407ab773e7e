<template>
    <div class="con_detail" v-if="page.is_display===1">
        <van-tabs :border="false">
            <van-tab title="维修单详情">
                <RepairInfo :info='info'/>
            </van-tab>
            <van-tab title="申请配件信息">
                <van-cell-group v-for="item in info.partsdata" :key="item.partid">
                    <van-cell title="配件名称" :value="item.parts"/>
                    <van-cell title="规格型号" :value="item.part_model"/>
                    <van-cell title="数量" :value="item.part_num"/>
                    <van-cell title="库存" :value="item.stock_num"/>
                </van-cell-group>
            </van-tab>
        </van-tabs>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">配件出库表单</h2>
        </div>
        <van-form ref="borrowForm" @submit="onSubmit" v-if="page.show_form==1">
            <van-cell-group>
                <van-field name="addtime" label="出库日期" :value="form.addtime" placeholder="点击选择日期" readonly
                           @click="page.showSelectTransfer = true"
                           :rules="[{ validator:addtime, message: '请选择日期',trigger:'onChange' }]"/>
                <van-popup v-model="page.showSelectTransfer" position="bottom">
                    <van-datetime-picker
                            v-model="page.currentDate"
                            type="date"
                            title="选择日期"
                            @confirm="onConfirmTransferDate"
                            @cancel="page.showSelectTransfer = false"
                    />
                </van-popup>
                <van-field name="leader" label="领用人" :value="form.leader" readonly/>
            </van-cell-group>
            <div style="margin: 16px;">
                <van-button round block type="warning" native-type="button" @click="changeFormType('yes')"
                            v-show="page.can_submit==1">确认并出库
                </van-button>
                <van-button round block type="danger" native-type="button" @click="changeFormType('no')"
                            v-show="page.can_submit==0">库存不足请先入库
                </van-button>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Toast,
        Loading,
        Popup,
        Picker,
        DatetimePicker
    } from 'vant';
    import {getInfo, submit} from "@/api/repair/parts";
    import RepairInfo from "@/components/RepairInfo";

    export default {
        name: 'applyScrap',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Loading.name]: Loading,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [DatetimePicker.name]: DatetimePicker,
            RepairInfo,
        },
        data() {
            return {
                info: {
                    partsdata: [],
                },
                page: {
                    can_submit: 0,//用于判断库存是否满足要求
                    is_display: 0,//用于判断是否加载完数据
                    formType: 'apply',//apply 申请 edit 重审 end 结束进程
                    currentDate: new Date(),//当前日期
                    showSelectTransfer: false,//时间控件显示
                    show_form: 1,
                },
                form: {
                    leader: '',//申请人
                    addtime: '',
                    remark: '',
                    outwareid: '',
                    parts: '',
                    parts_model: '',
                    apply_sum: '',
                    sum: '',

                },
            }
        },
        methods: {
            addtime(val) {
                if (this.page.can_submit == 0) {
                    return true;
                } else if (val) {
                    return true;
                }
                return false;

            },
            //选择转科日期
            onConfirmTransferDate(time) {
                this.form.addtime = this.timeFormat(time);
                this.page.showSelectTransfer = false;
            },
            validator(val) {
                if (this.form.borid > 0) {
                    return true;
                } else {
                    if (typeof val === 'undefined' || val.replace(/(^\s*)|(\s*$)/g, "") === '') {
                        this.form.scrap_reason = '';
                        return false;
                    }
                }
            },
            getInfo(repid) {
                let params = {repid: repid};
                getInfo(params).then(response => {
                    this.info = response.repArr;
                    this.info.partsdata = response.parts;
                    this.form.leader = response.outware.leader;
                    this.form.remark = response.outware.remark;
                    this.form.outwareid = response.outware.outwareid;
                    this.page.can_submit = response.can_submit;
                    this.page.show_form = response.is_display;
                    for (let x in response.apply) {
                        this.form.parts += response.apply[x].parts + '|';
                        this.form.parts_model += response.apply[x].parts_model + '|';
                        this.form.apply_sum += response.apply[x].sum + '|';
                        this.form.sum += response.apply[x].sum + '|';
                    }
                    this.page.is_display = 1;
                });
            },
            onSubmit(values) {
                switch (this.formType) {
                    case "yes":
                        //出库
                        values.remark = this.form.remark;
                        values.outwareid = this.form.outwareid;
                        values.parts = this.form.parts;
                        values.parts_model = this.form.parts_model;
                        values.apply_sum = this.form.apply_sum;
                        values.sum = this.form.sum;
                        break;
                    case "no":
                        //入库
                        this.$router.push(this.$store.getters.moduleName+'/RepairParts/partStockList');
                        return false;
                }
                //发送请求
                let _this = this;
                submit(Qs.stringify(values)).then(response => {
                    if (response.status === 1) {
                        Toast({
                            type:'success',//失败fail
                            duration:2000,//2秒
                            message: response.msg,
                            icon:'success',//失败cross
                            forbidClick:true,//是否禁止背景点击，避免重复提交
                            onClose(){
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/RepairParts/partsOutWareList');
                            }
                        });
                    }

                })
            },
            changeFormType(type) {
                this.formType = type;//改变提交表单的formType
                this.$refs.borrowForm.submit();//执行提交
            },
            timeFormat(time) { // 时间格式化
                let year = time.getFullYear();
                let month = time.getMonth() + 1;
                let day = time.getDate();
                return year + '-' + month + '-' + day;
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.repid);
        }
    }
</script>

<style scoped lang="scss">
    .van-button {
        margin-top: 10px;
    }

    .card-header {
        margin-top: 20px;
    }
</style>
