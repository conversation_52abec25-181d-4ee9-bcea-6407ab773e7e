import request from '@/utils/request'


export function getAssetsInfo(query) {
    return request({
        url: '/Lookup/showAssets',
        params: query
    })
}
export function upload(data) {
    return request({
        url: '/Notin/uploadReport',
        method: 'post',
        data
    })
}
export function startupInfo(query) {
    return request({
        url: '/Lookup/startup',
        params: query
    })
}
export function saveStartup(data) {
    return request({
        url: '/Lookup/startup',
        method: 'post',
        data
    })
}

export function getInsuranceInfo(data) {
    return request({
        url: '/Lookup/showAssets',
        method: 'post',
        data
    })
}
