<template>
    <div class="con_detail" v-if="page.is_display">
      <div class="card-header">
        <h2 class="detailTitle">设备基本信息</h2>
        <div class="bl1px"></div>
      </div>
      <div class="AssetsInfo">
        <van-cell-group>
          <van-cell title="设备名称 " :value="info.assets"/>
          <van-cell title="设备编号" :value="info.assnum"/>
          <van-cell title="设备原编号" :value="info.assorignum"/>
          <van-cell title="规格/型号" :value="info.model"/>
          <van-cell title="品牌" :value="info.brand"/>
          <van-cell title="所属科室" :value="info.department"/>
          <van-cell title="开机日期" :value="info.opendate"/>
        </van-cell-group>
      </div>

      <div class="his" v-if="page.show_form === 1" style="margin-bottom: 20px;">
        <van-collapse v-model="activeNames">
          <van-collapse-item name="1" wx-icon="lishijilu">
            <template #icon>
              <van-icon name="lishijilu" class-prefix="wx-icon" style="padding-right: 5px;"/>
            </template>
            <template #title>
              <div>历史开机记录（<span style="color: red;">{{ total }}条</span>）</div>
            </template>
            <div class="card">
              <van-card v-for="item in list" :key="item.id"  @click="to(item.id)">
                <template #title>
                  <van-cell>
                    <!-- 使用 title 插槽来自定义标题 -->
                    <template #title>
                      <span class="custom-title">登记单号：{{item.start_num}}</span>
                    </template>
                    <template #right-icon>
                      <van-tag :color="item.color">{{item.status_name}}</van-tag>
                    </template>
                  </van-cell>
                </template>
                <template #desc>
                  <div class="atn">
                    <span class="ct">病床号：</span>
                    <span class="cc">{{item.use_room}}</span>
                  </div>
                  <div class="atn">
                    <span class="ct">使用者：</span>
                    <span class="cc">{{item.user_name}}</span>
                  </div>
                  <div class="atn">
                    <span class="ct">开始时间：</span>
                    <span class="cc">{{item.start_time}}</span>
                  </div>
                  <div class="atn">
                    <span class="ct">结束时间：</span>
                    <span class="cc">{{item.end_time}}</span>
                  </div>
                  <div class="atn">
                    <span class="ct">消毒擦拭：</span>
                    <span class="cc">{{item.disinfect}}</span>
                  </div>
                  <div class="atn">
                    <span class="ct">遇到问题：</span>
                    <span class="cc">{{item.problems}}</span>
                  </div>
                </template>
                <template #footer>
                  <div class="gt">
                    <van-row>
                      <van-col span="10">使用人签名：{{item.user_sign}}</van-col>
                      <van-col span="14">需求部门签名：{{item.depart_sign}}</van-col>
                    </van-row>
                  </div>
                </template>
              </van-card>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>

      <div class="card-header">
        <img class="formImage" :src="require('@/assets/images/form/form.png')">
        <h2 class="formTitle">设备开机登记表单</h2>
      </div>
      <van-form ref="startupForm">
        <van-cell-group>
          <van-field label="登记人：" v-model="this.add_user" readonly/>
          <van-field
              label="病床号："
              name="use_room"
              placeholder="请填写患者病床号"
              v-model="formData.use_room"
              required
              :rules="[{ required: true, message: '患者病床号不能为空' }]"
          />
          <van-field
              label="患者名称："
              name="user_name"
              placeholder="请填写患者名称"
              v-model="formData.user_name"
              required
              :rules="[{ required: true, message: '患者名称不能为空' }]"
          />
          <van-field
              clickable
              label="开机时间："
              name="start_time"
              placeholder="请选择开机时间"
              v-model="formData.start_time"
              required
              :rules="[{ required: true, message: '开机时间不能为空' }]"
              @click="showStartPicker = true"
               />
          <van-popup v-model="showStartPicker" position="bottom">
            <van-datetime-picker
                type="datetime"
                v-model="currentDate"
                title="请选择具体开机时间"
                @confirm="onConfirmS"
                @cancel="showStartPicker = false"
            />
          </van-popup>

          <div v-show="formData.id">
            <van-field
                v-model="formData.problems"
                name="problems"
                label="遇到问题："
                type="textarea"
                rows="3"
                maxlength="120"
                show-word-limit
                placeholder="使用中遇到的问题"
            />
            <van-field
                clickable
                label="结束时间："
                name="end_time"
                placeholder="请选择结束时间"
                v-model="formData.end_time"
                required
                :rules="[{ required: true, message: '结束时间不能为空' }]"
                @click="showEndPicker = true"
                readonly/>
            <van-popup v-model="showEndPicker" position="bottom">
              <van-datetime-picker
                  type="datetime"
                  v-model="currentDate"
                  title="请选择具体结束时间"
                  @confirm="onConfirmE"
                  @cancel="showEndPicker = false"
              />
            </van-popup>
            <van-field
                label="消毒擦拭："
                name="disinfect"
                placeholder="请填写消毒擦拭人名称"
                v-model="formData.disinfect"
            />
            <van-field
                label="使用者签名："
                name="user_sign"
                placeholder="使用者签名"
                v-model="formData.user_sign"
                required
                :rules="[{ required: true, message: '使用者签名不能为空' }]"
            />
            <van-field
                label="需求部门签名："
                name="depart_sign"
                placeholder="请填写需求部门签名"
                v-model="formData.depart_sign"
                required
                :rules="[{ required: true, message: '需求部门签名不能为空' }]"
            />
          </div>
        </van-cell-group>
        <div style="margin: 16px;">
          <van-button v-show="formData.id" block color="#FFB800" native-type="button" @click="save('tmpSave')">暂时保存</van-button>
          <p></p>
          <van-button v-if="formData.working_status == null" block type="primary" native-type="button" @click="save('startSave')">确认登记</van-button>
          <van-button v-else block type="info" native-type="button" @click="save('endSave')">确认结束本次开机管理</van-button>
        </div>
      </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Divider, Cell, CellGroup, Icon, Field, Form, Button, Toast, Loading, List, Card, Tag, Row, Col,Collapse,CollapseItem,DatetimePicker,Popup} from 'vant';
    import {startupInfo,saveStartup} from "@/api/assets/lookup/show";

    export default {
        components: {
          [Divider.name]: Divider,
          [Cell.name]: Cell,
          [CellGroup.name]: CellGroup,
          [Icon.name]: Icon,
          [Field.name]: Field,
          [Form.name]: Form,
          [Button.name]: Button,
          [Loading.name]: Loading,
          [List.name]: List,
          [Card.name]: Card,
          [Tag.name]: Tag,
          [Row.name]: Row,
          [Col.name]: Col,
          [Collapse.name]: Collapse,
          [CollapseItem.name]: CollapseItem,
          [DatetimePicker.name]: DatetimePicker,
          [Popup.name]: Popup,
        },
        data() {
          return {
            info: {

            },
            list: [],
            page: {
              username: '',
              show_form: 1,
              is_display: 0,
            },
            total:0,
            minDate: '',
            maxDate: '',
            currentDate:new Date(),
            showStartPicker: false,
            showEndPicker: false,
            activeNames: ['2'],
            add_user:'',
            formType:'apply',
            formData:{
              use_room:'',
              user_name:'',
              start_time:'',
              problems:'',
              disinfect:'',
              user_sign:'',
              depart_sign:'',
              end_time:'',
            }
          }
        },
        methods: {
          getInfo(assnum) {
            let params = {assnum: assnum,'action':'scanQRcode_startup'};
            startupInfo(params).then(response => {
              this.page.is_display = 1;
              this.total = response.total;
              this.info = response.asArr;
              this.list = response.complete;
              this.formData = response.doing;
              this.add_user = response.add_user;
              this.minDate = new Date(response.minDate);
              this.maxDate = new Date(response.maxDate);
            })
          },
          save(type) {
            //发送请求
            this.formData.assid = this.info.assid;
            this.formData.action = type;
            saveStartup(Qs.stringify(this.formData)).then(response => {
              if(response.status === 1){
                Toast({
                  type:'success',//失败fail
                  duration:2000,//2秒
                  message: response.msg,
                  icon:'success',//失败cross
                  forbidClick:true,//是否禁止背景点击，避免重复提交
                  onClose(){
                    location.reload();
                  }
                });
              }
            })
          },
          onConfirmS(time) {
            this.formData.start_time = this.timeFormat(time);
            this.showStartPicker = false;
          },
          onConfirmE(time) {
            this.formData.end_time = this.timeFormat(time);
            this.showEndPicker = false;
          },
          timeFormat(time) { // 时间格式化
            let year = time.getFullYear();
            let month = time.getMonth() + 1;
            let day = time.getDate();
            let hours = time.getHours();
            let minutes = time.getMinutes();
            month = month < 10 ? '0'+month : month;
            day = day < 10 ? '0'+day : day;
            hours = hours < 10 ? '0'+hours : hours;
            minutes = minutes < 10 ? '0'+minutes : minutes;
            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes
          },
          // showEpick(){
          //   this.showEndPicker = true;
          //   this.formData.end_time = new Date();
          // }
        },
        mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.assnum);
        }
    }
</script>
<style scoped lang="scss">
.custom-title{
  font-size: 18px;
}
.van-card{
  padding: 8px 0;
  background:#fff;
}
.card .van-cell {
  padding: 10px 0;
}
.van-card__header{padding: 8px 16px;}
.van-card__footer{background:#FAFAFA;padding-left: 16px;}
.atn{margin-top: 5px;}
.ct{font-size: 14px;}
.cc{font-size: 14px;color:#B0B9BD;}
.gt{height: 45px;line-height: 45px;font-size: 14px;color:#B0B9BD;overflow: hidden;}
.van-card__footer{
  text-align: left;
}
.his ::v-deep .van-collapse-item__content {
  padding: 0;
  background-color:transparent;
}
</style>
