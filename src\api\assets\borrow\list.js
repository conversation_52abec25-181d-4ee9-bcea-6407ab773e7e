import request from '@/utils/request'

//借调申请列表
export function getBorrowAssetsList(query) {
    return request({
        url: '/Borrow/borrowAssetsList',
        params: query
    })
}

//确认借入列表
export function getBorrowInCheckList(query) {
    return request({
        url: '/Borrow/borrowInCheckList',
        params: query
    })
}
//归还验收列表
export function getGiveBackCheckList(query) {
    return request({
        url: '/Borrow/giveBackCheckList',
        params: query
    })
}
//借调逾期列表
export function getReminderList(query) {
    return request({
        url: '/Borrow/giveBackCheckList',
        params: query
    })
}
//借调逾期
export function sendOutReminder(data) {
    return request({
        url: '/Borrow/giveBackCheckList?action=sendOutReminder',
        method: 'post',
        data
    })
}
