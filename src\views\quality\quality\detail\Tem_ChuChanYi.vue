<template>
    <div class="con_detail" v-if="page.is_display===1">
        <Quality :info="info"/>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">质控检测明细（模板：{{page.templatename}}）</h2>
        </div>

        <van-form ref="qualityForm" @submit="onSubmit">
            <van-field name="lookslike" label="外观功能：" class="text_length">
                <template #input>
                    <van-radio-group @change="looklike_change" v-model="form.lookslike" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field v-show="show_lookslike_desc" name="lookslike_desc" type="textarea" rows="2" autosize
                       v-model="form.lookslike_desc" placeholder="外观不符合的请说明情况" style="margin-bottom: 10px;"/>
            <van-collapse v-model="page.activeNames">
            <van-collapse-item :title="page.heartRate" name="1">
                <van-row v-for="(item,key) in page.setting.heartRate" v-show="page.heartRateshow" :key="key">
                    <van-field :label="'设定值：'+item" v-model="page.heartRatevalue[key]" type="number" placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            <van-collapse-item :title="page.energesis" name="2">
                <van-row v-for="(item,key) in page.setting.energesis" v-show="page.energesisshow" :key="key">
                    <van-cell :title="'设定值：'+item" :value="'误差：'+page.tolerancevalue[key]"/>
                    <van-field v-model="page.energesisvalue[key]" @input="onChange(item,key)" placeholder="实际测量值"/>
                </van-row>
            </van-collapse-item>
            </van-collapse>
            <van-row>
                <van-col span="24">
                    <van-field name="charge_result" label="充电时间 (s)：" class="text_length">
                        <template #input>
                            <van-radio-group v-model="form.charge_result" direction="horizontal">
                                <van-radio name="1">符合</van-radio>
                                <van-radio name="2">不符合</van-radio>
                            </van-radio-group>
                        </template>
                    </van-field>
                </van-col>
                <van-col span="24">
                    <van-field type="number" name="charge" placeholder="请输入充电时间 (s)" v-model="form.charge"/>
                </van-col>
            </van-row>
            <van-field name="internal_discharge" label="内部放电:" class="text_length">
                <template #input>
                    <van-radio-group v-model="form.internal_discharge" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field name="total_desc" class="text_length" type="textarea" rows="3" show-word-limit maxlength="120" label="检测备注：" v-model="form.total_desc" placeholder="请填写检测备注（如偏离情况说明）"/>

            <div class="mp">
                <van-divider>设备铭牌照片</van-divider>
                <van-row type="flex" justify="center">
                    <van-uploader  multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" :before-read="beforeRead" v-model="form.nameplate_fileList" :after-read="nameplate_afterRead"
                                  :before-delete="del_nameplate">
                    </van-uploader>
                </van-row>
                <van-divider>检测仪器视图照片</van-divider>
                <van-row type="flex" justify="center">
                    <van-uploader multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" :before-read="beforeRead" v-model="form.instrument_fileList" :after-read="instrument_afterRead"
                                  :before-delete="del_instrument">
                    </van-uploader>
                </van-row>
                <div style="margin: 16px;">
                    <van-button round block color="#FFB800" native-type="button" @click="changeFormType('keepquality')"
                                style="margin-bottom: 16px;">
                        暂时保存
                    </van-button>
                    <van-button round block type="primary" native-type="button" @click="changeFormType('end')">
                        确认并提交
                    </van-button>
                </div>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>
<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Row,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Toast,
        Loading,
        Uploader,
        ImagePreview,
        Image as VanImage,
        Swipe,
        SwipeItem,
        RadioGroup,
        Radio,
        Col,
        Stepper,
        Notify,
        Collapse,
        CollapseItem
    } from 'vant';
    import {getInfo, submit} from "@/api/quality/detail";
    import Quality from "@/components/Quality";

    export default {
        name: 'ChuChanYi',
        components: {
            [Divider.name]: Divider,
            [Collapse.name]: Collapse,
            [CollapseItem.name]: CollapseItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Row.name]: Row,
            [Col.name]: Col,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Uploader.name]: Uploader,
            [Tab.name]: Tab,
            [Stepper.name]: Stepper,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Tabs.name]: Tabs,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [VanImage.name]: VanImage,
            [Loading.name]: Loading,
            Quality,
        },
        data() {
            return {
                info: {
                    asInfo: [],
                    qsInfo: [],
                },
                page: {
                    heartRate: '心率（次/min）：最大允差：',
                    energesis: '释放能量（J）：最大允差：',
                    heartRateshow: true,
                    energesisshow: true,
                    hicon: 'arrow-down',
                    eicon: 'arrow-down',
                    file_data: '',
                    heartRatevalue: [],
                    energesisvalue: [],
                    tolerancevalue: [],

                    setting: {
                        heartRate: [],
                        energesis: [],
                    },
                    templatename: '',
                    nameplate: [],
                    instrument_view: [],
                    detail_result: [],
                    activeNames:[],
                    positions: 0,
                    is_display: 0,
                  max_count:3
                },
                form: {
                    lookslike: "1",
                    charge_result: '1',
                    internal_discharge: '1',
                    type: 'keepquality',
                    total_desc: "",
                    charge: "",
                    nameplate_fileList: [],
                    instrument_fileList: [],
                },
                show_lookslike_desc: false,
            }
        },
        methods: {
            looklike_change(value) {
                if (value == 2) {
                    this.show_lookslike_desc = true;
                } else {
                    this.show_lookslike_desc = false;
                    this.form.lookslike_desc = '';
                }
            },
            onChange(item, key) {
                if(this.page.energesisvalue[key]){
                    let tolerance = Math.abs(item - this.page.energesisvalue[key]);
                    this.page.tolerancevalue[key] = tolerance;
                }else{
                    this.page.tolerancevalue[key] = '';
                }
            },
            show_nameplate_Image(key) {
                ImagePreview({
                    images: this.page.nameplate,
                    startPosition: key,
                });
            },
            show_instrument_Image(key) {
                ImagePreview({
                    images: this.page.instrument_view,
                    startPosition: key,
                });
            },
            on_nameplate_Change(index) {
                this.page.positions = index;
            },
            on_instrument_Change(index) {
                this.page.positions = index;
            },
          beforeRead(file){
            if (file.length > this.page.max_count){
              Notify({ type: 'danger', message: `最多只能选择${this.page.max_count}个图片` });
              return  false
            }
            return true
          },
            nameplate_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                //上传图片
                let values = [];
                values.type = 'nameplate';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';

                        this.form.nameplate_fileList.pop();
                        this.form.nameplate_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                });
            },
            instrument_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                //上传图片
                let values = [];
                values.type = 'instrument_view';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';

                        this.form.instrument_fileList.pop();
                        this.form.instrument_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                });
            },
            changeFormType(type) {
                this.form.type = type;
                this.$refs.qualityForm.submit();
            },
            oversize(){
                //超出限制大小
                Notify({ type: 'danger', message: '图片超出10M大小限制' });
                return false;
            },
            del_nameplate(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            del_instrument(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                console.log(file);
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            onSubmit(values) {
                if(values.lookslike == 2){
                    //外观不符合的要说明情况
                    if(!values.lookslike_desc.trim()){
                        Notify({ type: 'danger', message: '外观不符合的请说明情况' });
                        return false;
                    }
                }
                //心率
                values.heartRate = this.page.heartRatevalue;
                for(let i = 0;i < this.page.setting.heartRate.length;i++){
                    if(!values.heartRate[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整心率测量值' });
                        return false;
                    }else if (!values.heartRate[i]) {
                        values.heartRate[i] = "";
                    }
                }

                //释放能量
                values.energesis = this.page.energesisvalue;
                for(let i = 0;i < this.page.setting.energesis.length;i++){
                    if(!values.energesis[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完整释放能量测量值' });
                        return false;
                    }else if (!values.energesis[i]) {
                        values.energesis[i] = "";
                    }
                }

                //充电时间
                if(!values.charge.trim()&&this.form.type!="keepquality"){
                    Notify({ type: 'danger', message: '请填写充电时间' });
                    return false;
                }else if (!values.charge.trim()) {
                        values.charge = "";
                    }

                values.energesis_tolerance = this.page.tolerancevalue;
                values.qsid = this.$route.query.qsid;
                values.action = this.form.type;

                let _this = this;
                submit(Qs.stringify(values)).then(res => {
                    if (res.status == 1) {
                        Toast({
                            type: 'success',//失败fail
                            duration: 2000,//2秒
                            message: res.msg,
                            icon: 'success',//失败cross
                            forbidClick: true,//是否禁止背景点击，避免重复提交
                            onClose() {
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Quality/qualityDetailList');
                            }
                        });
                    }
                });
            },
            getInfo(qsid) {
                let params = {qsid: qsid};
                getInfo(params).then(response => {
                    this.info.asInfo = response.asInfo;
                    this.info.qsInfo = response.qsInfo;
                    this.page.templatename = response.templatename;
                    for (let i in response.setting) {
                        if (response.setting[i].detection_Ename == 'heartRate') {
                            this.page.setting.heartRate = response.setting[i].set;
                        }
                        if (response.setting[i].detection_Ename == 'energesis') {
                            this.page.setting.energesis = response.setting[i].set;
                        }
                        if (response.setting[i].detection_Ename == 'charge') {
                            for (let j in response.setting[i].set) {
                                this.page.tolerancevalue[j] = "";
                            }
                        }
                    }
                    if (response.detail_data != null && response.detail_data.fixed_detection) {
                        this.page.heartRatevalue = response.detail_data.fixed_detection.heartRate;
                        this.page.energesisvalue = response.detail_data.fixed_detection.energesis;
                        this.page.tolerancevalue = response.detail_data.fixed_detection.energesis_tolerance;
                        this.form.charge_result = response.detail_data.fixed_detection.charge_result;
                        this.form.lookslike = response.detail_data.fixed_detection.lookslike;
                        if(response.detail_data.fixed_detection.lookslike == 2){
                            this.show_lookslike_desc = true;
                            this.form.lookslike_desc = response.detail_data.fixed_detection.lookslike_desc;
                        }
                        this.form.internal_discharge = response.detail_data.fixed_detection.internal_discharge;
                        this.form.charge = response.detail_data.fixed_detection.charge;
                        this.form.total_desc = response.detail_data.fixed_detection.total_desc;
                    }
                    if (response.file_data) {
                        for (let i in response.file_data.nameplate) {
                            this.form.nameplate_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.nameplate[i].file_url,
                                id: response.file_data.nameplate[i].file_id
                            });
                        }
                        for (let i in response.file_data.instrument_view) {
                            this.form.instrument_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.instrument_view[i].file_url,
                                id: response.file_data.instrument_view[i].file_id
                            });
                        }

                    }
                    this.page.heartRate = this.page.heartRate + response.tolerance.heartRate;
                    this.page.energesis = this.page.energesis + response.tolerance.energesis;
                    this.page.is_display = 1;
                });
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.qsid);
        }
    }
</script>
<style scoped lang="scss">
    .background {
        background: rgba(69, 90, 100, 0.1);
    }
    ::v-deep .text_length .van-cell__title {
        width: 95px
    }
    ::v-deep  .van-field__label{
        width: 170px;
    }
    .card-header {
        margin-top: 20px;
    }

    .mp {
        margin-top: 20px;
        padding-top: 10px;
        background: #fff;
    }
</style>
