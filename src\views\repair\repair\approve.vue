<template>
    <div class="con_detail" v-if="page.is_display===1">
        <van-tabs :border="false">
            <van-tab title="维修单详情">
                <RepairInfo :info='info.repairdata'/>
            </van-tab>
            <van-tab title="报价信息" v-if="info.company!=[]&&info.company!=undefined">
                <van-cell-group v-for="item in info.company" :key="item.apprid">
                    <van-cell title="公司名称" :value="item.offer_company"/>
                    <van-cell title="公司资质">
                        <template #right-icon><span @click="img(item.pic_url)" v-html="item.aptitude"/></template>
                    </van-cell>
                    <van-cell title="联系人" :value="item.offer_contacts"/>
                    <van-cell title="联系人电话" :value="item.telphone"/>
                    <van-cell title="维修周期" :value="item.cycle"/>
                    <van-cell title="发票" :value="item.invoice"/>
                    <van-cell title="费用" :value="item.total_price"/>
                </van-cell-group>
            </van-tab>
            <van-tab title="配件信息" v-if="table_parts.bodyData!=[]&&table_parts.bodyData!=undefined&&table_parts.bodyData.length>0">
                <Table :table="table_parts"/>
            </van-tab>
        </van-tabs>
        <div style="margin-bottom: 20px;">
            <div class="card-header">
                <h2 class="detailTitle">审批记录</h2>
                <div class="bl1px"></div>
            </div>
            <Approves :info='info.approves'/>
        </div>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">审批表单</h2>
        </div>
        <addApprove :info="page" @values="onSubmit" v-if="page.show_form==1" />
        <div class="cus_loading" v-show="show_loading"><van-loading type="spinner" color="#1989fa" /></div>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px" />
        </div>
    </div>
</template>
<script>
    import Qs from 'qs';
    import {Divider, Cell, CellGroup, Icon, Field,Toast, Form, Button, Tab, Tabs,Popup,Picker,DatetimePicker,RadioGroup, Radio,Loading, ImagePreview} from 'vant';
    import {getInfo,submit} from "@/api/repair/approve";
    import RepairInfo from "@/components/RepairInfo";
    import Approves from "@/components/Approves";
    import addApprove from "@/components/addApprove";
    import Table from '@/components/Table';

    export default {
        name: 'approve',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Loading.name]: Loading,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [DatetimePicker.name]: DatetimePicker,
            RepairInfo,
            Approves,
            addApprove,
            Table
        },
        data() {
            return {
                info: {
                    repairdata: [],
                    parts:[],
                    approves:[],
                },
                page:{
                    approveDate:'',
                    approveUser:'',
                    is_display:0,
                    all_approver:'',
                    active:'',
                },
                form:{
                    is_adopt:'1',
                    approveRemark:'',
                },
                table_parts: {
                    headerData: [
                        {text: '配件名称', field: 'parts', width: 40},
                        {text: '配件型号', field: 'part_model', width: 40},
                        {text: '数量', field: 'part_num'},
                    ],
                    noData:'暂无相关配件信息',
                    bodyData: []
                },
                show_loading:0
            }
        },
        methods: {
            img(urlList){
                ImagePreview(urlList.map(url => process.env.VUE_APP_BASE_PROXY_URL + url));
            },
            onSubmit(values) {
                values.repid = this.$route.query.repid;
                let _this = this;
                _this.show_loading = 1;
                submit(Qs.stringify(values)).then(response => {
                    _this.show_loading = 0;
                    Toast({
                        type:'success',//失败fail
                        duration:2000,//2秒
                        message: response.msg,
                        icon:'success',//失败cross
                        forbidClick:true,//是否禁止背景点击，避免重复提交
                        onClose(){
                            //关闭后跳转到审批列表页
                            _this.$router.push(this.$store.getters.moduleName+'/Notin/approve');
                        }
                    });
                });
            },
            getInfo(repid) {
                let params = {repid: repid};
                getInfo(params).then(response => {
                    this.info.repairdata = response.repArr;
                    this.info.company = response.company;
                    this.table_parts.bodyData = response.parts;
                    this.info.approves = response.approves;
                    this.page.approveDate = response.approveDate;
                    this.page.approveUser = response.approveUser;
                    this.page.all_approver = response.repArr.all_approver;
                    this.page.active = response.repArr.app_user_num;
                    this.page.show_form = response.is_display;
                    this.page.is_display = 1;
                });
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.repid);
        }
    }
</script>
<style scoped lang="scss">
    .card-header {
        margin-top: 20px;
    }

    .app_pro {
        background: #fff;
    }
   ::v-deep .gray{
        color: #666;
        margin-left: 10px;
    }
   ::v-deep .red{color: #FF5722}
   .van-tabs--line .van-tabs__wrap {
        height: 0;
    }
</style>
