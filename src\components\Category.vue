<template>
    <div class="ListCategory">
        <van-row>
            <van-col span="12" @click="cate_show = true">
                <div class="category cate">
                    <van-icon class-prefix="wx-icon" name="shebeifenlei" size="36px" color="#FF5722"/>
                    <p class="category-title">设备分类</p>
                    <p class="category-text">医疗器械分类目录</p>
                </div>
            </van-col>
            <van-col span="12" @click="department_show = true">
                <div class="category department">
                    <van-icon class-prefix="wx-icon" name="keshiguanli" size="36px" color="#5fb878"/>
                    <p class="category-title">使用科室</p>
                    <p class="category-text">医院科室分类目录</p>
                </div>
            </van-col>
        </van-row>
        <!--设备分类-->
        <van-popup v-model="cate_show" position="top" :overlay="true" style="height:100%;" closeable close-icon="close">
            <div class="popupTopTitleDiv">
                <span class="popupTopTitle">设备分类（数字为对应分类的设备台数）</span>
            </div>
            <van-tree-select height="100%" :items="care_items" :main-active-index.sync="active">
                <template #content>
                    <ul class="right-content">
                        <li v-if="care_items[active]" @click="onItemClick(care_items[active].catid)" class="soncat">全部（<span style="color:red;">{{care_items[active].assetssum}}</span>）
                            <van-icon name="arrow" class="icon-right"/>
                        </li>
                        <li v-for="(item) in cateList" :key="item.catid" :class="[care_items[active].catid==item.parentid ? '' : 'aaa']" @click="onItemClick(item.catid)"> {{item.category}}
                            <van-icon name="arrow" class="icon-right"/>
                        </li>
                    </ul>
                </template>
            </van-tree-select>
        </van-popup>
        <!--科室分类-->
        <van-popup v-model="department_show" position="top" :overlay="true" style="height:100%;" closeable close-icon="close">
            <div class="popupTopTitleDiv">
                <span class="popupTopTitle">科室列表（数字为对应科室的设备台数）</span>
            </div>
            <div class="popupContent">
                <div class="weui-grids">
                    <div v-for="(item) in departmentList" :key="item.departid" class="weui-grid js_grid" @click="ondepartClick(item.departid)">
                        <p class="weui-grid__label">
                            <span class="department">{{item.department}}</span>
                        </p>
                        <div class="departmentAssetsSum">（{{item.assetssum}}）</div>
                    </div>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script>
    import {Col, Row, Icon, Popup, TreeSelect, Overlay} from "vant";
    import {cate, departs} from "@/api/common/category";

    export default {
        data() {
            return {
                care_items: [],
                department_items: [],
                cateList: [],
                departmentList: [],
                token: '1',
                active: 0,
                cate_show: false,
                department_show: false,
            };
        },
        props: {
            type: String
        },
        name: "ListCategory",
        components: {
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [Popup.name]: Popup,
            [TreeSelect.name]: TreeSelect,
            [Overlay.name]: Overlay,
        },
        methods: {
            onItemClick: function (catid) {
                this.$emit("catid", catid);
                this.cate_show = false;
            },
            ondepartClick: function (departid) {
                this.$emit("departid", departid);
                this.department_show = false;
            },
        },
        mounted: function () {
            var data = {type: this.type};
            cate(data).then(response => {
                for (let x in response.data) {
                    if (Object.prototype.hasOwnProperty.call(response.data,x)){
                        if (response.data[x].parentid === '0') {
                            this.care_items.push(response.data[x]);
                        } else {
                            this.cateList.push(response.data[x]);
                        }
                    }
                }
            });
            departs(data).then(response => {
                this.departmentList = response.data;
            })
        }
    }
</script>

<style scoped lang="scss">
    .category {
        padding-left: 10px;

        .wx-icon {
            float: left;
            position: relative;
            top: 3px;
            margin: 0 10px;
        }

        .category-title {
            font-size: 16px;
            color: #333333;
            margin: 0;
        }

        .category-text {
            font-size: 12px;
            margin: 5px;
            color: #707070;
        }
    }

    .cate {
        border-right: 1px solid #c9c9c9
    }

    .department {
        padding-left: 0;
    }

    .popupTopTitle {
        padding-left: 0.4rem;
    }

    .popupTopTitleDiv {
        position: relative;
        height: 3rem;
        padding: 0 0.375rem;
        font-size: 1rem;
        background-color: #f1f1f1;
        line-height: 3rem;
        border-bottom: 1px solid #dddddd;
    }

    .Icon {
        margin-left: 4rem;
    }

    .aaa {
        display: none;
    }

    li {
        padding: 0.6438rem 1rem;
        border-bottom: 1px solid #e6e6e6;
        position: relative;
        list-style: none;
    }

    .icon-right {
        position: absolute;
        right: 0.5rem;
        top: 30%;
        opacity: 0.5;
    }

    .popupContent {
        background-color: #fff;
    }

    .weui-grids {
        position: relative;
        overflow: hidden;
    }

    .weui-grid {
        text-align: center;
        position: relative;
        float: left;
        padding: 20px 10px;
        width: 33.3%;
        box-sizing: border-box;
        padding: 1rem;
        border-bottom: 1px solid #D9D9D9;
        border-right: 1px solid #D9D9D9;
    }

    .weui-grid__label {
        margin: 0.5rem 0;
        display: block;
        text-align: center;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .departmentAssetsSum {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        text-align: center;
        color: #FF5722;
    }
</style>
