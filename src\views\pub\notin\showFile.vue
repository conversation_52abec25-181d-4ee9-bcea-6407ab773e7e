<template>
    <div class="pdf">
        <div class="pdf-tab">
            <div class="btn-def btn-pre" @click.stop="prePage">上一页</div>
            <div class="btn-def btn-next" @click.stop="nextPage">下一页</div>
        </div>
        <div class="pdf-num">{{pageNum}}/{{pageTotalNum}}</div>
        <div class="pdf-process">进度：{{loadedRatio}}</div>
        <div class="pdf-total">页面加载成功: {{curPageNum}}</div>
        <pdf
                ref="pdf"
                :src="pdfUrl"
                :page="pageNum"
                :rotate="pageRotate"
                @password="password"
                @progress="loadedRatio = $event"
                @page-loaded="pageLoaded($event)"
                @num-pages="pageTotalNum=$event"
                @error="pdfError($event)"
                @link-clicked="page = $event">
        </pdf>
    </div>
</template>
<script>
    import pdf from 'vue-pdf'

    export default {
        name: 'Pdf',
        components: {
            pdf
        },
        data() {
            return {
                pdfUrl: '',
                pageNum: 1,
                pageTotalNum: 1,
                pageRotate: 0,
                // 加载进度
                loadedRatio: 0,
                curPageNum: 0,
            }
        },
        mounted: function () {
            let url = process.env.VUE_APP_BASE_PROXY_URL;
            this.pdfUrl = url+this.$route.query.path;
        },
        methods: {
            prePage() {
                var p = this.pageNum;
                p = p > 1 ? p - 1 : this.pageTotalNum;
                this.pageNum = p
            },
            nextPage() {
                var p = this.pageNum;
                p = p < this.pageTotalNum ? p + 1 : 1;
                this.pageNum = p;
            },
            clock() {
                this.pageRotate += 90;
            },
            counterClock() {
                this.pageRotate -= 90;
            },
            password(updatePassword) {
                updatePassword(prompt('password is "123456"'));
            },
            pageLoaded(e) {
                this.curPageNum = e;
            },
            pdfError(error) {
                console.error(error);
            },
            pdfPrintAll() {
                this.$refs.pdf.print();
            },
            pdfPrint() {
                this.$refs.pdf.print(100, [1, 2]);
            },
        }
    }
</script>
<style scoped lang="scss">
    .pdf-tab {
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        padding: 0 .4rem;
        -ms-flex-pack: justify;
        justify-content: space-between;
    }

    .pdf-tab .btn-def {
        border-radius: .2rem;
        font-size: 0.8rem;
        height: 1.6rem;
        width: 4rem;
        text-align: center;
        line-height: 1.6rem;
        background: #409eff;
        color: #fff;
        margin-bottom: .26667rem;
        margin-top: 1rem;
    }

    .pdf-num, .pdf-process, .pdf-total {
        text-align: center;
        font-size: 0.7rem;
    }
</style>

