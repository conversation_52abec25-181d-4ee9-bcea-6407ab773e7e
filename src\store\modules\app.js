import {use_wx} from "@/api/wechat";

const state = {
    menus: {},
    kBtn: {},
    targetUser: {}, //用户详细资料数据
    initLink: '',
    moduleName:process.env.VUE_APP_BASE_AXIOS_URL
};

const mutations = {
    SET_MENUS(state,menus){
        if (menus) {
            state.menus = menus;
        } else {
            state.menus = {};
        }
    },
    SET_KBTN_MENUS(state,kBtn){
        if (kBtn) {
            state.kBtn = kBtn;
        } else {
            state.kBtn = {};
        }
    },
	setUseWx: (state, boole) => {
		state.use_wx = boole
	},
    SET_TARGET_USER(state, targetUser) {
        if (targetUser) {
            state.targetUser = targetUser; //如果targetUser有内容就赋给状态信息
        } else {
            //如果没内容就给targetUser赋空对象
            state.targetUser = {};
        }
    },
    setInitLink (state, initLink) {
        state.initLink = initLink
    }
};

const actions = {
    use_wx({commit}){
        return new Promise((resolve, reject) => {
            use_wx().then(response=>{
                const { data } = response;
                commit('setUseWx', data);
                resolve()
            }).catch(error => {
                reject(error)
            })
        })
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
}
