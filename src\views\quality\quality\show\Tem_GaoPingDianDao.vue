<template>
    <div class="con_detail" v-if="page.is_display===1">
        <Quality :info="info"/>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">质控检测明细（模板：{{page.templatename}}）</h2>
        </div>

        <van-form>
            <van-field label="外观功能" :value="info.detailInfo.exterior=='1'?'符合':'不符合'" readonly/>
            <van-cell v-show="info.detailInfo.exterior==2" :title="'不符合情况说明：'+info.detailInfo.exterior_explain"/>
            <van-cell-group>
                <van-cell :title="page.Unipolar_cutting" class="background"/>
                <van-cell title="设定值" value="测量值"/>
                <van-cell v-for="(item,key) in page.setting.Unipolar_cutting" :key="key" :title="item"
                          :value="info.detailInfo.preset_detection.Unipolar_cutting[key]"/>
                <van-field label="检测结果"
                           :value="page.detail_result.Unipolar_cutting=='1'?'符合':page.detail_result.Unipolar_cutting=='2'?'不符合':page.detail_result.Unipolar_cutting=='3'?'不适用':'未知'"
                           readonly/>
            </van-cell-group>

            <van-cell-group>
                <van-cell :title="page.Bipolar_resection" class="background"/>
                <van-cell title="设定值" value="测量值"/>
                <van-cell v-for="(item,key) in page.setting.Bipolar_resection" :key="key" :title="item"
                          :value="info.detailInfo.preset_detection.Bipolar_resection[key]"/>
                <van-field label="检测结果"
                           :value="page.detail_result.Bipolar_resection=='1'?'符合':page.detail_result.Bipolar_resection=='2'?'不符合':page.detail_result.Bipolar_resection=='3'?'不适用':'未知'"
                           readonly/>
            </van-cell-group>

            <van-cell-group>
                <van-cell :title="page.Bipolar_coagulation" class="background"/>
                <van-cell title="设定值" value="测量值"/>
                <van-cell v-for="(item,key) in page.setting.Bipolar_coagulation" :key="key" :title="item"
                          :value="info.detailInfo.preset_detection.Bipolar_coagulation[key]"/>
                <van-field label="检测结果"
                           :value="page.detail_result.Bipolar_coagulation=='1'?'符合':page.detail_result.Bipolar_coagulation=='2'?'不符合':page.detail_result.Bipolar_coagulation=='3'?'不适用':'未知'"
                           readonly/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="高频漏电(单极模式)(mA)" class="background"/>
                <van-cell title="电极高频漏电(单切电极)" :value="info.detailInfo.preset_detection.Unipolar_mode[0]"/>
                <van-cell title="电极高频漏电(单切电凝)" :value="info.detailInfo.preset_detection.Unipolar_mode[1]"/>
                <van-cell title="中性电极高频漏电(单切电极)" :value="info.detailInfo.preset_detection.Unipolar_mode[2]"/>
                <van-cell title="中性电极高频漏电(单切电凝)" :value="info.detailInfo.preset_detection.Unipolar_mode[3]"/>
                <van-field label="单极模式测试结果"
                           :value="page.detail_result.Unipolar_mode=='1'?'符合':page.detail_result.Unipolar_mode=='2'?'不符合':page.detail_result.Unipolar_mode=='3'?'不适用':'未知'"
                           readonly/>

            </van-cell-group>

            <van-cell-group>
                <van-cell title="高频漏电(双极模式)(mA)" class="background"/>
                <van-cell title="电极高频漏电(双极电极)" :value="info.detailInfo.preset_detection.Bipolar_mode[0]"/>
                <van-cell title="电极高频漏电(双极电凝)" :value="info.detailInfo.preset_detection.Bipolar_mode[1]"/>
                <van-cell title="中性电极高频漏电(双极电极)" :value="info.detailInfo.preset_detection.Bipolar_mode[2]"/>
                <van-cell title="中性电极高频漏电(双极电凝)" :value="info.detailInfo.preset_detection.Bipolar_mode[3]"/>
                <van-field label="单极模式测试结果"
                           :value="page.detail_result.Bipolar_mode=='1'?'符合':page.detail_result.Bipolar_mode=='2'?'不符合':page.detail_result.Bipolar_mode=='3'?'不适用':'未知'"
                           readonly/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="其他信息" class="background"/>
                <van-cell title="报警限检查："
                          :value="page.detail_result.Contact_resistance_monitoring==1?'符合':page.detail_result.Contact_resistance_monitoring==2?'不符合':'未知'"/>
                <van-cell title="声光报警："
                          :value="page.detail_result.Sound_and_light_alarm_function==1?'符合':page.detail_result.Sound_and_light_alarm_function==2?'不符合':'未知'"/>
            </van-cell-group>
            <van-field label="检测结果" :value="info.detailInfo.result=='1'?'合格':'不合格'" readonly/>
            <van-cell v-show="info.detailInfo.remark" :title="'检测备注：'+info.detailInfo.remark"/>

            <div class="mp">
                <van-divider v-show="page.nameplate.length>0">设备铭牌照片</van-divider>
                <van-swipe class="image-swipe" :autoplay="3000" indicator-color="white" height="220"
                           v-show="page.nameplate.length>0" @change="onChange">
                    <van-swipe-item v-for="(item,index) in page.nameplate" :key="index" @click="showImage">
                        <van-image width="100%" height="220" :src="item" fit="fill"/>
                    </van-swipe-item>
                </van-swipe>

                <van-divider v-show="page.instrument_view.length>0">检测仪器视图照片</van-divider>
                <van-swipe class="image-swipe" :autoplay="3000" indicator-color="white" height="220"
                           v-show="page.instrument_view.length>0" @change="onChange">
                    <van-swipe-item v-for="(item,index) in page.instrument_view" :key="index" @click="showImg">
                        <van-image width="100%" height="220" :src="item" fit="fill"/>
                    </van-swipe-item>
                </van-swipe>
                <van-divider v-show="page.file_data">质控报告</van-divider>
                <van-image v-show="page.file_data" width="100%" height="100%" :src="page.file_data"/>
                <van-row type="flex" justify="center">
                    <van-uploader max-size="10485760" multiple :after-read="afterRead">
                        <van-button type="primary" native-type="button">上传质控报告</van-button>
                    </van-uploader>
                </van-row>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>
<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Row,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Toast,
        Loading,
        Uploader,
        ImagePreview,
        Image as VanImage,
        Swipe,
        SwipeItem
    } from 'vant';
    import {getInfo, submit} from "@/api/quality/show";
    import Quality from "@/components/Quality";

    export default {
        name: 'GaoPingDianDao',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Row.name]: Row,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Uploader.name]: Uploader,
            [Tab.name]: Tab,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Tabs.name]: Tabs,
            [VanImage.name]: VanImage,
            [Loading.name]: Loading,
            Quality,
        },
        data() {
            return {
                info: {
                    asInfo: [],
                    qsInfo: [],
                    detailInfo: [],
                },
                page: {
                    Unipolar_cutting: '单极电切 (W) 最大允差：',
                    Bipolar_resection: '双极电切 (W)：最大允差：',
                    Bipolar_coagulation: '双极电凝 (W)：最大允差：',
                    file_data: '',
                    templatename: '',
                    setting: {
                        heartRate: [],
                    },
                    nameplate: [],
                    instrument_view: [],
                    detail_result: [],
                    positions: 0,
                    is_display: 0,
                },
                form: {},
            }
        },
        methods: {
            onChange(index) {
                this.page.positions = index;
            },
            showImage() {
                ImagePreview({
                    images: this.page.nameplate,
                    startPosition: this.page.positions,
                });
            },
            showImg() {
                ImagePreview({
                    images: this.page.instrument_view,
                    startPosition: this.page.positions,
                });
            },
            afterRead(file) {
                let values = [];
                values.qsid = this.$route.query.qsid;
                values.base64 = file.content;
                values.action = 'upload';
                values.fileName = file.file.name;
                submit(Qs.stringify(values)).then(response => {
                    this.page.file_data = process.env.VUE_APP_BASE_PROXY_URL + response.path;
                    Toast.success(response.msg);
                    /*this.$router.push('/');*/
                })

            },
            getInfo(qsid) {
                let params = {qsid: qsid};
                getInfo(params).then(response => {
                    this.info.asInfo = response.asInfo;
                    this.info.qsInfo = response.qsInfo;
                    this.info.detailInfo = response.detailInfo;
                    this.page.setting = response.setting;
                    if (response.detailInfo.report) {
                        this.page.file_data = process.env.VUE_APP_BASE_PROXY_URL + response.detailInfo.report;
                    }
                    this.page.detail_result = response.detail_result;
                    for (let i in response.file_data.nameplate) {
                        this.page.nameplate.push(process.env.VUE_APP_BASE_PROXY_URL + response.file_data.nameplate[i].file_url);
                    }
                    for (let i in response.file_data.instrument_view) {
                        this.page.instrument_view.push(process.env.VUE_APP_BASE_PROXY_URL + response.file_data.instrument_view[i].file_url);
                    }
                    this.page.Unipolar_cutting = this.page.Unipolar_cutting + response.tolerance.Unipolar_cutting;
                    this.page.Bipolar_resection = this.page.Bipolar_resection + response.tolerance.Bipolar_resection;
                    this.page.Bipolar_coagulation = this.page.Bipolar_coagulation + response.tolerance.Bipolar_coagulation;
                    this.page.templatename = response.templatename;

                    this.page.is_display = 1;
                })
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.qsid);
        }
    }
</script>
<style scoped lang="scss">
    ::v-deep .van-field__control {
        color: red;
    }

    ::v-deep .van-field__label {
        width: 86%;
    }

    .background {
        background: rgba(69, 90, 100, 0.1);
    }

    .card-header {
        margin-top: 20px;
    }

    .mp {
        margin-top: 20px;
        padding-top: 10px;
        background: #fff;
    }

    .van-row {
        margin: 20px 0;
    }
</style>
