<template>
  <div class="list">
    <van-row>
      <van-col span="24">
        <div class="bgcf total-div">
          <div class="total-div-title" id="repair">
            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
            盘点计划查询列表
          </div>
        </div>
      </van-col>
    </van-row>


    <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="refreshList"
    >
      <van-card v-for="item in list" :key="item.inventory_plan_id">
        <template #title>
          盘点名称：{{ item.inventory_plan_name }}
        </template>
        <template #desc>
          <ul>
            <li>盘点单号：{{ item.inventory_plan_no }}</li>
            <li>盘点员：{{ item.inventory_users.join('、') }}</li>
            <li>状态：{{ item.inventory_plan_status_name }}</li>
          </ul>
        </template>
        <template #footer>
          <van-button type="info" block size="small" class="detail-button"
                      :to="{path: $store.getters.moduleName+'/Inventory/planShow',
                        query: { inventory_plan_id: item.inventory_plan_id }}"
          >
            {{item.inventory_plan_status_name}}
          </van-button>
        </template>
      </van-card>
    </van-list>
  </div>
</template>

<script>
import Qs from 'qs';
import {
  Cell,
  CellGroup,
  List,
  Button,
  Card,
  Search,
  Col,
  Row,
  Icon,
  DropdownMenu,
  DropdownItem,
  Divider,
  Pagination
} from "vant";
// import {getprogressList, postprogressList} from "@/api/common/progress";
import {getInventoryList} from "@/api/common/inventory";
import Sort from "@/components/Sort";

export default {
  name: 'inventory-list',
  components: {
    [List.name]: List,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Button.name]: Button,
    [Card.name]: Card,
    [Divider.name]: Divider,
    [Search.name]: Search,
    [Col.name]: Col,
    [Row.name]: Row,
    [Icon.name]: Icon,
    [Pagination.name]: Pagination,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    Sort,
  },
  data() {
    return {
      list: [],
      keyword: '',
      loading: true,
      finished: false,
      page: 1,
      limit: 5,
      total: 0,
      //全局列表搜索条件
      where: {
        limit: 10,
        page: 1
      },
      statusName: {
        "待盘点": "盘点",
        "正在盘点": "继续盘点",
      }
    };
  },
  methods: {
    page_change(value) {
      this.page = value
      //清空搜索条件
      this.where = {};
      //重新加搜索条件
      this.where = {page: value, limit: this.limit};
      this.getDataList();
    },
    getDataList() {
      this.where.limit = this.limit
      getInventoryList(this.where).then(response => {
        this.list = response.rows;
        this.total = response.total;
        this.loading = false;
        this.finished = true;
        if (this.total > 0) {
          this.page = Math.ceil(this.page * this.limit / response.total);
        }
      });
    },
    refreshList() {
      setTimeout(() => {
        getInventoryList(this.where).then(response => {
          console.log(response)
          if (response.code === 400) {
            this.loading = false;
            this.finished = true;
          }
          //先判断如果返回的总数为0 则把状态设置为停止加载
          if (response.total === 0) {
            this.finished = true;
          }
          //判断数据键值是否存在
          if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
            this.list.push(...response.rows);
          }
          //仅第一页赋值总数
          if (this.where.page === 1) {
            this.total = response.total;
          }
          //数据获取到了 停止加载
          this.loading = false;
          //全部加载了 且当前列表数量大于总数 设置完成状态
          if (this.list.length >= this.total) {
            this.finished = true;
          }
          //页数加1
          this.where.page++;
        });
      }, 100);
    }
  }, mounted() {
    this.refreshList()
    //获取相关设备信息
    // this.getDataList();
  }
}
</script>

<style scoped lang="scss">
.tips {
  color: #DEDEDE;
  margin-left: 20px;
}

.tips {
  .van-icon {
    top: 2px;
    padding-left: 8px;
  }
}
</style>
