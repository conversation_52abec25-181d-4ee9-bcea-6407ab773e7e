<template>
    <div class="list">
        <van-row>
            <div class="bgcf total-div">
                <div class="total-div-title">
                    <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                    应急预案 <span class="num"> {{total}} </span>
                </div>
            </div>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="refreshList"
        >
            <van-card :thumb="item.file_type" v-for="item in list" :key="item.arempid" @click="onclick(item.arempid)">
                <template #title>
                    {{item.emergency}}
                </template>
                <template #desc>
                    <ul>
                        <li></li>
                        <li class="cli">
                            预案类型：
                            <van-tag plain type="primary">{{item.name}}</van-tag>
                        </li>
                        <li class="cli">发布时间：{{item.add_time}}</li>
                    </ul>
                </template>
            </van-card>
        </van-list>
    </div>
</template>

<script>
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem, Tag} from "vant";
    import {getEmergencyList} from "@/api/archives/list";

    export default {
        name: 'getEmergencyList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            [Tag.name]: Tag,
        },
        data() {
            return {
                list: [],
                keyword: '',
                loading: false,
                finished: false,
                total: 0,
                //全局列表搜索条件
                where: {
                    page: 1
                }
            };
        },
        methods: {
            onclick(arempid) {
                this.$router.push(this.$store.getters.moduleName+'/Emergency/showEmergencyPlan?id=' + arempid);
            },
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                getEmergencyList(this.where).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished = true;
                    }
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'row')) {
                        this.list.push(...response.row);
                    }
                    //仅第一页赋值总数
                    if (this.where.page === 1) {
                        this.total = response.total;
                    }
                    //数据获取到了 停止加载
                    this.loading = false;
                    //全部加载了 且当前列表数量大于总数 设置完成状态
                    if (this.list.length >= this.total) {
                        this.finished = true;
                    }
                    //页数加1
                    this.where.page++;
                })
            }
        },
    }
</script>

<style scoped lang="scss">
    .list .van-card__thumb {
        height: 100px;
    }
    .cli{color:#969799;font-size: 14px;}
</style>
