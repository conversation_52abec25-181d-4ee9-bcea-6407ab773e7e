<template>
  <div class="repair-records">
    <div class="bgcf" style="padding: 20px 0 0;">
      <van-search
        v-model="keyword"
        shape="round"
        placeholder="搜索（设备名称、编号、科室）"
        @search="onSearch"
      />
    </div>
    <van-row>
      <van-col span="18">
        <div class="bgcf total-div">
          <div class="total-div-title">
            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
            我的维修记录 <span class="num"> {{total}} </span> 条
          </div>
        </div>
      </van-col>
      <van-col span="6">
        <Sort @order="order" :option="option"/>
      </van-col>
    </van-row>

    <!-- 空状态提示 -->
    <van-row v-if="list.length === 0 && !loading && finished">
      <div style="text-align: center; padding: 40px; color: #999;">
        <van-icon name="orders-o" size="48" color="#ddd"/>
        <p style="margin-top: 16px; font-size: 16px;">暂无维修记录</p>
        <van-button type="primary" @click="testAPI" size="small" style="margin-top: 10px;">重新加载</van-button>
      </div>
    </van-row>
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="refreshList"
    >
      <van-card v-for="item in list" :key="item.repid" @click="showRepairDetail(item)">
        <template #title>
          <van-cell>
            <!-- 使用 title 插槽来自定义标题 -->
            <template #title>
              <span class="custom-title">维修单号：{{item.repnum}}</span>
            </template>
            <template #right-icon>
              <van-tag :color="getStatusColor(item.status)">{{item.statusName}}</van-tag>
            </template>
          </van-cell>
        </template>
        <template #desc>
          <div class="atn">
            <span class="ct">设备名称：</span>
            <span class="cc">{{item.assets}}</span>
          </div>
          <div class="atn">
            <span class="ct">设备编号：</span>
            <span class="cc">{{item.assnum}}</span>
          </div>
          <div class="atn">
            <span class="ct">设备型号：</span>
            <span class="cc">{{item.model || '无'}}</span>
          </div>
          <div class="atn">
            <span class="ct">所属科室：</span>
            <span class="cc">{{item.department}}</span>
          </div>
          <div class="atn">
            <span class="ct">维修性质：</span>
            <span class="cc">{{item.repairTypeName || '未分类'}}</span>
          </div>
          <div class="atn">
            <span class="ct">报修时间：</span>
            <span class="cc">{{item.applicant_time}}</span>
          </div>
          <div class="atn">
            <span class="ct">故障描述：</span>
            <span class="cc">{{item.breakdown}}</span>
          </div>
        </template>
        <template #footer>
          <div class="gt">
            <van-row>
              <van-col span="10">报修人：{{item.applicant}}</van-col>
              <van-col span="14">维修工程师：{{item.engineer || '未分配'}}</van-col>
            </van-row>
          </div>
        </template>
      </van-card>
    </van-list>
    <ScrollTop/>
  </div>
</template>

<script>
import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem, Tag} from "vant";
import {getPersonalRepairRecords} from "@/api/repair/list";
import ScrollTop from "@/components/ScrollTop";
import Sort from "@/components/Sort";

export default {
  name: 'RepairRecords',
  components: {
    [List.name]: List,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Button.name]: Button,
    [Card.name]: Card,
    [Search.name]: Search,
    [Col.name]: Col,
    [Row.name]: Row,
    [Icon.name]: Icon,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    [Tag.name]: Tag,
    ScrollTop,
    Sort
  },
  data() {
    return {
      list: [],
      option: [
        {text: '按报修时间（降序）', value: {sort: 'applicant_time', order: 'desc'}},
        {text: '按维修状态（降序）', value: {sort: 'status', order: 'desc'}},
        {text: '按报修时间（升序）', value: {sort: 'applicant_time', order: 'asc'}},
        {text: '按维修状态（升序）', value: {sort: 'status', order: 'asc'}},
      ],
      tips: [],
      keyword: '',
      orderValue: '',
      loading: false,
      finished: false,
      total: 0,
      //全局列表搜索条件
      where: {
        offset: 0,
        limit: 10,
      }
    };
  },
  mounted() {
    console.log('维修记录页面已挂载');
    // 初始化数据
    this.initData();
    // van-list 会自动触发第一次 @load 事件，不需要手动调用
  },
  methods: {
    // 初始化数据
    initData() {
      console.log('初始化数据');
      this.list = [];
      this.finished = false;
      this.total = 0;
      this.where = {
        offset: 0,
        limit: 10
      };
    },
    getStatusColor(status) {
      // 根据状态返回对应的颜色
      const statusColors = {
        '1': '#ff9500', // 待接单
        '2': '#1989fa', // 已接单
        '3': '#07c160', // 维修中
        '4': '#ee0a24', // 待审核
        '5': '#ff9500', // 审核中
        '6': '#07c160', // 审核通过
        '7': '#ee0a24', // 审核不通过
        '8': '#969799', // 已完成
        '9': '#ee0a24'  // 已取消
      };
      return statusColors[status] || '#969799';
    },
    showRepairDetail(item) {
      // 可以跳转到维修详情页面
      this.$router.push({
        path: '/P/Repair/showRepairDetails',
        query: {
          repid: item.repid
        }
      }).catch(err => { 
        if (err.name !== 'NavigationDuplicated') {
          throw err
        }
      });
    },
    //刷新列表 第一次进入自动触发
    refreshList() {
      console.log('refreshList 被调用，当前状态:', {
        finished: this.finished,
        loading: this.loading,
        listLength: this.list.length,
        offset: this.where.offset
      });

      // 如果已经加载完成，不再请求
      if (this.finished) {
        console.log('数据已加载完成，跳过请求');
        return;
      }

      console.log('发送请求参数:', this.where);

      // 将对象转换为 URLSearchParams 格式
      const formData = new URLSearchParams();
      Object.keys(this.where).forEach(key => {
        formData.append(key, this.where[key]);
      });

      // 异步更新数据
      getPersonalRepairRecords(formData).then(response => {
        console.log('接口响应数据:', response);

        // 处理响应数据
        const rows = response.rows || [];
        const total = parseInt(response.total) || 0;

        console.log('处理后的数据:', { rows, total, rowsLength: rows.length });

        // 添加数据到列表
        if (rows && Array.isArray(rows) && rows.length > 0) {
          this.list.push(...rows);
        }

        // 设置总数（仅第一页）
        if (this.where.offset === 0) {
          this.total = total;
        }

        // 检查是否还有更多数据
        if (rows.length === 0 || this.list.length >= total) {
          this.finished = true;
        } else {
          // 更新offset准备下次请求
          this.where.offset += this.where.limit;
        }

        // 停止加载状态
        this.loading = false;

      }).catch(error => {
        console.error('获取维修记录失败:', error);
        this.loading = false;
        this.finished = true;
      });
    },
    onSearch(keyword) {
      console.log('搜索触发:', keyword);
      this.list = [];
      this.finished = false;
      //清空搜索条件
      this.where = {};
      //重新加搜索条件
      this.where = {offset: 0, limit: 10, search: keyword};
      // van-list 会自动检测到 finished 状态变化并触发 @load 事件
    },
    order(value) {
      console.log('排序触发:', value);
      this.list = [];
      this.finished = false;
      //清空搜索条件
      this.where = {};
      //重新加搜索条件
      this.where = {
        offset: 0,
        limit: 10,
        sort: value.sort,
        order: value.order
      };
      if (this.keyword) {
        this.where.search = this.keyword;
      }
      // van-list 会自动检测到 finished 状态变化并触发 @load 事件
    },
    // 测试API调用
    testAPI() {
      console.log('手动测试API调用');
      this.list = [];
      this.finished = false;
      this.where = { offset: 0, limit: 10 };
      // van-list 会自动检测到 finished 状态变化并触发 @load 事件
    }
  }
}
</script>

<style scoped lang="scss">
.custom-title{
  font-size: 18px;
}
.van-card{
  padding: 8px 0;
}
.van-cell {
  padding: 10px 0;
}
.van-card__header{padding: 8px 16px;}
.van-card__footer{background:#FAFAFA;padding-left: 16px;}
.atn{margin-top: 5px;}
.ct{font-size: 14px;}
.cc{font-size: 14px;color:#B0B9BD;}
.gt{height: 45px;line-height: 45px;font-size: 14px;color:#B0B9BD;overflow: hidden;}
.van-card__footer{
  text-align: left;
}

.bgcf {
  background-color: #fff;
}

.total-div {
  padding: 15px;
}

.total-div-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.num {
  color: #1989fa;
  margin: 0 5px;
}
</style>
