<template>
    <div class="list">
        <van-row>
            <van-col span="18">
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        借调逾期列表 <span class="num"> {{total}} </span> 台
                    </div>
                </div>
            </van-col>
            <van-col span="6">
                <van-dropdown-menu class="filter">
                    <van-dropdown-item title="排序" v-model="orderValue" :options="option" @change="order"/>
                </van-dropdown-menu>
            </van-col>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="refreshList"
        >
            <van-card :thumb="item.pic_url" v-for="item in list" :key="item.assid">
                <template #desc>
                    <div class="jumpButton">
                        <van-button type="info" size="small" @click="onclick(item.borid)">点击催还</van-button>
                    </div>
                    <ul>
                        <li>
                            <span >流水号：</span>
                            <span class="text">{{item.borrow_num}}</span>
                        </li>
                        <li>
                            <span >申请科室：</span>
                            <span class="text">{{item.apply_department}}</span>
                        </li>
                        <li>
                            <span >申请人：</span>
                            <span class="text">{{item.apply_user}}</span>
                        </li>
                        <li>
                            <span >申请时间：</span>
                            <span class="text">{{item.apply_time}}</span>
                        </li>
                        <li>
                            <span >借调原因：</span>
                            <span v-html="item.borrow_reason"/>
                        </li>
                        <li>
                            <span>预计归还时间：</span>
                            <span v-html="item.estimate_back"/>
                        </li>
                        <li>
                            <span>逾期时长：</span>
                            <span v-html="item.overdue"/>
                        </li>
                    </ul>
                </template>
                <template #footer>
                    <van-button type="primary" block size="small" class="detail-button" :to="{ path: $store.getters.moduleName+'/Borrow/applyBorrow', query: { assid: item.assid }}" v-if="item.url">申请借调</van-button>
                </template>
            </van-card>
        </van-list>
        <ScrollTop/>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem,Toast} from "vant";
    import {getReminderList,sendOutReminder} from "@/api/assets/borrow/list";
    import ScrollTop from "@/components/ScrollTop";


    export default {
        name: 'getReminderList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            ScrollTop
        },
        data() {
            return {
                list: [],
                keyword: '',
                orderValue: '',
                loading: false,
                finished: false,
                total: '',
                option: [
                    {text: '按科室名称（降序）', value: 'department-desc'},
                    {text: '按科室设备数量（降序）', value: 'assetssum-desc'},
                    {text: '按设备状态（降序）', value: 'status-desc'},
                    {text: '按设备启用时间（降序）', value: 'opendate-desc'},
                    {text: '按科室名称（升序）', value: 'department-asc'},
                    {text: '按科室设备数量（升序）', value: 'assetssum-asc'},
                    {text: '按设备状态（升序）', value: 'status-asc'},
                    {text: '按设备启用时间（升序）', value: 'opendate-asc'},
                ],
                //全局列表搜索条件
                where: {
                    action: 'getReminderList',
                    page: 1
                }
            };
        },
        methods: {
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                setTimeout(() => {
                    getReminderList(this.where).then(response => {
                        //先判断如果返回的总数为0 则把状态设置为停止加载
                        if (response.total === 0) {
                            this.finished = true;
                        }
                        //判断数据键值是否存在
                        if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                            this.list.push(...response.rows);
                        }
                        //仅第一页赋值总数
                        if (this.where.page === 1) {
                            this.total = response.total;
                        }
                        //数据获取到了 停止加载
                        this.loading = false;
                        //全部加载了 且当前列表数量大于总数 设置完成状态
                        if (this.list.length >= this.total) {
                            this.finished = true;
                        }
                        //页数加1
                        this.where.page++;
                    })
                }, 100);
            },
            onclick(borid){
                let values = [];
                values.borid = borid;
                sendOutReminder(Qs.stringify(values)).then(response => {
                    Toast.success(response.msg);
                })
            },
            onSearch(keyword) {
                this.resetWhere();
                this.finished = false;
                //重新加搜索条件
                this.where.search = keyword;
                this.refreshList();
            },
            order(value) {
                this.resetWhere(false);
                //重新加搜索条件
                this.where.page = 1;
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.refreshList();
            },
            //重置列表条件
            resetWhere(clearWhere = true) {
                this.list = [];//清空列表
                if (clearWhere) {
                    //清空搜索条件
                    this.where = {};
                }
                //重置为第一页
                this.where.page = 1;
                this.where.action = 'showReminderList';
                //重置表格为加载状态
                this.loading = true;
                this.finished = false;
            }
        }
    }
</script>

<style scoped lang="scss">
    .list {
        .total-div {
            margin-top: 0;
        }

        ::v-deep .filter {
            margin-top: 0;
        }
    }
</style>
