<template>
    <div v-if="page.is_display===1">
        <van-divider>配件库存信息</van-divider>
        <van-form ref="partsForm" @submit="onSubmit">
        <van-tabs :border="false">
            <van-tab title="配件库存信息">
                <van-cell title="配件名称 " :value="form.parts"/>
                <van-cell title="配件型号 " :value="form.model"/>
                <van-cell title="库存数量 " :value="form.stock"/>
                <van-field name="price" type="number" label="配件单价" :rules="[{ validator:price, message: '请填写配件单价' }]" placeholder="请填写配件单价" :value="form.price" @input="edit_price"/>
                <van-field
                        readonly
                        clickable
                        :value="form.supplier_name"
                        label="供应商"
                        placeholder="点击选择供应商"
                        :rules="[{ validator:supplier_name, message: '请选择供应商',trigger:'onChange'}]"
                        @click="page.showSelectsupplier = true"
                />
                <van-popup v-model="page.showSelectsupplier" position="bottom">
                    <van-picker
                            show-toolbar
                            :columns="page.supplierColumns"
                            @confirm="onConfirmsupplier"
                            @cancel="page.showSelectsupplier = false"
                    />
                </van-popup>
            </van-tab>
        </van-tabs>
        <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">
            <van-icon size="16px" style="margin-right: 5px;" :name="require('@/assets/images/form/form.png')"/>
            配件入库表单
        </van-divider>
            <van-cell-group>
               <van-field name="sum" :rules="[{ required: true, message: '请填写入库数量' }]" type="digit" label="采购入库数量" :value="form.sum" @input="edit_sum" placeholder="请填写入库数量"/>
               <van-field name="tprice"  type="number" label="费用合计"  placeholder="填写配件数量和单价会自动计算"  :value="form.tprice" readonly/>

            </van-cell-group>
            <div style="margin: 16px;">
                <van-button round block type="info" native-type="submit">确认并入库</van-button>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px" />
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Divider, Cell, CellGroup, Icon, Field, Form, Button, Tab, Tabs, Toast,Loading,Popup,Picker,DatetimePicker} from 'vant';
    import {getInfo,submit} from "@/api/repair/apply";

    export default {
        name: 'applyScrap',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Loading.name]: Loading,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [DatetimePicker.name]: DatetimePicker,
        },
        data() {
            return {
                info: {
                },
                page:{
                    supplier:[],//科室列表
                    supplierColumns: [],//科室选项
                    is_display:0,//用于判断是否加载完数据
                    showSelectsupplier: false,//科室控件显示
                },
                form:{
                    parts:'',
                    model:'',
                    stock:'',
                    price:'',
                    supplier_id:'',
                    supplier_name:'',
                    tprice:'',
                    sum:'',
                    inwareid:'',
                    leader:'',
                    addtime:'',
                    apply_sum:'',
                },
            }
        },
        methods: {
            price(val){
                if (Number(val)>0) {
                    return true;
                }
                return false;
            },
            supplier_name(val){
                if (val=='/') {
                    return false;
                }
                return true;
            },
            //选择供应商
            onConfirmsupplier(value) {
                this.form.supplier_name = value;
                for(var i in this.page.supplier)
                {
                    if (this.page.supplier[i].title==value) {
                        this.form.supplier_id = this.page.supplier[i].value;
                    }
                }
                this.page.showSelectsupplier = false;
            },
            edit_price(value){
                this.form.price = value;
                if (this.form.sum!='') {
                   this.form.tprice = this.form.price*this.form.sum;
                }
            },
            edit_sum(value){
                this.form.sum = value;
                if (this.form.price!='') {
                   this.form.tprice = this.form.price*this.form.sum;
                }
            },
            getInfo(repid,model,parts,stock) {
                    let params = {repid:repid,model:model,parts:parts,stock:stock};
                    getInfo(params).then(response => {
                        this.page.supplier = response.sups;
                        this.page.supplierColumns = response.supsColumns;
                        this.form.inwareid = response.inwareid;
                        this.form.leader = response.leader;
                        this.form.addtime = response.addtime;
                        this.form.apply_sum = response.min_sum;
                        this.page.is_display = 1;
                    })
            },
            onSubmit(values) {
                //发送请求
                    values.parts = this.form.parts;
                    values.action = 'partsInWareApply';
                    values.model = this.form.model;
                    values.stock = this.form.stock;
                    values.price = this.form.price;
                    values.supplier_id = this.form.supplier_id;
                    values.supplier_name = this.form.supplier_name;
                    values.tprice = this.form.tprice;
                    values.sum = this.form.sum;
                    values.inwareid = this.form.inwareid;
                    values.leader = this.form.leader;
                    values.addtime = this.form.addtime;
                    values.apply_sum = this.form.apply_sum;
                    if (values.price <= 0) {
                        Toast.fail('请填写的合理价格');
                        return false;
                    }
                    if (values.sum=="") {
                        Toast.fail('请填写入库数量');
                        return false;
                    }
                    if (values.sum<values.apply_sum) {
                        Toast.fail('入库数量不可少于'+values.apply_sum);
                        return false;
                    }
                    let _this = this;
                submit(Qs.stringify(values)).then(response => {
                    if (response.status==1) {
                    Toast({
                            type:'success',//失败fail
                            duration:2000,//2秒
                            message: response.msg,
                            icon:'success',//失败cross
                            forbidClick:true,//是否禁止背景点击，避免重复提交
                            onClose(){
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/RepairParts/partsOutWare?repid='+_this.$route.query.repid);
                            }
                        });
                }
                })
            },
        }, mounted() {
            this.form.parts = this.$route.query.parts;
            this.form.model = this.$route.query.model;
            this.form.stock = this.$route.query.stock;
            this.form.price = this.$route.query.price;
            this.form.supplier_id = this.$route.query.sid;
            this.form.supplier_name = this.$route.query.sname;
            //获取相关设备信息
            this.getInfo(this.$route.query.repid,this.$route.query.model,this.$route.query.parts,this.$route.query.stock);

        }
    }
</script>

<style scoped lang="scss">
    .van-button {
        margin-top: 10px;
    }
</style>
