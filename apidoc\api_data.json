[{"type": "Get", "url": "/Login/login", "title": "登录验证接口", "version": "0.1.0", "name": "login", "group": "登录", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": false, "field": "username", "description": "<p>用户名</p>"}, {"group": "Parameter", "type": "String", "optional": false, "field": "password", "description": "<p>密码</p>"}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "status", "description": "<p>状态码</p>"}, {"group": "Success 200", "type": "String", "optional": false, "field": "msg", "description": "<p>说明信息</p>"}, {"group": "Success 200", "type": "String", "optional": false, "field": "token", "description": "<p>token值</p>"}]}, "examples": [{"title": "响应结果实例", "content": "{\n  \"status\": 1,\n  \"msg\": 登录成功\n  \"token\": abcdefghijklmnobqrstuvwxyz\n}", "type": "json"}]}, "filename": "src/doc_file/login/login/login.js", "groupTitle": "登录"}, {"type": "Get", "url": "/Transfer/getList", "title": "获取转科申请列表", "version": "0.1.0", "name": "getList", "group": "设备管理", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Authorization", "description": "<p>token值</p>"}]}}, "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "optional": false, "field": "page", "description": "<p>页数</p>"}, {"group": "Parameter", "type": "Number", "optional": false, "field": "limit", "description": "<p>每页条数</p>"}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "total", "description": "<p>列表总条数</p>"}, {"group": "Success 200", "type": "Object", "optional": false, "field": "data", "description": "<p>列表数据</p>"}, {"group": "Success 200", "type": "String", "optional": false, "field": "data.assets", "description": "<p>设备名称</p>"}, {"group": "Success 200", "type": "String", "optional": false, "field": "data.model", "description": "<p>设备规格型号</p>"}, {"group": "Success 200", "type": "String", "optional": false, "field": "data.assnum", "description": "<p>设备编号</p>"}, {"group": "Success 200", "type": "String", "optional": false, "field": "data.department", "description": "<p>设备科室名称</p>"}, {"group": "Success 200", "type": "String", "optional": false, "field": "data.status_name", "description": "<p>设备状态</p>"}, {"group": "Success 200", "type": "String", "optional": false, "field": "data.pic_url", "description": "<p>设备图片地址</p>"}]}, "examples": [{"title": "响应结果实例", "content": "{\n  \"status\": 1,\n  \"total\": 1000,\n  \"data\": {\n     assets: \"中心供氧系统\",\n     model: \"A1\",\n     assnum: \"12622346\",\n     department: \"康复医学科\",\n     status_name: \"转科中\",\n     pic_url: \"/Public/uploads/assets/20200526/5eccac5c00b08.jpg\",\n  }\n}", "type": "json"}]}, "filename": "src/doc_file/assets/transfer/get-list.js", "groupTitle": "设备管理"}, {"type": "Post", "url": "/Notin/getMenus", "title": "获取首页菜单", "description": "<p>组织数据8个菜单为1组</p>", "version": "0.1.0", "name": "getMenus", "group": "首页", "success": {"examples": [{"title": "响应结果实例", "content": " {\n     \"status\": 1,\n     \"menu\": [\n          {\n               \"title\": \"设备管理\",\n               \"swipe\": [\n                    {\n                         \"menu\": [\n                              {\n                                   \"actionname\": \"设备查询\",\n                                   \"actionurl\": \"//Lookup/getAssetsList.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/sbcx.png\"\n                              },\n                              {\n                                   \"actionname\": \"借调申请\",\n                                   \"actionurl\": \"//Borrow/borrowAssetsList.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/jdsq.png\"\n                              },\n                              {\n                                   \"actionname\": \"借入验收\",\n                                   \"actionurl\": \"//Borrow/borrowInCheckList.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/qrjr.png\"\n                              },\n                              {\n                                   \"actionname\": \"归还验收\",\n                                   \"actionurl\": \"//Borrow/giveBackCheckList.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/ghys.png\"\n                              },\n                              {\n                                   \"actionname\": \"归还验收\",\n                                   \"actionurl\": \"//Borrow/giveBackCheckList.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/jdyq.png\"\n                              },\n                              {\n                                   \"actionname\": \"转科申请\",\n                                   \"actionurl\": \"//Transfer/getList.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/zksq.png\"\n                              },\n                              {\n                                   \"actionname\": \"转科进程\",\n                                   \"actionurl\": \"//Transfer/progress.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/zkjc.png\"\n                              },\n                              {\n                                   \"actionname\": \"转科验收\",\n                                   \"actionurl\": \"//Transfer/checkLists.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/zkys.png\"\n                              }\n                         ]\n                    },\n                    {\n                         \"menu\": [\n                              {\n                                   \"actionname\": \"报废申请\",\n                                   \"actionurl\": \"//Scrap/getApplyList.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/bfsq.png\"\n                              },\n                              {\n                                   \"actionname\": \"报废查询\",\n                                   \"actionurl\": \"//Scrap/getScrapList.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/bfcx.png\"\n                              },\n                              {\n                                   \"actionname\": \"档案盒管理\",\n                                   \"actionurl\": \"//Box/boxList.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/smda.png\"\n                              },\n                              {\n                                   \"actionname\": \"标签核实\",\n                                   \"actionurl\": \"//Print/verify.html\",\n                                   \"icon\": \"http://www.tecev-git.com//Public/mobile/images/icon/byys.png\"\n                              }\n                         ]\n                    }\n               ]\n          }\n     ]\n}", "type": "json"}]}, "filename": "src/doc_file/index/index.js", "groupTitle": "首页"}]