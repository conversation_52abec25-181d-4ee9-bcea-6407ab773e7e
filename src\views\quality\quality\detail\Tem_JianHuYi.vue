<template>
    <div class="con_detail" v-if="page.is_display===1">
        <Quality :info="info"/>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">质控检测明细（模板：{{page.templatename}}）</h2>
        </div>

        <van-form ref="qualityForm" @submit="onSubmit">
            <van-field name="lookslike" label="外观功能：">
                <template #input>
                    <van-radio-group @change="looklike_change" v-model="form.lookslike" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field v-show="show_lookslike_desc" name="lookslike_desc" type="textarea" rows="2" autosize v-model="form.lookslike_desc" placeholder="外观不符合的请说明情况" style="margin-bottom: 10px;"/>

            <van-collapse v-model="page.activeNames">
            <van-collapse-item :title="page.heartRate" name="1">
                <van-row v-for="(item,key) in page.setting.heartRate" :key="key">
                    <van-field :label="'设定值：'+item" v-model="page.heartRatevalue[key]" type="number" placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            <van-collapse-item :title="page.breathRate" name="2">
                <van-row v-for="(item,key) in page.setting.breathRate" :key="key">
                    <van-field :label="'设定值：'+item" v-model="page.breathRatevalue[key]" type="number" placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            <van-collapse-item :title="page.pressure" name="3">
                <van-row v-for="(item,key) in page.setting.pressure" :key="key">
                    <van-field :label="'设定值：'+item" v-model="page.pressurevalue[key]" placeholder="填写格式：75/45(55)"/>
                </van-row>
            </van-collapse-item>
            <van-collapse-item :title="page.BOS" name="4">
                <van-row v-for="(item,key) in page.setting.BOS" :key="key">
                    <van-field :label="'设定值：'+item" v-model="page.BOSvalue[key]" type="number" placeholder="测量值"/>
                </van-row>
            </van-collapse-item>
            </van-collapse>

            <van-field name="audible_and_visual_alarm" label="声光报警:">
                <template #input>
                    <van-radio-group v-model="form.audible_and_visual_alarm" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field name="alarm_limit" label="报警限检查:">
                <template #input>
                    <van-radio-group v-model="form.alarm_limit" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field name="mute" label="静音检查:">
                <template #input>
                    <van-radio-group v-model="form.mute" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field name="total_desc" type="textarea" rows="3" show-word-limit maxlength="120" label="检测备注：" v-model="form.total_desc" placeholder="请填写检测备注（如偏离情况说明）"/>

            <div class="mp">
                <van-divider>设备铭牌照片</van-divider>
                <van-row type="flex" justify="center">
                    <van-uploader multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" :before-read="beforeRead" v-model="form.nameplate_fileList" :after-read="nameplate_afterRead"
                                  :before-delete="del_nameplate">
                    </van-uploader>
                </van-row>
                <van-divider>检测仪器视图照片</van-divider>
                <van-row type="flex" justify="center">
                    <van-uploader multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" :before-read="beforeRead" v-model="form.instrument_fileList" :after-read="instrument_afterRead"
                                  :before-delete="del_instrument">
                    </van-uploader>
                </van-row>
                <div style="margin: 16px;">
                    <van-button round block color="#FFB800" native-type="button" @click="changeFormType('keepquality')"
                                style="margin-bottom: 16px;">
                        暂时保存
                    </van-button>
                    <van-button round block type="primary" native-type="button" @click="changeFormType('end')">
                        确认并提交
                    </van-button>
                </div>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>
<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Row,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Toast,
        Loading,
        Uploader,
        ImagePreview,
        Image as VanImage,
        Swipe,
        SwipeItem,
        RadioGroup,
        Radio,
        Col,
        Stepper,
        Notify,
        Collapse,
        CollapseItem
    } from 'vant';
    import {getInfo, submit} from "@/api/quality/detail";
    import Quality from "@/components/Quality";

    export default {
        name: 'ChuChanYi',
        components: {
            [Divider.name]: Divider,
            [Collapse.name]: Collapse,
            [CollapseItem.name]: CollapseItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Row.name]: Row,
            [Col.name]: Col,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Uploader.name]: Uploader,
            [Tab.name]: Tab,
            [Stepper.name]: Stepper,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Tabs.name]: Tabs,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [VanImage.name]: VanImage,
            [Loading.name]: Loading,
            Quality,
        },
        data() {
            return {
                info: {
                    asInfo: [],
                    qsInfo: [],
                },
                page: {
                    heartRate: '心率（次/min）：最大允差：',
                    breathRate: '呼吸率 (次/min) 最大允差：',
                    pressure: '无创血压 (mmHg) 最大允差：',
                    BOS: '血氧饱和度 (%) 最大允差：',
                    file_data: '',
                    heartRatevalue: [],
                    breathRatevalue: [],
                    pressurevalue: [],
                    BOSvalue: [],
                    setting: {
                        heartRate: [],
                        breathRate: [],
                    },
                    templatename: '',
                    nameplate: [],
                    activeNames:[],
                    instrument_view: [],
                    detail_result: [],
                    positions: 0,
                    is_display: 0,
                  max_count:3
                },
                form: {
                    lookslike: "1",
                    type: 'keepquality',
                    total_desc: "",
                    audible_and_visual_alarm: '1',
                    alarm_limit: '1',
                    mute: '1',
                    nameplate_fileList: [],
                    instrument_fileList: [],
                },
                show_lookslike_desc:false,
            }
        },
        methods: {
            looklike_change(value){
                if(value == 2){
                    this.show_lookslike_desc = true;
                }else{
                    this.show_lookslike_desc = false;
                    this.form.lookslike_desc = '';
                }
            },
            onChange(item, key) {
                let tolerance = Math.abs(item - this.page.energesisvalue[key]);
                this.page.tolerancevalue[key] = tolerance;
            },
            show_nameplate_Image(key) {
                ImagePreview({
                    images: this.page.nameplate,
                    startPosition: key,
                });
            },
            show_instrument_Image(key) {
                ImagePreview({
                    images: this.page.instrument_view,
                    startPosition: key,
                });
            },
            on_nameplate_Change(index) {
                this.page.positions = index;
            },
            on_instrument_Change(index) {
                this.page.positions = index;
            },
            nameplate_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                //上传图片
                let values = [];
                values.type = 'nameplate';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';

                        this.form.nameplate_fileList.pop();
                        this.form.nameplate_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                });
            },
          beforeRead(file){
            if (file.length > this.page.max_count){
              Notify({ type: 'danger', message: `最多只能选择${this.page.max_count}个图片` });
              return  false
            }
            return true
          },
            instrument_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                let values = [];
                values.type = 'instrument_view';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';

                        this.form.instrument_fileList.pop();
                        this.form.instrument_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                });
            },
            oversize(){
                //超出限制大小
                Notify({ type: 'danger', message: '图片超出10M大小限制' });
                return false;
            },
            del_nameplate(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            del_instrument(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            changeFormType(type) {
                this.form.type = type;
                this.$refs.qualityForm.submit();
            },
            onSubmit(values) {
                if(values.lookslike == 2){
                    //外观不符合的要说明情况
                    if(!values.lookslike_desc.trim()){
                        Notify({ type: 'danger', message: '外观不符合的请说明情况' });
                        return false;
                    }
                }
                values.heartRate = this.page.heartRatevalue;
                values.breathRate = this.page.breathRatevalue;
                values.pressure = this.page.pressurevalue;
                values.BOS = this.page.BOSvalue;
                values.qsid = this.$route.query.qsid;
                values.action = this.form.type;

                //心率
                for(let i = 0;i < this.page.setting.heartRate.length;i++){
                    if(!values.heartRate[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的心率测量值' });
                        return false;
                    }else if (!values.heartRate[i]) {
                        values.heartRate[i] = "";
                    }
                }
                //呼吸率
                for(let i = 0;i < this.page.setting.breathRate.length;i++){
                    if(!values.breathRate[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的呼吸率测量值' });
                        return false;
                    }else if (!values.breathRate[i]) {
                        values.breathRate[i] = "";
                    }
                }

                //无创血压
                for(let i = 0;i < this.page.setting.pressure.length;i++){
                    if(!values.pressure[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的无创血压测量值' });
                        return false;
                    }else if (!values.pressure[i]) {
                        values.pressure[i] = "";
                    }
                }

                //血氧饱和度
                for(let i = 0;i < this.page.setting.BOS.length;i++){
                    if(!values.BOS[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的血氧饱和度测量值' });
                        return false;
                    }else if (!values.BOS[i]) {
                        values.BOS[i] = "";
                    }
                }

                let _this = this;
                submit(Qs.stringify(values)).then(res => {
                    if (res.status == 1) {
                        Toast({
                            type: 'success',//失败fail
                            duration: 2000,//2秒
                            message: res.msg,
                            icon: 'success',//失败cross
                            forbidClick: true,//是否禁止背景点击，避免重复提交
                            onClose() {
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Quality/qualityDetailList');
                            }
                        });
                    }
                })
            },
            getInfo(qsid) {
                let params = {qsid: qsid};
                getInfo(params).then(response => {
                    this.info.asInfo = response.asInfo;
                    this.info.qsInfo = response.qsInfo;
                    this.page.templatename = response.templatename;
                    for (let i in response.setting) {
                        if (response.setting[i].detection_Ename == 'heartRate') {
                            this.page.setting.heartRate = response.setting[i].set;
                        }
                        if (response.setting[i].detection_Ename == 'breathRate') {
                            this.page.setting.breathRate = response.setting[i].set;
                        }
                        if (response.setting[i].detection_Ename == 'pressure') {
                            this.page.setting.pressure = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.pressurevalue[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'BOS') {
                            this.page.setting.BOS = response.setting[i].set;
                        }
                    }
                    if (response.detail_data != null && response.detail_data.fixed_detection) {
                        this.page.heartRatevalue = response.detail_data.fixed_detection.heartRate;
                        this.page.breathRatevalue = response.detail_data.fixed_detection.breathRate;
                        this.page.pressurevalue = response.detail_data.fixed_detection.pressure;
                        this.page.BOSvalue = response.detail_data.fixed_detection.BOS;
                        this.form.lookslike = response.detail_data.fixed_detection.lookslike;
                        if(response.detail_data.fixed_detection.lookslike == 2){
                            this.show_lookslike_desc = true;
                            this.form.lookslike_desc = response.detail_data.fixed_detection.lookslike_desc;
                        }
                        this.form.audible_and_visual_alarm = response.detail_data.fixed_detection.audible_and_visual_alarm;
                        this.form.alarm_limit = response.detail_data.fixed_detection.alarm_limit;
                        this.form.mute = response.detail_data.fixed_detection.mute;
                        this.form.total_desc = response.detail_data.fixed_detection.total_desc;
                    }
                    if (response.file_data) {
                        for (let i in response.file_data.nameplate) {
                            this.form.nameplate_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.nameplate[i].file_url,
                                id: response.file_data.nameplate[i].file_id
                            });
                        }
                        for (let i in response.file_data.instrument_view) {
                            this.form.instrument_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.instrument_view[i].file_url,
                                id: response.file_data.instrument_view[i].file_id
                            });
                        }

                    }
                    this.page.heartRate = this.page.heartRate + response.tolerance.heartRate;
                    this.page.breathRate = this.page.breathRate + response.tolerance.breathRate;
                    this.page.pressure = this.page.pressure + response.tolerance.pressure;
                    this.page.BOS = this.page.BOS + response.tolerance.BOS;
                    this.page.is_display = 1;
                });
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.qsid);
        }
    }
</script>
<style scoped lang="scss">
    .background {
        background: rgba(69, 90, 100, 0.1);
    }

    ::v-deep .text_length .van-cell__title {
        width: 95px
    }
    .van-row{
        ::v-deep .van-field__label {
            width: 60%;
        }
    }

    .font-lable {
        color: #323233;
        font-size: 14px;
        line-height: 24px;
        padding: 0px 16px;
    }

    .card-header{margin-top: 20px;}
    .mp{
        margin-top: 20px;
        padding-top: 10px;
        background: #fff;
    }
</style>
