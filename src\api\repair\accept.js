import request from '@/utils/request'


export function getInfo(query) {
    return request({
        url: '/Repair/accept',
        params:query
    })
}

export function submit(data) {
    return request({
        url: '/Repair/accept',
        method: 'post',
        data
    })
}
export function get_real_location(data) {
    return request({
        url: '/Notin/get_gps',
        method: 'post',
        data
    })
}
