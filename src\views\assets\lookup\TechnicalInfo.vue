<template>
  <div class="AssetsInfo">
    <van-cell-group title="技术资料">
      <div v-for="item in info.techni_files" :key="item.fileid">
        <van-cell title="文件名称 ">
          <template #default>
            <a style="color: #4994df" size="small" type="info" v-if="item.file_type === 'pdf'"
               @click="scan_file(item.file_url)">{{item.file_name}}
            </a>
            <a style="color: #4994df" size="small" type="info" v-if="item.file_type === 'jpg'||item.file_type === 'png'"
               @click="img(item.file_url)">{{item.file_name}}
            </a>
          </template>
        </van-cell>
        <van-cell title="上传日期 " :value="item.add_time"></van-cell>
      </div>
    </van-cell-group>
  </div>
</template>
<script>
import { Cell, CellGroup, ImagePreview } from 'vant';

export default {
  components: {
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
  },
  methods: {
    img(url){
      ImagePreview([process.env.VUE_APP_BASE_PROXY_URL+url]);
    },
    scan_file(path) {
      path = process.env.VUE_APP_BASE_PROXY_URL+path;
      this.$router.push({
        name: 'showFile',
        query: {
          path: path
        }
      });
    },
  },
  props: ['info'],
}
</script>

