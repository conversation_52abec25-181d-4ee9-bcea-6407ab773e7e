<template>
    <div class="con_detail" v-if="page.is_display===1">
        <Quality :info="info"/>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">质控检测明细（模板：{{page.templatename}}）</h2>
        </div>

        <van-form ref="qualityForm" @submit="onSubmit">
            <van-field name="lookslike" label="外观功能：">
                <template #input>
                    <van-radio-group @change="looklike_change" v-model="form.lookslike" direction="horizontal">
                        <van-radio name="1">符合</van-radio>
                        <van-radio name="2">不符合</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field v-show="show_lookslike_desc" name="lookslike_desc" type="textarea" rows="2" autosize
                       v-model="form.lookslike_desc" placeholder="外观不符合的请说明情况" style="margin-bottom: 10px;"/>

            <van-collapse v-model="page.activeNames">
            <van-collapse-item title="Tx显示温度：34℃(每3分钟记录1次）" name="1">
                <van-row v-for="(item,key) in page.setting.Tx.slice(0,10)" :key="key">
                        <van-field :label="'测量值 '+(key+1)+'：'" v-model="page.Txvalue[key]" type="number" placeholder="测量值"
                                   @input="onTxChange"/>
                    </van-row>
                    <van-cell title="平均值" :value="'Txa='+form.Txa"/>
            </van-collapse-item>
            <van-collapse-item title="中心温度测试点T1：34℃(每3分钟记录1次）" name="2">
                <van-row v-for="(item,key) in page.setting.T1.slice(0,10)" v-show="page.T1show" :key="key">
                        <van-field :label="'测量值 '+(key+1)+'：'" v-model="page.T1value[key]" type="number" placeholder="测量值"
                                   @input="onT1Change"/>
                    </van-row>
                    <van-cell title="平均值" :value="'T1a='+form.T1a"/>
            </van-collapse-item>
            <van-collapse-item title="中心温度测试点T2：34℃(每3分钟记录1次）" name="3">
                <van-row v-for="(item,key) in page.setting.T2.slice(0,10)" v-show="page.T2show" :key="key">
                        <van-field :label="'测量值 '+(key+1)+'：'" v-model="page.T2value[key]" type="number" placeholder="测量值"
                                   @input="onT2Change"/>
                    </van-row>
                    <van-cell title="平均值" :value="'T2a='+form.T2a"/>
            </van-collapse-item>
            <van-collapse-item title="中心温度测试点T3：34℃(每3分钟记录1次）" name="4">
                <van-row v-for="(item,key) in page.setting.T3.slice(0,10)" v-show="page.T3show" :key="key">
                        <van-field :label="'测量值 '+(key+1)+'：'" v-model="page.T3value[key]" type="number" placeholder="测量值"
                                   @input="onT3Change"/>
                    </van-row>
                    <van-cell title="平均值" :value="'T3a='+form.T3a"/>
            </van-collapse-item>
            <van-collapse-item title="中心温度测试点T4：34℃(每3分钟记录1次）" name="5">
                <van-row v-for="(item,key) in page.setting.T4.slice(0,10)" v-show="page.T4show" :key="key">
                        <van-field :label="'测量值 '+(key+1)+'：'" v-model="page.T4value[key]" type="number" placeholder="测量值"
                                   @input="onT4Change"/>
                    </van-row>
                    <van-cell title="平均值" :value="'T4a='+form.T4a"/>
            </van-collapse-item>
            <van-collapse-item title="中心温度测试点T5：34℃(每3分钟记录1次）" name="6">
                <van-row v-for="(item,key) in page.setting.T5.slice(0,10)" v-show="page.T5show" :key="key">
                        <van-field :label="'测量值 '+(key+1)+'：'" v-model="page.T5value[key]" type="number" placeholder="测量值"
                                   @input="onT5Change"/>
                    </van-row>
                    <van-cell title="平均值" :value="'T5a='+form.T5a"/>
            </van-collapse-item>
            <van-collapse-item title="检测项目" name="7">
                <div>
                        <van-cell title="温度偏差值：" label="允许误差值：|Txa-T1a|≤0.8℃" :value="'实际偏差值：'+form.Temperature_deviation"/>
                        <van-cell title="温度均匀性:" label="允许误差值：|T2a/3a/4a/5a-T1a|max≤0.8℃" :value="'实际偏差值：'+form.Temperature_uniformity"/>
                        <van-cell title="波动度:" label="允许误差值：|T1s-T1a|max≤0.5℃" :value="'实际偏差值：'+form.Volatility"/>
                        <van-cell title="温控偏差:" label="允许误差值：|Tk-T1a|≤1.5℃" :value="'实际偏差值：'+form.Temperature_control_deviation"/>
                    </div>
            </van-collapse-item>
            <van-collapse-item title="声级检测" name="8">
                <div>
                        <van-cell title="箱内正常噪音检测：" value="允许误差值 ≤ 60dB"/>
                        <van-field v-model="form.Normal_noise_detection_in_the_box[0]"  placeholder="实际测量值"/>
                        <van-cell title="箱内报警噪音测试：" value="允许误差值 ≤ 80dB"/>
                        <van-field v-model="form.Alarm_noise_test_in_the_box[0]" placeholder="实际测量值"/>
                        <van-cell title="报警声级测试 (3米）：" value="允许误差值 ≤ 65dB"/>
                        <van-field v-model="form.Alarm_sound_level_test[0]" placeholder="实际测量值"/>
                    </div>
                    <van-cell title="湿度检测" value="容许误差： ＜ 10%"/>
                    <van-row>
                        <van-col span="12">
                            <van-field v-model="form.Humidity_detection[0]" placeholder="设定值"/>
                        </van-col>
                        <van-col span="12">
                            <van-field v-model="form.Humidity_detection[1]" placeholder="测量值"/>
                        </van-col>
                    </van-row>
            </van-collapse-item>
            </van-collapse>
            <div class="dc">
                <van-field name="Power_failure_alarm" label="断电报警:">
                    <template #input>
                        <van-radio-group v-model="form.Power_failure_alarm" direction="horizontal">
                            <van-radio name="1">符合</van-radio>
                            <van-radio name="2">不符合</van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-field name="Over_temperature_alarm" label="超温报警:">
                    <template #input>
                        <van-radio-group v-model="form.Over_temperature_alarm" direction="horizontal">
                            <van-radio name="1">符合</van-radio>
                            <van-radio name="2">不符合</van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-field name="total_desc" type="textarea" rows="3" show-word-limit maxlength="120" label="检测备注：" v-model="form.total_desc" placeholder="请填写检测备注（如偏离情况说明）"/>
            </div>

            <div class="mp">
                <van-divider>设备铭牌照片</van-divider>
                <van-row type="flex" justify="center">
                    <van-uploader multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" v-model="form.nameplate_fileList" :before-read="beforeRead" :after-read="nameplate_afterRead"
                                  :before-delete="del_nameplate">
                    </van-uploader>
                </van-row>

                <van-divider>检测仪器视图照片</van-divider>
                <van-row type="flex" justify="center">
                    <van-uploader multiple image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count" v-model="form.instrument_fileList" :before-read="beforeRead" :after-read="instrument_afterRead"
                                  :before-delete="del_instrument">
                    </van-uploader>
                </van-row>

                <div style="margin: 16px;">
                    <van-button round block color="#FFB800" native-type="button" @click="changeFormType('keepquality')"
                                style="margin-bottom: 16px;">
                        暂时保存
                    </van-button>
                    <van-button round block type="primary" native-type="button" @click="changeFormType('end')">
                        确认并提交
                    </van-button>
                </div>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>
<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Row,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Toast,
        Loading,
        Uploader,
        ImagePreview,
        Image as VanImage,
        Swipe,
        SwipeItem,
        RadioGroup,
        Radio,
        Col,
        Stepper,
        Notify,
        Collapse,
        CollapseItem
    } from 'vant';
    import {getInfo, submit} from "@/api/quality/detail";
    import Quality from "@/components/Quality";

    export default {
        name: 'ChuChanYi',
        components: {
            [Divider.name]: Divider,
            [Collapse.name]: Collapse,
            [CollapseItem.name]: CollapseItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Row.name]: Row,
            [Col.name]: Col,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Uploader.name]: Uploader,
            [Tab.name]: Tab,
            [Stepper.name]: Stepper,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Tabs.name]: Tabs,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [VanImage.name]: VanImage,
            [Loading.name]: Loading,
            Quality,
        },
        data() {
            return {
                info: {
                    asInfo: [],
                    qsInfo: [],
                },
                page: {
                    Txshow: true,
                    T1show: true,
                    T2show: true,
                    T3show: true,
                    T4show: true,
                    T5show: true,
                    dshow: true,
                    sshow: true,
                    Txicon: 'arrow-down',
                    T1icon: 'arrow-down',
                    T2icon: 'arrow-down',
                    T3icon: 'arrow-down',
                    T4icon: 'arrow-down',
                    T5icon: 'arrow-down',
                    dicon: 'arrow-down',
                    sicon: 'arrow-down',
                    file_data: '',
                    Txvalue: [],
                    T1value: [],
                    T2value: [],
                    T3value: [],
                    T4value: [],
                    T5value: [],
                    dvalue: [],
                    svalue: [],
                    setting: {
                        heartRate: [],
                        energesis: [],
                    },
                    templatename: '',
                    nameplate: [],
                    instrument_view: [],
                    detail_result: [],
                    positions: 0,
                    is_display: 0,
                    activeNames:[],
                  max_count:3
                },
                form: {
                    lookslike: "1",
                    type: 'keepquality',
                    total_desc: "",
                    Temperature_deviation: '',
                    Temperature_uniformity: '',
                    Volatility: '',
                    Temperature_control_deviation: '',
                    Power_failure_alarm: '1',
                    Over_temperature_alarm: '1',
                    Normal_noise_detection_in_the_box: [""],
                    Alarm_noise_test_in_the_box: [""],
                    Alarm_sound_level_test: [""],
                    Humidity_detection: ["", ""],
                    Txa: '',
                    T1a: '',
                    T2a: '',
                    T3a: '',
                    T4a: '',
                    T5a: '',
                    nameplate_fileList: [],
                    instrument_fileList: [],

                },
                show_lookslike_desc: false,
            }
        },
        methods: {
            looklike_change(value) {
                if (value == 2) {
                    this.show_lookslike_desc = true;
                } else {
                    this.show_lookslike_desc = false;
                    this.form.lookslike_desc = '';
                }
            },
            onTxChange() {
                let sum = 0;
                for (let i in this.page.Txvalue) {
                    sum = sum + Number(this.page.Txvalue[i]);
                }
                this.form.Txa = (sum / this.page.Txvalue.length).toFixed(3);
                this.form.Temperature_deviation = Math.abs(this.form.Txa - this.form.T1a).toFixed(3);
            },
            onT1Change() {
                let sum = 0;
                let T1s = [];
                for (let i in this.page.T1value) {
                    sum = sum + Number(this.page.T1value[i]);
                }
                this.form.T1a = (sum / this.page.T1value.length).toFixed(3);
                for (let i in this.page.T1value) {
                    T1s.push(Math.abs(this.page.T1value[i] - this.form.T1a).toFixed(3));
                }
                T1s.sort().reverse();
                let Temperature_uniformity = [];
                Temperature_uniformity.push(Math.abs(this.form.T2a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T3a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T4a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T5a - this.form.T1a).toFixed(3));
                Temperature_uniformity.sort().reverse();
                this.form.Temperature_deviation = Math.abs(this.form.Txa - this.form.T1a).toFixed(3);
                this.form.Temperature_uniformity = Temperature_uniformity[0];
                this.form.Volatility = T1s[0];
                this.form.Temperature_control_deviation = Math.abs(34 - parseFloat(this.form.T1a)).toFixed(3);
            },
            onT2Change() {
                let sum = 0;
                for (let i in this.page.T2value) {
                    sum = sum + Number(this.page.T2value[i]);
                }
                this.form.T2a = (sum / this.page.T2value.length).toFixed(3);
                let Temperature_uniformity = [];
                Temperature_uniformity.push(Math.abs(this.form.T2a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T3a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T4a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T5a - this.form.T1a).toFixed(3));
                Temperature_uniformity.sort().reverse();
                this.form.Temperature_uniformity = Temperature_uniformity[0];

            },
            onT3Change() {
                let sum = 0;
                for (let i in this.page.T3value) {
                    sum = sum + Number(this.page.T3value[i]);
                }
                this.form.T3a = (sum / this.page.T3value.length).toFixed(3);
                let Temperature_uniformity = [];
                Temperature_uniformity.push(Math.abs(this.form.T2a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T3a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T4a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T5a - this.form.T1a).toFixed(3));
                Temperature_uniformity.sort().reverse();
                this.form.Temperature_uniformity = Temperature_uniformity[0];

            },
            onT4Change() {
                let sum = 0;
                for (let i in this.page.T4value) {
                    sum = sum + Number(this.page.T4value[i]);
                }
                this.form.T4a = (sum / this.page.T4value.length).toFixed(3);
                let Temperature_uniformity = [];
                Temperature_uniformity.push(Math.abs(this.form.T2a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T3a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T4a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T5a - this.form.T1a).toFixed(3));
                Temperature_uniformity.sort().reverse();
                this.form.Temperature_uniformity = Temperature_uniformity[0];

            },
            onT5Change() {
                let sum = 0;
                for (let i in this.page.T5value) {
                    sum = sum + Number(this.page.T5value[i]);
                }
                this.form.T5a = (sum / this.page.T5value.length).toFixed(3);
                let Temperature_uniformity = [];
                Temperature_uniformity.push(Math.abs(this.form.T2a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T3a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T4a - this.form.T1a).toFixed(3));
                Temperature_uniformity.push(Math.abs(this.form.T5a - this.form.T1a).toFixed(3));
                Temperature_uniformity.sort().reverse();
                this.form.Temperature_uniformity = Temperature_uniformity[0];

            },
            onChange(item, key) {
                let tolerance = Math.abs(item - this.page.energesisvalue[key]);
                this.page.tolerancevalue[key] = tolerance;
            },
            show_nameplate_Image(key) {
                ImagePreview({
                    images: this.page.nameplate,
                    startPosition: key,
                });
            },
            show_instrument_Image(key) {
                ImagePreview({
                    images: this.page.instrument_view,
                    startPosition: key,
                });
            },
            on_nameplate_Change(index) {
                this.page.positions = index;
            },
            on_instrument_Change(index) {
                this.page.positions = index;
            },
          beforeRead(file){
            if (file.length > this.page.max_count){
              Notify({ type: 'danger', message: `最多只能选择${this.page.max_count}个图片` });
              return  false
            }
            return true
          },
            nameplate_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                //上传图片
                let values = [];
                values.type = 'nameplate';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';
                        this.form.nameplate_fileList.pop();
                        this.form.nameplate_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                });
            },
            instrument_afterRead(file) {
                file.status = 'uploading';
                file.message = '上传中...';
                //上传图片
                let values = [];
                values.type = 'instrument_view';
                values.action = "upload_pic";
                values.base64Data = file.content;
                values.qsid = this.$route.query.qsid;
                submit(Qs.stringify(values)).then(res => {
                    if(res.status === 1){
                        file.status = 'success';
                        file.message = '上传成功';

                        this.form.instrument_fileList.pop();
                        this.form.instrument_fileList.push({
                            url: process.env.VUE_APP_BASE_PROXY_URL + res.pic_url,
                            id: res.file_id
                        });
                    }else{
                        file.status = 'failed';
                        file.message = '上传失败';
                    }
                })
            },
            oversize(){
                //超出限制大小
                Notify({ type: 'danger', message: '图片超出10M大小限制' });
                return false;
            },
            del_nameplate(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            del_instrument(file) {
                let values = [];
                values.action = "delpic";
                values.id = file.id;
                submit(Qs.stringify(values)).then(res => {
                    Notify({ type: 'primary', message: res.msg });
                });
                return new Promise((resolve) => {
                    resolve();
                });
            },
            changeFormType(type) {
                this.form.type = type;
                this.$refs.qualityForm.submit();
            },
            onSubmit(values) {
                if(values.lookslike == 2){
                    //外观不符合的要说明情况
                    if(!values.lookslike_desc.trim()){
                        Notify({ type: 'danger', message: '外观不符合的请说明情况' });
                        return false;
                    }
                }
                values.Tx = this.page.Txvalue;
                for(let i = 0;i < this.page.setting.Tx.slice(0,10).length;i++){
                    if(!values.Tx[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的Tx温度' });
                        return false;
                    }else if (!values.Tx[i]) {
                        values.Tx[i] = "";
                    }
                }

                values.T1 = this.page.T1value;
                for(let i = 0;i < this.page.setting.T1.slice(0,10).length;i++){
                    if(!values.T1[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的T1温度' });
                        return false;
                    }else if (!values.T1[i]) {
                        values.T1[i] = "";
                    }
                }

                values.T2 = this.page.T2value;
                for(let i = 0;i < this.page.setting.T2.slice(0,10).length;i++){
                    if(!values.T2[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的T2温度' });
                        return false;
                    }else if (!values.T2[i]) {
                        values.T2[i] = "";
                    }
                }

                values.T3 = this.page.T3value;
                for(let i = 0;i < this.page.setting.T3.slice(0,10).length;i++){
                    if(!values.T3[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的T3温度' });
                        return false;
                    }else if (!values.T3[i]) {
                        values.T3[i] = "";
                    }
                }

                values.T4 = this.page.T4value;
                for(let i = 0;i < this.page.setting.T4.slice(0,10).length;i++){
                    if(!values.T4[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的T4温度' });
                        return false;
                    }else if (!values.T4[i]) {
                        values.T4[i] = "";
                    }
                }

                values.T5 = this.page.T5value;
                for(let i = 0;i < this.page.setting.T5.slice(0,10).length;i++){
                    if(!values.T5[i]&&this.form.type!="keepquality"){
                        Notify({ type: 'danger', message: '请填写完成的T5温度' });
                        return false;
                    }else if (!values.T5[i]) {
                        values.T5[i] = "";
                    }
                }

                values.Txa = [];
                values.Txa.push(this.form.Txa);
                values.T1a = [];
                values.T1a.push(this.form.T1a);
                values.T2a = [];
                values.T2a.push(this.form.T2a);
                values.T3a = [];
                values.T3a.push(this.form.T3a);
                values.T4a = [];
                values.T4a.push(this.form.T4a);
                values.T5a = [];
                values.T5a.push(this.form.T5a);
                values.Normal_noise_detection_in_the_box = this.form.Normal_noise_detection_in_the_box;
                values.Alarm_noise_test_in_the_box = this.form.Alarm_noise_test_in_the_box;
                values.Alarm_sound_level_test = this.form.Alarm_sound_level_test;
                values.Humidity_detection = this.form.Humidity_detection;
                values.Temperature_deviation = [this.form.Temperature_deviation];
                values.Temperature_uniformity = [this.form.Temperature_uniformity];
                values.Volatility = [this.form.Volatility];
                values.Temperature_control_deviation = [this.form.Temperature_control_deviation];
                values.qsid = this.$route.query.qsid;
                values.action = this.form.type;
                let _this = this;
                submit(Qs.stringify(values)).then(res => {
                    if (res.status == 1) {
                        Toast({
                            type: 'success',//失败fail
                            duration: 2000,//2秒
                            message: res.msg,
                            icon: 'success',//失败cross
                            forbidClick: true,//是否禁止背景点击，避免重复提交
                            onClose() {
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Quality/qualityDetailList');
                            }
                        });
                    }
                })
            },
            OnTx() {
                this.page.Txshow = !this.page.Txshow;
                if (this.page.Txshow) {
                    this.page.Txicon = "arrow-down";
                } else {
                    this.page.Txicon = "arrow-up";
                }
            },
            OnT1() {
                this.page.T1show = !this.page.T1show;
                if (this.page.T1show) {
                    this.page.T1icon = "arrow-down";
                } else {
                    this.page.T1icon = "arrow-up";
                }
            },
            OnT2() {
                this.page.T2show = !this.page.T2show;
                if (this.page.T2show) {
                    this.page.T2icon = "arrow-down";
                } else {
                    this.page.T2icon = "arrow-up";
                }
            },
            OnT3() {
                this.page.T3show = !this.page.T3show;
                if (this.page.T3show) {
                    this.page.T3icon = "arrow-down";
                } else {
                    this.page.T3icon = "arrow-up";
                }
            },
            OnT4() {
                this.page.T4show = !this.page.T4show;
                if (this.page.T4show) {
                    this.page.T4icon = "arrow-down";
                } else {
                    this.page.T4icon = "arrow-up";
                }
            },
            OnT5() {
                this.page.T5show = !this.page.T5show;
                if (this.page.T5show) {
                    this.page.T5icon = "arrow-down";
                } else {
                    this.page.T5icon = "arrow-up";
                }
            },
            Ondetect() {
                this.page.dshow = !this.page.dshow;
                if (this.page.dshow) {
                    this.page.dicon = "arrow-down";
                } else {
                    this.page.dicon = "arrow-up";
                }
            },
            Onsound() {
                this.page.sshow = !this.page.sshow;
                if (this.page.sshow) {
                    this.page.sicon = "arrow-down";
                } else {
                    this.page.sicon = "arrow-up";
                }
            },
            getInfo(qsid) {
                let params = {qsid: qsid};
                getInfo(params).then(response => {
                    this.info.asInfo = response.asInfo;
                    this.info.qsInfo = response.qsInfo;
                    this.page.templatename = response.templatename;
                    for (let i in response.setting) {
                        if (response.setting[i].detection_Ename == 'Tx') {
                            this.page.setting.Tx = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.Txvalue[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'T1') {
                            this.page.setting.T1 = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.T1value[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'T2') {
                            this.page.setting.T2 = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.T2value[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'T3') {
                            this.page.setting.T3 = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.T3value[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'T4') {
                            this.page.setting.T4 = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.T4value[j] = "";
                            }
                        }
                        if (response.setting[i].detection_Ename == 'T5') {
                            this.page.setting.T5 = response.setting[i].set;
                            for (let j in response.setting[i].set) {
                                this.page.T5value[j] = "";
                            }
                        }
                    }
                    if (response.detail_data != null && response.detail_data.fixed_detection) {
                        this.page.Txvalue = response.detail_data.fixed_detection.Tx;
                        this.page.T1value = response.detail_data.fixed_detection.T1;
                        this.page.T2value = response.detail_data.fixed_detection.T2;
                        this.page.T3value = response.detail_data.fixed_detection.T3;
                        this.page.T4value = response.detail_data.fixed_detection.T4;
                        this.page.T5value = response.detail_data.fixed_detection.T5;
                        this.form.Txa = Number(response.detail_data.fixed_detection.Txa[0]);
                        this.form.T1a = Number(response.detail_data.fixed_detection.T1a[0]);
                        this.form.T2a = Number(response.detail_data.fixed_detection.T2a[0]);
                        this.form.T3a = Number(response.detail_data.fixed_detection.T3a[0]);
                        this.form.T4a = Number(response.detail_data.fixed_detection.T4a[0]);
                        this.form.T5a = Number(response.detail_data.fixed_detection.T5a[0]);
                        this.form.Temperature_deviation = Number(response.detail_data.fixed_detection.Temperature_deviation[0]);
                        this.form.Temperature_uniformity = Number(response.detail_data.fixed_detection.Temperature_uniformity[0]);
                        this.form.Volatility = Number(response.detail_data.fixed_detection.Volatility[0]);
                        this.form.Temperature_control_deviation = Number(response.detail_data.fixed_detection.Temperature_control_deviation[0]);
                        this.form.Humidity_detection = response.detail_data.fixed_detection.Humidity_detection;
                        this.form.Alarm_sound_level_test = response.detail_data.fixed_detection.Alarm_sound_level_test;
                        this.form.Alarm_noise_test_in_the_box = response.detail_data.fixed_detection.Alarm_noise_test_in_the_box;
                        this.form.Normal_noise_detection_in_the_box = response.detail_data.fixed_detection.Normal_noise_detection_in_the_box;
                        this.form.Over_temperature_alarm = response.detail_data.fixed_detection.Over_temperature_alarm;
                        this.form.Power_failure_alarm = response.detail_data.fixed_detection.Power_failure_alarm;
                        this.form.lookslike = response.detail_data.fixed_detection.lookslike;
                        if(response.detail_data.fixed_detection.lookslike == 2){
                            this.show_lookslike_desc = true;
                            this.form.lookslike_desc = response.detail_data.fixed_detection.lookslike_desc;
                        }
                        this.form.total_desc = response.detail_data.fixed_detection.total_desc;
                    }
                    if (response.file_data) {
                        for (let i in response.file_data.nameplate) {
                            this.form.nameplate_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.nameplate[i].file_url,
                                id: response.file_data.nameplate[i].file_id
                            });
                        }
                        for (let i in response.file_data.instrument_view) {
                            this.form.instrument_fileList.push({
                                url: process.env.VUE_APP_BASE_PROXY_URL + response.file_data.instrument_view[i].file_url,
                                id: response.file_data.instrument_view[i].file_id
                            });
                        }

                    }
                    this.page.is_display = 1;
                });
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.qsid);
        }
    }
</script>
<style scoped lang="scss">
    .background {
        background: rgba(69, 90, 100, 0.1);
    }
    .card-header {
        margin-top: 20px;
    }

    .mp {
        margin-top: 20px;
        padding-top: 10px;
        background: #fff;
    }
    .jc{
        .van-row{
            width: 50%;
            float: left;
        }
    }
    .wc{
        .van-cell__label{
            width: 180%;
        }
        .van-field__label{
            width: 130px;
        }
    }
</style>
<style>
    .jc .van-field__label{
        width: 80px;
    }
    .wc .van-field__label{
        width: 130px;
    }
</style>
