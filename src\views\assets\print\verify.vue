<template>
    <div v-if="page.is_display===1">
        <van-divider>设备相关信息</van-divider>
        <van-tabs :border="false">
            <van-tab title="设备基本信息">
                <AssetsInfo :info='info'/>
            </van-tab>
        </van-tabs>
        <div style="color:red">{{page.tips}}</div>
        <van-uploader v-model="page.fileList" max-size="10485760" multiple :after-read="afterRead" :before-delete="beforedelete"/>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Divider, Cell, CellGroup, Icon, Field, Form, Button, Tab, Tabs, Toast, Loading, Uploader} from 'vant';
    import {getInfo} from "@/api/assets/print/verify";
    import AssetsInfo from "@/components/Assetsinfo";
    import {upload} from "@/api/assets/lookup/show";

    export default {
        name: 'applyScrap',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Uploader.name]: Uploader,
            [Tabs.name]: Tabs,
            [Loading.name]: Loading,
            AssetsInfo,
        },
        data() {
            return {
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    scrapInfo: [],//是否有历史报废信息
                    approves: [],
                    scriptdata: {
                        scrapnum: '',
                    },
                },
                page: {
                    tips: '',
                    fileList: [],
                    is_display: 0,
                },
                form: {
                    type: '',
                },
            }
        },
        methods: {
            afterRead(file, detail) {
                // 此时可以自行将文件上传至服务器
                let values = [];
                values.action = 'upload';
                values.t = 'assets_info';
                values.assid = this.info.assid;
                values.base64 = file.content;
                values.type = this.form.type;
                console.log(detail);
                upload(Qs.stringify(values)).then(response => {
                    Toast.success(response.msg);
                    this.page.fileList[detail.index].url = response.url;
                });
            },
            beforedelete(file, detail) {
                let values = [];
                values.action = 'deleteAssetsPic';
                values.assid = this.info.assid;
                values.src = this.page.fileList[detail.index].url;
                upload(Qs.stringify(values)).then(response => {
                    Toast.success(response.msg);
                });
                return new Promise((resolve) => {
                    resolve();
                })
            },
            getInfo(assid, assnum, from) {
                let params = {assid: assid, assnum: assnum, from: from};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.page.tips = response.tips;
                    this.form.type = response.type;
                    this.page.is_display = 1;
                });
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.assid, this.$route.query.assnum, this.$route.query.from);
        }
    }
</script>

<style scoped lang="scss">
    .van-button {
        margin-top: 10px;
    }
</style>
