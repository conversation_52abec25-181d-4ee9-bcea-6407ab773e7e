<template>
    <div class="con_detail" v-if="page.is_display===1">
        <Quality :info="info"/>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">质控检测明细（模板：{{page.templatename}}）</h2>
        </div>

        <van-form>
            <van-field label="外观功能" :value="info.detailInfo.exterior=='1'?'符合':'不符合'" readonly/>
            <van-cell v-show="info.detailInfo.exterior==2" :title="'不符合情况说明：'+info.detailInfo.exterior_explain"/>

            <van-cell-group>
                <van-cell :title="page.humidity" class="background"/>
                <van-row>
                    <van-col span="4">设定值</van-col>
                    <van-col span="5">实测值</van-col>
                    <van-col span="5">实测误差</van-col>
                    <van-col span="5">示值</van-col>
                    <van-col span="5">示值误差</van-col>
                </van-row>
                <van-row v-for="(item,key) in page.setting.humidity.slice(0,3)" :key="key">
                    <van-col span="4">{{item}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.humidity[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.humidity_tolerance[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.humidity_value[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.humidity_value_tolerance[key]}}</van-col>
                </van-row>
                <van-field label="最大输出误差" :value="info.detailInfo.preset_detection.humidity_max_output" readonly/>
                <van-field label="最大示值误差" :value="info.detailInfo.preset_detection.humidity_max_value" readonly/>
            </van-cell-group>

            <van-cell-group>
                <van-cell :title="page.aeration" class="background"/>
                <van-row>
                    <van-col span="4">设定值</van-col>
                    <van-col span="5">实测值</van-col>
                    <van-col span="5">实测误差</van-col>
                    <van-col span="5">示值</van-col>
                    <van-col span="5">示值误差</van-col>
                </van-row>
                <van-row v-for="(item,key) in page.setting.aeration.slice(0,3)" :key="key">
                    <van-col span="4">{{item}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.aeration[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.aeration_tolerance[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.aeration_value[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.aeration_value_tolerance[key]}}</van-col>
                </van-row>
                <van-field label="最大输出误差" :value="info.detailInfo.preset_detection.aeration_max_output" readonly/>
                <van-field label="最大示值误差" :value="info.detailInfo.preset_detection.aeration_max_value" readonly/>
            </van-cell-group>

            <van-cell-group>
                <van-cell :title="page.IOI" class="background"/>
                <van-row>
                    <van-col span="4">设定值</van-col>
                    <van-col span="5">实测值</van-col>
                    <van-col span="5">实测误差</van-col>
                    <van-col span="5">示值</van-col>
                    <van-col span="5">示值误差</van-col>
                </van-row>
                <van-row v-for="(item,key) in page.setting.IOI.slice(0,3)" :key="key">
                    <van-col span="4">{{item}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.IOI[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.IOI_tolerance[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.IOI_value[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.IOI_value_tolerance[key]}}</van-col>
                </van-row>
                <van-field label="最大输出误差" :value="info.detailInfo.preset_detection.IOI_max_output" readonly/>
                <van-field label="最大示值误差" :value="info.detailInfo.preset_detection.IOI_max_value" readonly/>
            </van-cell-group>

            <van-cell-group>
                <van-cell :title="page.IPAP" class="background"/>
                <van-row>
                    <van-col span="4">设定值</van-col>
                    <van-col span="5">实测值</van-col>
                    <van-col span="5">实测误差</van-col>
                    <van-col span="5">示值</van-col>
                    <van-col span="5">示值误差</van-col>
                </van-row>
                <van-row v-for="(item,key) in page.setting.IPAP.slice(0,3)" :key="key">
                    <van-col span="4">{{item}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.IPAP[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.IPAP_tolerance[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.IPAP_value[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.IPAP_value_tolerance[key]}}</van-col>
                </van-row>
                <van-field label="最大输出误差" :value="info.detailInfo.preset_detection.IPAP_max_output" readonly/>
                <van-field label="最大示值误差" :value="info.detailInfo.preset_detection.IPAP_max_value" readonly/>
            </van-cell-group>

            <van-cell-group>
                <van-cell :title="page.PEEP" class="background"/>
                <van-row>
                    <van-col span="4">设定值</van-col>
                    <van-col span="5">实测值</van-col>
                    <van-col span="5">实测误差</van-col>
                    <van-col span="5">示值</van-col>
                    <van-col span="5">示值误差</van-col>
                </van-row>
                <van-row v-for="(item,key) in page.setting.PEEP.slice(0,3)" :key="key">
                    <van-col span="4">{{item}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.PEEP[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.PEEP_tolerance[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.PEEP_value[key]}}</van-col>
                    <van-col span="5">{{info.detailInfo.preset_detection.PEEP_value_tolerance[key]}}</van-col>
                </van-row>
                <van-field label="最大输出误差" :value="info.detailInfo.preset_detection.PEEP_max_output" readonly/>
                <van-field label="最大示值误差" :value="info.detailInfo.preset_detection.PEEP_max_value" readonly/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="机械通气模式评价" class="background"/>

                <van-cell title="容量预制模式："
                          :value="page.detail_result.capacity_precut_mode==1?'符合':page.detail_result.capacity_precut_mode==2?'不符合':page.detail_result.capacity_precut_mode==3?'不适用':'未知'"/>
                <van-cell title="流量触发功能："
                          :value="page.detail_result.traffic_trigger==1?'符合':page.detail_result.traffic_trigger==2?'不符合':page.detail_result.traffic_trigger==3?'不适用':'未知'"/>
                <van-cell title="压力预制模式："
                          :value="page.detail_result.pressure_precut_mode==1?'符合':page.detail_result.pressure_precut_mode==2?'不符合':page.detail_result.pressure_precut_mode==3?'不适用':'未知'"/>
                <van-cell title="压力触发功能："
                          :value="page.detail_result.pressure_tigger==1?'符合':page.detail_result.pressure_tigger==2?'不符合':page.detail_result.pressure_tigger==3?'不适用':'未知'"/>
            </van-cell-group>

            <van-cell-group>
                <van-cell title="安全报警功能等检查" class="background"/>

                <van-cell title="电源报警："
                          :value="page.detail_result.power_supply_alarm==1?'符合':page.detail_result.power_supply_alarm==2?'不符合':page.detail_result.power_supply_alarm==3?'不适用':'未知'"/>
                <van-cell title="氧浓度上/下限报警："
                          :value="page.detail_result.oxygen_concentration_bound==1?'符合':page.detail_result.oxygen_concentration_bound==2?'不符合':page.detail_result.oxygen_concentration_bound==3?'不适用':'未知'"/>
                <van-cell title="气源报警："
                          :value="page.detail_result.gas_supply_alarm==1?'符合':page.detail_result.gas_supply_alarm==2?'不符合':page.detail_result.gas_supply_alarm==3?'不适用':'未知'"/>
                <van-cell title="窒息报警："
                          :value="page.detail_result.apnea_alarm==1?'符合':page.detail_result.apnea_alarm==2?'不符合':page.detail_result.apnea_alarm==3?'不适用':'未知'"/>
                <van-cell title="气道压力上/下限报警："
                          :value="page.detail_result.AWP_alarm==1?'符合':page.detail_result.AWP_alarm==2?'不符合':page.detail_result.AWP_alarm==3?'不适用':'未知'"/>
                <van-cell title="病人回路过压保护功能："
                          :value="page.detail_result.loop_overvoltage_pretection==1?'符合':page.detail_result.loop_overvoltage_pretection==2?'不符合':page.detail_result.loop_overvoltage_pretection==3?'不适用':'未知'"/>
                <van-cell title="分钟通气量上/下限报警："
                          :value="page.detail_result.minute_ventilation_bound==1?'符合':page.detail_result.minute_ventilation_bound==2?'不符合':page.detail_result.minute_ventilation_bound==3?'不适用':'未知'"/>
                <van-cell title="按键功能检查（含键盘锁）："
                          :value="page.detail_result.press_key==1?'符合':page.detail_result.press_key==2?'不符合':page.detail_result.press_key==3?'不适用':'未知'"/>
            </van-cell-group>


            <van-field label="检测结果" :value="info.detailInfo.result=='1'?'合格':'不合格'" readonly/>
            <van-cell v-if="info.detailInfo.remark" :title="'检测备注：'+info.detailInfo.remark"/>
            <van-cell v-else title="检测备注：无"/>
            <div class="mp">
                <van-divider v-show="page.nameplate.length>0">设备铭牌照片</van-divider>
                <van-swipe class="image-swipe" :autoplay="3000" indicator-color="white" height="220"
                           v-show="page.nameplate.length>0" @change="onChange">
                    <van-swipe-item v-for="(item,index) in page.nameplate" :key="index" @click="showImage">
                        <van-image width="100%" height="220" :src="item" fit="fill"/>
                    </van-swipe-item>
                </van-swipe>

                <van-divider v-show="page.instrument_view.length>0">检测仪器视图照片</van-divider>
                <van-swipe class="image-swipe" :autoplay="3000" indicator-color="white" height="220"
                           v-show="page.instrument_view.length>0" @change="onChange">
                    <van-swipe-item v-for="(item,index) in page.instrument_view" :key="index" @click="showImg">
                        <van-image width="100%" height="220" :src="item" fit="fill"/>
                    </van-swipe-item>
                </van-swipe>
                <van-divider v-show="page.file_data">质控报告</van-divider>
                <van-image v-show="page.file_data" width="100%" height="100%" :src="page.file_data"/>
                <van-row type="flex" justify="center">
                    <van-uploader max-size="10485760" multiple :after-read="afterRead">
                        <van-button type="primary" native-type="button">上传质控报告</van-button>
                    </van-uploader>
                </van-row>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>
<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Row,
        Field,
        Form,
        Button,
        Tab,
        Tabs,
        Toast,
        Loading,
        Uploader,
        ImagePreview,
        Image as VanImage,
        Swipe,
        SwipeItem,
        Col
    } from 'vant';
    import {getInfo, submit} from "@/api/quality/show";
    import Quality from "@/components/Quality";

    export default {
        name: 'GaoPingDianDao',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Row.name]: Row,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Uploader.name]: Uploader,
            [Tab.name]: Tab,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Tabs.name]: Tabs,
            [Col.name]: Col,
            [VanImage.name]: VanImage,
            [Loading.name]: Loading,
            Quality,
        },
        data() {
            return {
                info: {
                    asInfo: [],
                    qsInfo: [],
                    detailInfo: [],
                },
                page: {
                    humidity: '潮气量（vcv模式）最大允差：',
                    aeration: '强制通气频率与呼吸比（vcv模式）最大允差：',
                    IOI: '吸入氧浓度FiO₂最大允差：',
                    IPAP: '吸气压力水平PCV或新生儿呼吸的PLV模式f=20最大允差：',
                    PEEP: '呼气末正压PEEPVCV模式Vt=400mlf=20最大允差：',
                    file_data: '',
                    templatename: '',
                    setting: {
                        humidity: [],
                    },
                    nameplate: [],
                    instrument_view: [],
                    detail_result: [],
                    positions: 0,
                    is_display: 0,
                },
                form: {},
            }
        },
        methods: {
            onChange(index) {
                this.page.positions = index;
            },
            showImage() {
                ImagePreview({
                    images: this.page.nameplate,
                    startPosition: this.page.positions,
                });
            },
            showImg() {
                ImagePreview({
                    images: this.page.instrument_view,
                    startPosition: this.page.positions,
                });
            },
            afterRead(file) {
                let values = [];
                values.qsid = this.$route.query.qsid;
                values.base64 = file.content;
                values.action = 'upload';
                values.fileName = file.file.name;
                submit(Qs.stringify(values)).then(response => {
                    this.page.file_data = process.env.VUE_APP_BASE_PROXY_URL + response.path;
                    Toast.success(response.msg);
                    /*this.$router.push('/');*/
                })

            },
            getInfo(qsid) {
                let params = {qsid: qsid};
                getInfo(params).then(response => {
                    this.info.asInfo = response.asInfo;
                    this.info.qsInfo = response.qsInfo;
                    this.info.detailInfo = response.detailInfo;
                    this.page.setting = response.setting;
                    if (response.detailInfo.report) {
                        this.page.file_data = process.env.VUE_APP_BASE_PROXY_URL + response.detailInfo.report;
                    }
                    this.page.detail_result = response.detail_result;
                    for (let i in response.file_data.nameplate) {
                        this.page.nameplate.push(process.env.VUE_APP_BASE_PROXY_URL + response.file_data.nameplate[i].file_url);
                    }
                    for (let i in response.file_data.instrument_view) {
                        this.page.instrument_view.push(process.env.VUE_APP_BASE_PROXY_URL + response.file_data.instrument_view[i].file_url);
                    }
                    this.page.templatename = response.templatename;
                    this.page.humidity = this.page.humidity + response.tolerance.humidity;
                    this.page.aeration = this.page.aeration + response.tolerance.aeration;
                    this.page.IOI = this.page.IOI + response.tolerance.IOI;
                    this.page.IPAP = this.page.IPAP + response.tolerance.IPAP;
                    this.page.PEEP = this.page.PEEP + response.tolerance.PEEP;
                    this.page.is_display = 1;
                })
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.qsid);
        }
    }
</script>
<style scoped lang="scss">
    ::v-deep .van-form .van-field .van-field__control {
        color: red;
    }

    ::v-deep .van-cell-group .van-field .van-field__control {
        color: #323233;
    }

    ::v-deep .van-field__label {
        width: 86%;
    }

    .background {
        background: rgba(69, 90, 100, 0.1);
    }

    .van-cell-group .van-row .van-col {
        text-align: center;
        border: 1px solid #969799
    }

    .card-header {
        margin-top: 20px;
    }

    .mp {
        margin-top: 20px;
        padding-top: 10px;
        background: #fff;
    }

    .van-row {
        margin: 20px 0;
    }
</style>
