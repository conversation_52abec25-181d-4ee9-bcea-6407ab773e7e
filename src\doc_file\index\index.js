/**
 * @api {Post} /Notin/getMenus 获取首页菜单
 * @apiDescription 组织数据一组8个菜单
 * @apiVersion 0.1.0
 * @apiName getMenus
 * @apiGroup 首页
 *
 * @apiSuccessExample  {json} 响应结果实例
 {
     "status": 1,
     "menu": [
          {
               "title": "设备管理",
               "swipe": [
                    {
                         "menu": [
                              {
                                   "actionname": "设备查询",
                                   "actionurl": "//Lookup/getAssetsList.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/sbcx.png"
                              },
                              {
                                   "actionname": "借调申请",
                                   "actionurl": "//Borrow/borrowAssetsList.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/jdsq.png"
                              },
                              {
                                   "actionname": "借入验收",
                                   "actionurl": "//Borrow/borrowInCheckList.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/qrjr.png"
                              },
                              {
                                   "actionname": "归还验收",
                                   "actionurl": "//Borrow/giveBackCheckList.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/ghys.png"
                              },
                              {
                                   "actionname": "归还验收",
                                   "actionurl": "//Borrow/giveBackCheckList.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/jdyq.png"
                              },
                              {
                                   "actionname": "转科申请",
                                   "actionurl": "//Transfer/getList.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/zksq.png"
                              },
                              {
                                   "actionname": "转科进程",
                                   "actionurl": "//Transfer/progress.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/zkjc.png"
                              },
                              {
                                   "actionname": "转科验收",
                                   "actionurl": "//Transfer/checkLists.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/zkys.png"
                              }
                         ]
                    },
                    {
                         "menu": [
                              {
                                   "actionname": "报废申请",
                                   "actionurl": "//Scrap/getApplyList.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/bfsq.png"
                              },
                              {
                                   "actionname": "报废查询",
                                   "actionurl": "//Scrap/getScrapList.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/bfcx.png"
                              },
                              {
                                   "actionname": "档案盒管理",
                                   "actionurl": "//Box/boxList.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/smda.png"
                              },
                              {
                                   "actionname": "标签核实",
                                   "actionurl": "//Print/verify.html",
                                   "icon": "http://www.tecev-git.com//Public/mobile/images/icon/byys.png"
                              }
                         ]
                    }
               ]
          }
     ]
}
 */
