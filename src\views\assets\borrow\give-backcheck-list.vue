<template>
    <div class="list">
                <van-search
                        v-model="keyword"
                        shape="round"
                        placeholder="搜索(设备名称、编号、申请科室、单号)"
                        @search="onSearch"
                />
        <van-row>
            <van-col span="18">
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        待归还确认验收设备 <span class="num"> {{total}} </span> 台
                    </div>
                </div>
            </van-col>
            <van-col span="6">
                <Sort @order="order"  :option="option"/>
            </van-col>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="refreshList"
        >
            <van-card :thumb="item.pic_url" v-for="item in list" :key="item.assid">
                <template #desc>
                    <div class="jumpButton">
                        <van-button type="info" size="small" :to="{ path: $store.getters.moduleName+'/Borrow/giveBackCheck', query: { borid: item.borid }}">归还验收</van-button>
                    </div>
                    <ul>
                        <li class="list_li_width">
                            <span class="detailText">设备名称：</span>
                            <span class="text">{{item.assets}}</span>
                        </li>
                        <li>
                            <span class="detailText">借调单号：</span>
                            <span class="text">{{item.borrow_num}}</span>
                        </li>
                        <li class="list_li_width">
                            <span class="detailText">申请科室：</span>
                            <span class="text">{{item.apply_department}}</span>
                        </li>
                        <li>
                            <span class="detailText">申请人：</span>
                            <span class="text">{{item.apply_user}}</span>
                        </li>
                        <li>
                            <span class="detailText">预计归还：</span>
                            <span class="text">{{item.estimate_back}}</span>
                        </li>
                    </ul>
                </template>
            </van-card>
        </van-list>
        <ScrollTop/>
    </div>
</template>

<script>
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem} from "vant";
    import {getGiveBackCheckList} from "@/api/assets/borrow/list";
    import ScrollTop from "@/components/ScrollTop";
    import Sort from "@/components/Sort";

    export default {
        name: 'giveBackCheckList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            ScrollTop,
            Sort
        },
        data() {
            return {
                list: [],
                option: [
                    {text: '按预计归还时间（降序）', value: 'estimate_back-desc'},
                    {text: '按申请科室（降序）', value: 'apply_departid-desc'},
                    {text: '按预计归还时间（升序）', value: 'estimate_back-asc'},
                    {text: '按申请科室（升序）', value: 'apply_departid-asc'},
                ],
                keyword: '',
                orderValue: '',
                loading: false,
                finished: false,
                total: 0,
                //全局列表搜索条件
                where: {
                    page: 1
                }
            };
        },
        methods: {
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                setTimeout(() => {
                    getGiveBackCheckList(this.where).then(response => {
                        //先判断如果返回的总数为0 则把状态设置为停止加载
                        if (response.total === 0) {
                            this.finished = true;
                        }
                        //判断数据键值是否存在
                        if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                            this.list.push(...response.rows);
                        }
                        //仅第一页赋值总数
                        if (this.where.page === 1) {
                            this.total = response.total;
                        }
                        //数据获取到了 停止加载
                        this.loading = false;
                        //全部加载了 且当前列表数量大于总数 设置完成状态
                        if (this.list.length >= this.total) {
                            this.finished = true;
                        }
                        //页数加1
                        this.where.page++;
                    })
                }, 100);
            },
            onSearch(keyword) {
                this.resetWhere();
                this.finished = false;
                //重新加搜索条件
                this.where.search = keyword;
                this.refreshList();
            },
            order(value) {
                this.resetWhere(false);
                //重新加搜索条件
                this.where.page = 1;
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.refreshList();
            },
            // getcatid(id) {
            //     this.resetWhere();
            //     //重新加搜索条件
            //     this.where.catid = id;
            //     this.refreshList();
            // },
            // getdepartid(id) {
            //     this.resetWhere();
            //     //重新加搜索条件
            //     this.where.departid = id;
            //     this.refreshList();
            // },
            //重置列表条件
            resetWhere(clearWhere = true) {
                this.list = [];//清空列表
                if (clearWhere) {
                    //清空搜索条件
                    this.where = {};
                }
                //重置为第一页
                this.where.page = 1;
                //重置表格为加载状态
                this.loading = true;
                this.finished = false;
            }
        }
    }
</script>

<style scoped lang="scss">
    .list {
        .total-div {
            margin-top: 0;
        }

        ::v-deep .filter {
            margin-top: 0;
        }
        .van-search{
            margin-top: 10px;
        }
    }
</style>
