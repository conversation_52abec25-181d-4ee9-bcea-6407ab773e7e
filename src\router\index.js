import Vue from 'vue'
import VueRouter from 'vue-router'
// import VueRouter from 'uni-simple-router'
Vue.use(VueRouter);
let MOBILE_NAME = process.env.VUE_APP_BASE_AXIOS_URL;
const routes = [
    //首页
    {
        path: '/',
        name:'index',
        component: () => import('@/views/index/index'),
        meta: {
            needLogin: true //需要加校检判断的路由
        },
    },
    //公共页面
    {
        path: '/fail/:tips',//tips为json格式的字符串，如：{"tips":"账户已过期","btn":"返回登录页面","url":"login"}
        name: 'fail',
        component: () => import('@/views/pub/notin/fail'),
        meta: {
            title: '出错啦'
        },
    },
    {
        path: '/suc',
        name: 'suc',
        component: () => import('@/views/pub/notin/suc'),
        meta: {
            title: '操作成功'
        },
    },
    {
        path: '/chan_user',
        name: 'chan_user',
        component: () => import('@/views/pub/notin/chan_user'),
        meta: {
            title: '选择登录用户'
        },
    },
    {
        path: '/dw',
        name: 'dw',
        component: () => import('@/views/pub/notin/dw'),
        meta: {
            title: '请在浏览器中打开'
        },
    },
    {
        path: '/showFile',
        name: 'showFile',
        component: () => import('@/views/pub/notin/showFile'),
        meta: {
            title: '预览文件'
        },
    },
    {
        path: '/todo',
        name: 'todo',
        component: () => import('@/views/pub/notin/todo'),
        meta: {
            title: '选择性操作'
        },
    },
    {
        path: '/search',
        name: 'search',
        component: () => import('@/views/pub/notin/search'),
        meta: {
            title: '系统搜索'
        },
    },
    //设备管理
    //设备列表
    {
        path: MOBILE_NAME+'/Lookup/getAssetsList',
        component: () => import('@/views/assets/lookup/assets-list'),
        meta: {
            title: '设备列表'
        },
    },
    //设备详情
    {
        path: MOBILE_NAME+'/Lookup/showAssets',
        name:'showAssets',
        component: () => import('@/views/assets/lookup/show-assets'),
        meta: {
            title: '设备详情'
        },
    },
    //开机管理
    {
        path: MOBILE_NAME+'/Lookup/startup',
        name:'startup',
        component: () => import('@/views/assets/lookup/startup'),
        meta: {
            title: '开机管理'
        },
    },
    //借调管理
    //申请借调
    {
        path: MOBILE_NAME+'/Borrow/applyBorrow',
        name: 'applyBorrow',
        component: () => import('@/views/assets/borrow/borrow-apply'),
        meta: {
            title: '借调申请'
        },
    },
    //借调审批
    {
        path: MOBILE_NAME+'/Borrow/approveBorrow',
        component: () => import('@/views/assets/borrow/borrow-approve'),
        meta: {
            title: '借调审批'
        },
    },
    //借调申请设备列表
    {
        path: MOBILE_NAME+'/Borrow/borrowAssetsList',
        component: () => import('@/views/assets/borrow/borrow-list'),
        meta: {
            title: '借调申请设备列表'
        },
    },
    //借调进程详情
    {
        path: MOBILE_NAME+'/Borrow/progress_detail',
        name: 'borrow_progress_detail',
        component: () => import('@/views/assets/borrow/progress-detail'),
        meta: {
            title: '借调进程详情'
        },
    },
    //借入验收
    {
        path: MOBILE_NAME+'/Borrow/borrowInCheck',
        name: 'borrowInCheck',
        component: () => import('@/views/assets/borrow/borrow-incheck'),
        meta: {
            title: '借入验收'
        },
    },
    //借入检查确定列表
    {
        path: MOBILE_NAME+'/Borrow/borrowInCheckList',
        component: () => import('@/views/assets/borrow/borrow-incheck-list'),
        meta: {
            title: '借入检查确定列表'
        },
    },
    //归还验收
    {
        path: MOBILE_NAME+'/Borrow/giveBackCheck',
        component: () => import('@/views/assets/borrow/give-backcheck'),
        meta: {
            title: '归还验收'
        },
    },
    //归还确认验收
    {
        path: MOBILE_NAME+'/Borrow/giveBackCheckList',
        component: () => import('@/views/assets/borrow/give-backcheck-list'),
        meta: {
            title: '归还确认验收'
        },
    },
    //借调逾期催还
    {
        path: MOBILE_NAME+'/Borrow/giveBackCheckList/showReminderList',
        component: () => import('@/views/assets/borrow/reminder-list'),
        meta: {
            title: '借调逾期催还'
        },
    },
    //确认归还时间
    {
        path: MOBILE_NAME+'/Borrow/showReminder',
        component: () => import('@/views/assets/borrow/show-reminder'),
        meta: {
            title: '确认归还时间'
        },
    },
    //转科管理
    //设备转科
    {
        path: MOBILE_NAME+'/Transfer/add',
        name: 'transfer_add',
        component: () => import('@/views/assets/transfer/add'),
        meta: {
            title: '转科申请'
        },
    },
    //转科审批
    {
        path: MOBILE_NAME+'/Transfer/approval',
        component: () => import('@/views/assets/transfer/transfer-approve'),
        meta: {
            title: '转科审批'
        },
    },
    //转科验收
    {
        path: MOBILE_NAME+'/Transfer/check',
        component: () => import('@/views/assets/transfer/check'),
        meta: {
            title: '转科验收'
        },
    },
    //转科验收列表
    {
        path: MOBILE_NAME+'/Transfer/checkLists',
        component: () => import('@/views/assets/transfer/transfer-check-list'),
        meta: {
            title: '转科验收列表'
        },
    },
    //转科申请设备列表
    {
        path: MOBILE_NAME+'/Transfer/getList',
        component: () => import('@/views/assets/transfer/get-list'),
        meta: {
            title: '转科申请设备列表'
        },
    },
    //转科进程
    {
        path: MOBILE_NAME+'/Transfer/progress',
        name: 'transfer_progress',
        component: () => import('@/views/assets/transfer/progress'),
        meta: {
            title: '转科进程'
        },
    },
    //转科进程详情
    {
        path: MOBILE_NAME+'/Transfer/progress_detail',
        name: 'transfer_progress_detail',
        component: () => import('@/views/assets/transfer/progress-detail'),
        meta: {
            title: '转科进程详情'
        },
    },
    //报废管理
    //设备报废申请
    {
        path: MOBILE_NAME+'/Scrap/applyScrap',
        name:'applyScrap',
        component: () => import('@/views/assets/scrap/apply-scrap'),
        meta: {
            title: '报废申请'
        },
    },
    //报废审批
    {
        path: MOBILE_NAME+'/Scrap/examine',
        component: () => import('@/views/assets/scrap/approve'),
        meta: {
            title: '报废审批'
        },
    },
    //报废申请设备列表
    {
        path: MOBILE_NAME+'/Scrap/getApplyList',
        component: () => import('@/views/assets/scrap/get-apply-list'),
        meta: {
            title: '报废申请设备列表'
        },
    },
    //报废结果列表
    {
        path: MOBILE_NAME+'/Scrap/getResultList',
        component: () => import('@/views/assets/scrap/result-list'),
        meta: {
            title: '报废结果列表'
        },
    },
    //报废查询列表
    {
        path: MOBILE_NAME+'/Scrap/getScrapList',
        component: () => import('@/views/assets/scrap/get-scrap-list'),
        meta: {
            title: '报废查询列表'
        },
    },
    //报废处置
    {
        path: MOBILE_NAME+'/Scrap/result',
        component: () => import('@/views/assets/scrap/result'),
        meta: {
            title: '报废处置'
        },
    },
    //设备报废明细
    {
        path: MOBILE_NAME+'/Scrap/getScrapList/showScrap',
        name: 'showScrap',
        component: () => import('@/views/assets/scrap/show-scrap'),
        meta: {
            title: '设备报废明细'
        },
    },
    //设备打印
    //扫码核实列表
    {
        path: MOBILE_NAME+'/Print/verify',
        component: () => import('@/views/assets/print/list'),
        meta: {
            title: '扫码核实列表'
        },
    },
    //扫码核实
    {
        path: MOBILE_NAME+'/Print/labelCheck',
        component: () => import('@/views/assets/print/verify'),
        meta: {
            title: '扫码核实'
        },
    },
    //档案管理
    //档案盒信息
    {
        path: MOBILE_NAME+'/Box/showBox',
        name:'showBox',
        component: () => import('@/views/archives/box/show-box'),
        meta: {
            title: '档案盒信息'
        },
    },
    //档案盒文件显示
    {
        path: MOBILE_NAME+'/Box/showFile',
        component: () => import('@/views/archives/box/show-file'),
        meta: {
            title: '档案盒文件显示'
        },
    },
    //应急预案管理
    //应急预案列表
    {
        path: MOBILE_NAME+'/Emergency/emergencyPlanList',
        component: () => import('@/views/archives/emergency/list'),
        meta: {
            title: '应急预案列表'
        },
    },
    //应急预案详情
    {
        path: MOBILE_NAME+'/Emergency/showEmergencyPlan',
        component: () => import('@/views/archives/emergency/show-emergency'),
        meta: {
            title: '应急预案详情'
        },
    },
    //公告管理
    //公告列表
    {
        path: MOBILE_NAME+'/Notice/getNoticeList',
        component: () => import('@/views/basesetting/notice/list'),
        meta: {
            title: '公告列表'
        },
    },
    //公告详情
    {
        path: MOBILE_NAME+'/Notice/showNotice',
        component: () => import('@/views/basesetting/notice/show-notice'),
        meta: {
            title: '公告详情'
        },
    },
    //维修管理
    //维修接单
    {
        path: MOBILE_NAME+'/Repair/accept',
        component: () => import('@/views/repair/repair/accept'),
        meta: {
            title: '维修接单'
        },
    },
    //维修审批
    {
        path: MOBILE_NAME+'/Repair/addApprove',
        component: () => import('@/views/repair/repair/approve'),
        meta: {
            title: '维修审批'
        },
    },
    //设备报修
    {
        path: MOBILE_NAME+'/Repair/addRepair',
        name: 'addRepair',
        component: () => import('@/views/repair/repair/add'),
        meta: {
            title: '设备报修'
        },
    },
    //维修验收
    {
        path: MOBILE_NAME+'/Repair/checkRepair',
        component: () => import('@/views/repair/repair/check'),
        meta: {
            title: '维修验收'
        },
    },
    //验收列表
    {
        path: MOBILE_NAME+'/Repair/examine',
        component: () => import('@/views/repair/repair/check-list'),
        meta: {
            title: '维修验收列表'
        },
    },
    //维修处理列表
    {
        path: MOBILE_NAME+'/Repair/getRepairLists',
        component: () => import('@/views/repair/repair/list'),
        meta: {
            title: '维修处理列表'
        },
    },
    //个人维修记录
    {
        path: MOBILE_NAME+'/Repair/repair-records',
        name: 'repairRecords',
        component: () => import('@/views/repair/repair/repair-records'),
        meta: {
            title: '维修记录',
            needLogin: true
        },
    },
    //维修接单列表
    {
        path: MOBILE_NAME+'/Repair/ordersLists',
        component:()=>import('@/views/repair/repair/orders-list'),
        meta: {
            title: '维修接单列表'
        },
    },
    //维修检修
    {
        path: MOBILE_NAME+'/Repair/overhaul',
        name:'overhaul',
        component: () => import('@/views/repair/repair/overhaul'),
        meta: {
            title: '维修检修'
        },
    },
    //检修列表
    {
        path: MOBILE_NAME+'/Repair/overhaulLists',
        component: () => import('@/views/repair/repair/overhaul-list'),
        meta: {
            title: '检修列表'
        },
    },
    //维修进程
    {
        path: MOBILE_NAME+'/Repair/progress',
        component: () => import('@/views/repair/repair/progress'),
        meta: {
            title: '维修进程'
        },
    },
    //维修详情
    {
        path: MOBILE_NAME+'/Repair/showRepairDetails',
        component: () => import('@/views/repair/repair/show-progress'),
        meta: {
            title: '维修详情'
        },
    },
    //维修处理
    {
        path: MOBILE_NAME+'/Repair/startRepair',
        name: 'startRepair',
        component: () => import('@/views/repair/repair/start-repair'),
        meta: {
            title: '维修处理'
        },
    },
    //配件管理
    //配件入库
    {
        path: MOBILE_NAME+'/RepairParts/partsInWare',
        component: () => import('@/views/repair/parts/apply'),
        meta: {
            title: '配件入库'
        },
    },
    //配件出库
    {
        path: MOBILE_NAME+'/RepairParts/partsOutWare',
        component: () => import('@/views/repair/parts/outware'),
        meta: {
            title: '配件出库'
        },
    },
    //配件出库列表
    {
        path: MOBILE_NAME+'/RepairParts/partsOutWareList',
        component: () => import('@/views/repair/parts/outware-list'),
        meta: {
            title: '配件出库列表'
        },
    },
    //配件列表
    {
        path: MOBILE_NAME+'/RepairParts/partStockList',
        component: () => import('@/views/repair/parts/list'),
        meta: {
            title: '配件列表'
        },
    },
    //邮件管理
    //邮件内容
    {
        path: MOBILE_NAME+'/Tasks/show_email',
        component: () => import('@/views/tasks/tasks/show-email'),
        meta: {
            title: '邮件内容'
        },
    },
    //档案管理
    //档案盒列表
    {
        path: MOBILE_NAME+'/Box/boxList',
        component: () => import('@/views/archives/box/box-list'),
        meta: {
            title: '档案盒列表'
        },
    },
    //质控管理
    //保养列表
    {
        path: MOBILE_NAME+'/Patrol/tasksList',
        component: () => import('@/views/patrol/patrol/tasks-list'),
        meta: {
            title: '保养列表'
        },
    },
    //质控检测列表
    {
        path: MOBILE_NAME+'/Quality/qualityDetailList',
        component: () => import('@/views/quality/quality/detail-list'),
        meta: {
            title: '质控检测列表'
        },
    },
    //质控检测列表
    {
        path: MOBILE_NAME+'/Quality/qualityResult',
        component: () => import('@/views/quality/quality/result-list'),
        meta: {
            title: '质控检测列表'
        },
    },
        //除颤仪
    {
        path: MOBILE_NAME+'/Quality/setQualityDetail/Tem_ChuChanYi',
        component: () => import('@/views/quality/quality/detail/Tem_ChuChanYi'),
        meta: {
            title: '质控录入'
        },
    },
        //高频电刀
    {
        path: MOBILE_NAME+'/Quality/setQualityDetail/Tem_GaoPingDianDao',
        component: () => import('@/views/quality/quality/detail/Tem_GaoPingDianDao'),
        meta: {
            title: '质控录入'
        },
    },
      //呼吸机
    {
        path: MOBILE_NAME+'/Quality/setQualityDetail/Tem_HuXiJi',
        component: () => import('@/views/quality/quality/detail/Tem_HuXiJi'),
        meta: {
            title: '质控录入'
        },
    },
    //监护仪
    {
        path: MOBILE_NAME+'/Quality/setQualityDetail/Tem_JianHuYi',
        component: () => import('@/views/quality/quality/detail/Tem_JianHuYi'),
        meta: {
            title: '质控录入'
        },
    },
    //输液装置
    {
        path: MOBILE_NAME+'/Quality/setQualityDetail/Tem_ShuYeZhuangZhi',
        component: () => import('@/views/quality/quality/detail/Tem_ShuYeZhuangZhi'),
        meta: {
            title: '质控录入'
        },
    },
    //通用电气
    {
        path: MOBILE_NAME+'/Quality/setQualityDetail/Tem_TongYongDianQi',
        component: () => import('@/views/quality/quality/detail/Tem_TongYongDianQi'),
        meta: {
            title: '质控录入'
        },
    },
    //婴儿培养箱
    {
        path: MOBILE_NAME+'/Quality/setQualityDetail/Tem_YingErPeiYangXiang',
        component: () => import('@/views/quality/quality/detail/Tem_YingErPeiYangXiang'),
        meta: {
            title: '质控录入'
        },
    },
    //除颤仪
    {
        path: MOBILE_NAME+'/Quality/showDetail/Tem_ChuChanYi',
        component: () => import('@/views/quality/quality/show/Tem_ChuChanYi'),
        meta: {
            title: '质控结果'
        },
    },
       //高频电刀
    {
        path: MOBILE_NAME+'/Quality/showDetail/Tem_GaoPingDianDao',
        component: () => import('@/views/quality/quality/show/Tem_GaoPingDianDao'),
        meta: {
            title: '质控结果'
        },
    },
        //呼吸机
    {
        path: MOBILE_NAME+'/Quality/showDetail/Tem_HuXiJi',
        component: () => import('@/views/quality/quality/show/Tem_HuXiJi'),
        meta: {
            title: '质控结果'
        },
    },
        //监护仪
    {
        path: MOBILE_NAME+'/Quality/showDetail/Tem_JianHuYi',
        component: () => import('@/views/quality/quality/show/Tem_JianHuYi'),
        meta: {
            title: '质控结果'
        },
    },
        //输液装置
    {
        path: MOBILE_NAME+'/Quality/showDetail/Tem_ShuYeZhuangZhi',
        component: () => import('@/views/quality/quality/show/Tem_ShuYeZhuangZhi'),
        meta: {
            title: '质控结果'
        },
    },
        //通用电气
    {
        path: MOBILE_NAME+'/Quality/showDetail/Tem_TongYongDianQi',
        component: () => import('@/views/quality/quality/show/Tem_TongYongDianQi'),
        meta: {
            title: '质控结果'
        },
    },
        //婴儿培养箱
    {
        path: MOBILE_NAME+'/Quality/showDetail/Tem_YingErPeiYangXiang',
        component: () => import('@/views/quality/quality/show/Tem_YingErPeiYangXiang'),
        meta: {
            title: '质控结果'
        },
    },
    //审批列表
    {
        path: MOBILE_NAME+'/Notin/approve',
        component: () => import('@/views/pub/notin/approve'),
        meta: {
            title: '审批列表'
        },
    },
    //修改密码
    {
        path: MOBILE_NAME+'/Notin/edit-password',
        component: () => import('@/views/pub/notin/edit-password'),
        meta: {
            title: '修改密码'
        },
    },
    //综合验收页面
    {
        path: MOBILE_NAME+'/Notin/check',
        name: 'Notin_check',
        component: () => import('@/views/pub/notin/check'),
        meta: {
            title: '验收'
        },
    },
    //综合进程页面
    {
        path: MOBILE_NAME+'/Notin/progress',
        name: 'Notin_progress',
        component: () => import('@/views/pub/notin/progress'),
        meta: {
            title: '进程'
        },
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/views/login/login/login'),
        meta: {
            title: '系统登录'
        },
    },
    {
        path: '/logout',
        name: 'logout',
        component: () => import('@/views/login/login/logout'),
        meta: {
            title: '注销'
        },
    },
    //任务列表
    {
        path: MOBILE_NAME+'/Patrol/patrolList',
        component: () => import('@/views/patrol/patrol/patrol-list'),
        meta: {
            title: '计划列表'
        },
    },
    //巡查任务实施列表
    {
        path: MOBILE_NAME+'/Patrol/tasksList',
        component: () => import('@/views/patrol/patrol/tasks-list'),
        meta: {
            title: '计划查询列表'
        },
    },
    //任务实施状态
    {
        path: MOBILE_NAME+'/Patrol/operation',
        component: () => import('@/views/patrol/patrol/operation'),
        meta: {
            title: '操作'
        },
    },
    //任务实施状态
    {
        path: MOBILE_NAME+'/Patrol/doTask',
        component: () => import('@/views/patrol/patrol/do-task'),
        meta: {
            title: '操作'
        },
    },
    {
        path: MOBILE_NAME+'/Inventory',
        name: 'inventory',
        component: () => import('@/views/inventory/plan-list'),
        meta: {
            title: '盘点计划'
        },
    },
    {
        path: MOBILE_NAME+'/Inventory/planShow',
        component: () => import('@/views/inventory/plan-show'),
        meta: {
            title: '盘点计划详情'
        },
    },
    {
        path: MOBILE_NAME+'/Inventory/assetDeal',
        component: () => import('@/views/inventory/plan-deal'),
        meta: {
            title: '盘点设备详情'
        },
    },
];

const router = new VueRouter({
    routes
});

export default router
