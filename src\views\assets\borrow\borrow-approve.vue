<template>
    <div class="con_detail" v-if="page.is_display===1">
        <van-tabs :border="false">
            <van-tab title="设备基本信息">
                <Assetsinfo :info='info'/>
            </van-tab>
            <van-tab title="借调申请信息" v-if="info.borrow!=[]&&info.borrow!=undefined">
                <van-cell-group>
                    <van-cell title="流水号" :value="info.borrow.borrow_num"/>
                    <van-cell title="申请科室" :value="info.borrow.department"/>
                    <van-cell title="申请人" :value="info.borrow.apply_username"/>
                    <van-cell title="申请时间" :value="info.borrow.apply_time"/>
                    <van-cell title="预计归还时间" :value="info.borrow.estimate_back"/>
                    <van-cell title="借用原因" :value="info.borrow.borrow_reason"/>
                </van-cell-group>
            </van-tab>
            <van-tab title="审批信息" v-if="info.approves!=[]&&info.approves!=undefined&&info.approves.length>0">
                <Approves :info='info.approves'/>
            </van-tab>
        </van-tabs>
        <div v-if="page.show_form">
            <div class="card-header">
                <img class="formImage" :src="require('@/assets/images/form/form.png')">
                <h2 class="formTitle">审批表单</h2>
            </div>
            <van-cell-group title="一同借调附属设备" v-if="page.subsidiary.length>0">
                <van-cell v-for="item in page.subsidiary" :key="item.assid" :title="item.assets" :value="item.model"/>
            </van-cell-group>
            <addApprove :info="page" @values="onSubmit"/>
            <div class="cus_loading" v-show="show_loading"><van-loading type="spinner" color="#1989fa" /></div>
        </div>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Field,
        Toast,
        Form,
        Button,
        Tab,
        Tabs,
        Popup,
        Picker,
        DatetimePicker,
        RadioGroup,
        Radio,
        Loading
    } from 'vant';
    import {getInfo, submit} from "@/api/assets/borrow/approve";
    import Assetsinfo from "@/components/Assetsinfo";
    import Approves from "@/components/Approves";
    import addApprove from "@/components/addApprove";

    export default {
        name: 'approve',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Loading.name]: Loading,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [DatetimePicker.name]: DatetimePicker,
            Assetsinfo,
            Approves,
            addApprove,
        },
        data() {
            return {
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    approves: [],//审批记录
                    borrow: [],
                },
                page: {
                    approveDate: '',
                    approveUser: '',
                    is_display: 0,
                    all_approver: '',
                    subsidiary: [],
                    active: '',
                },
                form: {
                    is_adopt: '1',
                    approveRemark: '',
                },
                show_loading:0
            }
        },
        methods: {
            onSubmit(values) {
                values.borid = this.$route.query.borid;
                let _this = this;
                _this.show_loading = 1;
                submit(Qs.stringify(values)).then(response => {
                    _this.show_loading = 0;
                    Toast({
                        type:'success',//失败fail
                        duration:2000,//2秒
                        message: response.msg,
                        icon:'success',//失败cross
                        forbidClick:true,//是否禁止背景点击，避免重复提交
                        onClose(){
                            //关闭后跳转到审批列表页
                            _this.$router.push(this.$store.getters.moduleName+'/Notin/approve');
                        }
                    });
                });
            },
            getInfo(borid) {
                let params = {borid: borid};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.info.borrow = response.borrow;
                    this.info.approves = response.approve;
                    this.page.approveDate = response.approve_time;
                    this.page.approveUser = response.approver;
                    this.page.subsidiary = response.subsidiary;
                    this.page.show_form = response.is_display;
                    this.page.is_display = 1;
                });
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.borid);
        }
    }
</script>
<style scoped lang="scss">
    .card-header {
        margin-top: 20px;
    }

    .app_pro {
        background: #fff;
    }
</style>
