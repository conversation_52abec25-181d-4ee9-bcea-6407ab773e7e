<template>
    <div class="list">
        <div v-if="show.show_repair==1">
            <van-row>
                <van-col span="18" @click="is_display.repair=is_display.repair?false:true;">
                    <div class="bgcf total-div">
                        <div class="total-div-title" id="repair">
                            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                            维修流程 <span class="num"> {{total.repair}} </span>条
                            <span class="tips" v-show="tips.repair.up" @click="show_hidden('repair')">收起<van-icon name="arrow-up" /></span>
                            <span class="tips" v-show="tips.repair.down" @click="show_hidden('repair')">展开<van-icon name="arrow-down" /></span>
                        </div>
                    </div>

                </van-col>
                <van-col span="6">
                    <Sort @order="repair_order" :option="option.repair"/>
                </van-col>
            </van-row>
            <van-list
                    v-model="loading.repair"
                    v-show="is_display.repair"
                    :finished="finished.repair"
                    @load="repairList"
            >
                <van-card v-for="item in list.repair_list" :key="item.assid">
                    <template #title>
                    设备名称：{{item.assets}}
                </template>
                <template #desc>
                    <ul>
                        <li>维修单号：{{item.repnum}}</li>
                        <li>使用科室：{{item.department}}</li>
                        <li>设备编号：{{item.assnum}}</li>
                        <li>固定资产编号：{{item.assorignum}}</li>
                        <li>维修状态：{{item.status_name}}</li>
                        <li>接单时间：{{item.applicant_time}}</li>
                    </ul>
                </template>
                <template #footer>
                    <van-button  type="info" block size="small" class="detail-button" :to="{ path: $store.getters.moduleName+'/Repair/showRepairDetails', query: { repid: item.repid }}">查看</van-button>
                </template>
            </van-card>
            </van-list>
            <van-pagination v-model="page.repair" :total-items="total.repair" :items-per-page="5" v-show="is_display.repair&&total.repair>5" @change="repair_change"/>
            <van-divider/>
        </div>
        <div v-if="show.show_borrow==1">
            <van-row>
                <van-col span="18" @click="is_display.borrow=is_display.borrow?false:true;">
                    <div class="bgcf total-div">
                        <div class="total-div-title" id="borrow">
                            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                            借调流程 <span class="num"> {{total.borrow}} </span>条
                            <span class="tips" v-show="tips.borrow.up" @click="show_hidden('borrow')">收起<van-icon name="arrow-up" /></span>
                            <span class="tips" v-show="tips.borrow.down" @click="show_hidden('borrow')">展开<van-icon name="arrow-down" /></span>
                        </div>
                    </div>
                </van-col>
                <van-col span="6">
                    <Sort @order="borrow_order" :option="option.borrow"/>
                </van-col>
            </van-row>
            <van-list
                    v-model="loading.borrow"
                    v-show="is_display.borrow"
                    :finished="finished.borrow"
                    @load="borrowList"
            >
                <van-card v-for="item in list.borrow_list" :key="item.assid">
                    <template #desc>
                        <ul>
                        <li>
                            <span class="detailText">借调单号：</span>
                            <span class="text">{{item.borrow_num}}</span>
                        </li>
                        <li>
                            <span class="detailText">设备名称：</span>
                            <span class="text">{{item.assets}}</span>
                        </li>
                        <li>
                            <span class="detailText">规格型号：</span>
                            <span class="text">{{item.model}}</span>
                        </li>
                        <li>
                            <span class="detailText">设备编号：</span>
                            <span class="text">{{item.assnum}}</span>
                        </li>
                        <li>
                            <span class="detailText">借出科室：</span>
                            <span class="text">{{item.department}}<img :src="require('@/assets/images/list/right.png')" style="width: 24px;margin-left: 5px;margin-bottom: -7px;"></span>
                        </li>
                        <li>
                            <span class="detailText">借入科室：</span>
                            <span class="text">{{item.apply_department}}<img :src="require('@/assets/images/list/left.png')" style="width: 24px;margin-left: 5px;margin-bottom: -7px;"></span>
                        </li>
                        <li>
                            <span class="detailText">进程状态：</span>
                            <span v-html="item.show_status_name"/>
                        </li>


                        </ul>
                    </template>
                    <template #footer>
                        <van-button type="info" block size="small" class="detail-button" :to="{ path: $store.getters.moduleName+'/Borrow/progress_detail', query: { borid: item.borid }}">详情</van-button>
                    </template>
                </van-card>
            </van-list>
            <van-pagination v-model="page.borrow" :total-items="total.borrow" :items-per-page="5" v-show="is_display.borrow&&total.borrow>5" @change="borrow_change"/>
            <van-divider/>
            <van-divider/>
        </div>
        <div v-if="show.show_transfer==1">
            <van-row>
                <van-col span="18" @click="is_display.transfer=is_display.transfer?false:true;">
                    <div class="bgcf total-div">
                        <div class="total-div-title"  id="transfer">
                            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                            转科流程 <span class="num"> {{total.transfer}} </span>条
                            <span class="tips" v-show="tips.transfer.up" @click="show_hidden('transfer')">收起<van-icon name="arrow-up" /></span>
                            <span class="tips" v-show="tips.transfer.down" @click="show_hidden('transfer')">展开<van-icon name="arrow-down" /></span>
                        </div>
                    </div>
                </van-col>
                <van-col span="6">
                    <Sort @order="transfer_order" :option="option.transfer"/>
                </van-col>
            </van-row>
            <van-list
                    v-model="loading.transfer"
                    v-show="is_display.transfer"
                    :finished="finished.transfer"
                    @load="transferList"
            >
                <van-card v-for="item in list.transfer_list" :key="item.assid">
                    <template #desc>
                    <ul>
                        <li>
                            <span class="detailText">转科单号：</span>
                            <span class="text">{{item.transfernum}}</span>
                        </li>
                        <li>
                            <span class="detailText">设备名称：</span>
                            <span class="text">{{item.assets}}</span>
                        </li>
                        <li>
                            <span class="detailText">规格型号：</span>
                            <span class="text">{{item.model}}</span>
                        </li>
                        <li>
                            <span class="detailText">设备编号：</span>
                            <span class="text">{{item.assnum}}</span>
                        </li>
                        <li>
                            <span class="detailText">转出科室：</span>
                            <span v-html="item.tranout_depart_name"/><img :src="require('@/assets/images/list/right.png')" style="width: 24px;margin-left: 5px;margin-bottom: -7px;">
                        </li>
                        <li>
                            <span class="detailText">转入科室：</span>
                            <span v-html="item.tranin_depart_name"/><img :src="require('@/assets/images/list/left.png')" style="width: 24px;margin-left: 5px;margin-bottom: -7px;">
                        </li>
                        <li>
                            <span class="detailText">进程状态：</span>
                            <span v-html="item.show_status_name"/>
                        </li>
                    </ul>
                </template>
                <template #footer>
                    <van-button type="info" block size="small" class="detail-button" :to="{ path: $store.getters.moduleName+'/Transfer/progress_detail', query: { atid: item.atid }}">详情</van-button>
                </template>
                </van-card>
            </van-list>
            <van-pagination v-model="page.transfer" :total-items="total.transfer" :items-per-page="5" v-show="is_display.transfer&&total.transfer>5" @change="transfer_change"/>
            <van-divider/>
            <van-divider/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {
        Cell,
        CellGroup,
        List,
        Button,
        Card,
        Search,
        Col,
        Row,
        Icon,
        DropdownMenu,
        DropdownItem,
        Divider,
        Pagination
    } from "vant";
    import {getprogressList, postprogressList} from "@/api/common/progress";
    import Sort from "@/components/Sort";

    export default {
        name: 'getprogressList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Divider.name]: Divider,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [Pagination.name]: Pagination,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            Sort,
        },
        data() {
            return {
                list: {
                    repair_list: [],
                    borrow_list: [],
                    transfer_list: [],
                },
                show: {
                    show_borrow: 0,
                    show_repair: 0,
                    show_transfer: 0,
                },
                is_display: {
                    repair: true,
                    borrow: true,
                    transfer: true,
                },
                tips: {
                    repair: {
                        up:true,
                        down:false
                    },
                    borrow: {
                        up:true,
                        down:false
                    },
                    scrap: {
                        up:true,
                        down:false
                    },
                    transfer: {
                        up:true,
                        down:false
                    },
                },
                keyword: '',
                loading: {
                    repair: false,
                    borrow: false,
                    transfer: false,
                },
                finished: {
                    repair: false,
                    borrow: false,
                    transfer: false,
                },
                page:{
                    repair : 1,
                    borrow : 1,
                    transfer : 1,
                },
                option: {
                    repair: [
                        {text: '按科室名称（降序）', value: 'department-desc'},
                        {text: '按报修时间（降序）', value: 'applicant_time-desc'},
                        {text: '按科室名称（升序）', value: 'department-asc'},
                        {text: '按报修时间（升序）', value: 'applicant_time-asc'},],
                    borrow: [
                        {text: '按科室名称（降序）', value: 'apply_departid-desc'},
                        {text: '按申请时间（降序）', value: 'apply_time-desc'},
                        {text: '按科室名称（升序）', value: 'apply_departid-asc'},
                        {text: '按申请时间（升序）', value: 'apply_time-asc'},],
                    transfer: [
                        {text: '按科室名称（降序）', value: 'department-desc'},
                        {text: '按申请时间（降序）', value: 'applicant_time-desc'},
                        {text: '按科室名称（升序）', value: 'department-asc'},
                        {text: '按申请时间（升序）', value: 'applicant_time-asc'},],

                },
                total: {
                    repair: 0,
                    borrow: 0,
                    transfer: 0,
                },
                //全局列表搜索条件
                where: {}
            };
        },
        methods: {
            borrow_change(value){
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: value};
                this.borrowList();
            },
            transfer_change(value){
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: value};
                this.transferList();
            },
            repair_change(value){
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: value};
                this.repairList();
            },
            repairList() {
                let data = [];
                let str;
                if (this.where.order) {
                    str = '&order='+this.where.order+'&sort='+this.where.sort;
                }
                data.action = "repair";
                postprogressList(Qs.stringify(data),this.where.page,str).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished.repair = true;
                    }
                    this.list.repair_list = [];
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.repair_list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    this.total.repair = response.total;
                    //数据获取到了 停止加载
                    this.loading.repair = false;
                    this.finished.repair = true;
                    if (this.where.page) {
                        document.getElementById("repair").scrollIntoView();
                    }
                    this.where = [];
                })
            },
            borrowList() {
                let data = [];
                let str;
                if (this.where.order) {
                    str = '&order='+this.where.order+'&sort='+this.where.sort;
                }
                data.action = "borrow";
                postprogressList(Qs.stringify(data),this.where.page,str).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished.borrow = true;
                    }
                    this.list.borrow_list = [];
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.borrow_list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    this.total.borrow = response.total;
                    //数据获取到了 停止加载
                    this.loading.borrow = false;
                    this.finished.borrow = true;
                    if (this.where.page) {
                        document.getElementById("borrow").scrollIntoView();
                    }
                    this.where = [];
                })
            },
            transferList() {
                let data = [];
                let str;
                if (this.where.order) {
                    str = '&order='+this.where.order+'&sort='+this.where.sort;
                }
                data.action = "transfer";
                postprogressList(Qs.stringify(data),this.where.page,str).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished.transfer = true;
                    }
                    this.list.transfer_list = [];
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.transfer_list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    this.total.transfer = response.total;
                    //数据获取到了 停止加载
                    this.loading.transfer = false;
                    this.finished.transfer = true;
                    if (this.where.page) {
                        document.getElementById("transfer").scrollIntoView();
                    }
                    this.where = [];
                });
            },
            getcompetence() {
                getprogressList(this.where).then(response => {
                    this.show = response.data;
                });
            },
            repair_order(value) {
                this.list.repair_list = [];
                this.loading.repair = true;
                this.finished.repair = false;
                //重新加搜索条件
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.repairList();
            },
            borrow_order(value) {
                this.list.borrow_list = [];
                this.loading.borrow = true;
                this.finished.borrow = false;
                //重新加搜索条件
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.borrowList();
            },
            transfer_order(value) {
                this.list.transfer_list = [];
                this.loading.transfer = true;
                this.finished.transfer = false;
                //重新加搜索条件
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.transferList();
            },
            show_hidden(module){
                if(this.is_display[module]){
                    this.tips[module].up = false;
                    this.tips[module].down = true;
                }else{
                    this.tips[module].down = false;
                    this.tips[module].up = true;
                }
            }
        }, mounted() {
            //获取相关设备信息
            this.getcompetence();
        }
    }
</script>

<style scoped lang="scss">
.tips{
    color:#DEDEDE;
    margin-left: 20px;
}
.tips{
    .van-icon{
        top:2px;
        padding-left: 8px;
    }
}
</style>
