import request from '@/utils/request'
//报废申请列表
export function getApplyList(query) {
    return request({
        url: 'Scrap/getApplyList',
        params: query
    })
}
// 报废查询列表
export function getScrapList(query) {
    return request({
        url: '/Scrap/getScrapList',
        params: query
    })
}
// 报废处置列表
export function getResultList(query) {
    return request({
        url: '/Scrap/getResultList',
        params: query
    })
}
