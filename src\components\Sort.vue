<template>
    <div class="Sort"> 
         <van-dropdown-menu class="filter">
                    <van-dropdown-item title="排序" v-model="orderValue" :options="option" @change="order"/>
          </van-dropdown-menu>
    </div>
</template>
<script>
    import {Divider, Swipe, SwipeItem, Cell, CellGroup, Tab, Tabs, Step, Steps,DropdownMenu, DropdownItem} from 'vant';
    export default {
        name: 'Sort',
        components: {
            [Divider.name]: Divider,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Step.name]: Step,
            [Steps.name]: Steps,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
        },
        data() {
            return {
                position: 0,
                orderValue: '',
            }
        },
        props: ['option'],
        methods: {
            order: function (value) {
                this.$emit("order", value);
            },
        },
    }
</script>

<style scoped lang="scss">
    </style>
