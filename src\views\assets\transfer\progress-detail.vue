<template>
    <div class="con_detail">
        <van-tabs :border="false">
            <van-tab title="转科申请信息">
                <TransferInfo :info='transInfo'/>
            </van-tab>
            <van-tab title="设备基本信息">
                <AssetsInfo :info='info'/>
            </van-tab>
            <van-tab title="转科审批信息" v-if="approves">
                <Approves :info='approves'/>
            </van-tab>
        </van-tabs>
        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">设备转科进程</h2>
        </div>
        <van-steps direction="vertical" :active="0">
            <van-step v-for="(item,index) in line" :key="index">
                <h3>{{item.statusName}}  {{item.date}}</h3>
                <span v-html="item.text"/>
            </van-step>
            <van-step>
                <h3>设备入库</h3>
                <p>{{info.storage_date}}</p>
            </van-step>
        </van-steps>
    </div>
</template>

<script>
    import {Divider, Swipe, SwipeItem, Cell, CellGroup, Tab, Tabs, Step, Steps, ImagePreview, Image as VanImage, Overlay} from 'vant';
    import {getInfo} from "@/api/assets/transfer/progress";
    import AssetsInfo from "@/components/Assetsinfo";
    import TransferInfo from '@/components/TransferInfo';
    import Approves from '@/components/Approves';
    export default {
        name: 'progressDetail',
        components: {
            [Divider.name]: Divider,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Step.name]: Step,
            [Steps.name]: Steps,
            [Steps.Overlay]: Overlay,
            [VanImage.name]: VanImage,
            AssetsInfo,
            Approves,
            TransferInfo,
        },
        methods: {
            getInfo(atid,assnum) {
                getInfo(
                    {atid: atid,assnum:assnum,action:'detail'}
                ).then(response => {
                    this.transInfo = response.transInfo;
                    this.info = response.asArr;
                    this.line = response.line;
                    this.approves = response.approves;
                })
            },
            onChange(index) {
                this.positions = index;
            },
            showImage() {
                ImagePreview({
                    images: this.info.pic_url,
                    startPosition: this.positions,
                });
            },
        },
        data() {
            return {
                position: 0,
                line:[],
                approves: [],
                transInfo: [],
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    life: []
                },
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.atid,this.$route.query.assnum);
        }
    }
</script>

<style scoped lang="scss">
    .red {
        color: #FF5722
    }

    .green {
        color: #85AB70
    }
    .card-header{
        margin-top: 20px;
    }
</style>
