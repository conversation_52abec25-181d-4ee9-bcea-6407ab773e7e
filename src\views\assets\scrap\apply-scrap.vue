<template>
    <div class="con_detail" v-if="page.is_display===1">
        <div class="card-header">
            <h2 class="detailTitle">设备基本信息</h2>
            <div class="bl1px"></div>
        </div>
        <AssetsInfo :info='info'/>

        <div v-if="page.show_form === 1" style="margin-bottom: 20px;">
            <div class="card-header">
                <h2 class="detailTitle">审批记录</h2>
                <div class="bl1px"></div>
            </div>
            <Approves :info='info.approves'/>
        </div>

        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">报废申请表单</h2>
        </div>
        <van-form ref="scrapForm" @submit="onSubmit">
            <van-cell-group>
                <van-field label="报废单号" :value="info.scrapnum" readonly/>
                <van-field label="申请人" :value="page.username" readonly/>
                <van-field label="报废日期" :value="form.now" readonly/>
                <van-field
                        v-model="form.scrap_reason"
                        :rules="[{ validator, message: '请填写报废原因' }]"
                        name="scrap_reason"
                        required
                        label="报废原因"
                        type="textarea"
                        rows="2"
                        maxlength="60"
                        show-word-limit
                        placeholder="报废原因"

                />
            </van-cell-group>
            <div style="margin: 16px;">
                <van-button round block type="warning" native-type="button" @click="changeFormType('edit')" v-show="form.scrid>0">申请重审</van-button>
                <van-button round block type="danger" native-type="button" @click="changeFormType('end')" v-show="form.scrid >0">结束进程</van-button>
                <van-button round block type="info" native-type="button" @click="changeFormType('apply')" v-show="form.scrid == ''">提交</van-button>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Divider, Cell, CellGroup, Icon, Field, Form, Button, Tab, Tabs, Toast, Loading} from 'vant';
    import {getInfo, submit} from "@/api/assets/scrap/apply";
    import AssetsInfo from "@/components/Assetsinfo";
    import Approves from '@/components/Approves';

    export default {
        name: 'applyScrap',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Loading.name]: Loading,
            AssetsInfo,
            Approves,
        },
        data() {
            return {
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    scrapInfo: [],//是否有历史报废信息
                    approves: [],
                    scriptdata: {
                        scrapnum: '',
                    },
                },
                page: {
                    username: '',
                    show_form: 1,
                    is_display: 0,
                },
                form: {
                    scrap_reason: '',
                    now: '',
                    scrid: 0,
                    formType: 'apply',//apply 报废申请 edit 报废重审 end 报废结束进程
                },
            }
        },
        methods: {
            validator(val) {
                if (this.form.scrid > 0) {
                    return true;
                } else {
                    if (typeof val === 'undefined' || val.replace(/(^\s*)|(\s*$)/g, "") === '') {
                        this.form.scrap_reason = '';
                        return false;
                    }
                }
            },
            getInfo(assnum) {
                let params = {assnum: assnum};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.form.now = response.now;
                    this.page.username = response.username;
                    this.page.show_form = response.show_form;
                    this.info.approves = response.asArr.approves;
                    if(response.scrapinfo){
                        this.form.scrid = response.scrapinfo.scrid;
                        this.form.scrap_reason = response.scrapinfo.scrap_reason;
                        this.info.scriptdata = response.scrapinfo.scrapdate;
                    }
                    this.page.is_display = 1;
                })
            },
            onSubmit(values) {
                switch (this.form.formType) {
                    case "apply":
                        //报废申请
                        values.assid = this.info.assid;
                        values.scrapnum = this.info.scrapnum;
                        values.type = this.form.formType;
                        break;
                    case "edit":
                        //报废重审
                        values.scrid = this.form.scrid;
                        values.type = this.form.formType;
                        break;
                    case "end":
                        //报废结束进程
                        values.scrid = this.form.scrid;
                        values.type = this.form.formType;
                        break;
                }
                //发送请求
                let _this = this;
                submit(Qs.stringify(values)).then(response => {
                    if(response.status === 1){
                        Toast({
                            type:'success',//失败fail
                            duration:2000,//2秒
                            message: response.msg,
                            icon:'success',//失败cross
                            forbidClick:true,//是否禁止背景点击，避免重复提交
                            onClose(){
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Scrap/getApplyList');
                            }
                        });
                    }
                })
            },
            changeFormType(type) {
                this.form.formType = type;//改变提交表单的formType
                this.$refs.scrapForm.submit();//执行提交
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.assnum);
        }
    }
</script>

<style scoped lang="scss">
    .van-button {
        margin-top: 10px;
    }
</style>
