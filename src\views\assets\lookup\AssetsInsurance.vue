<template>
  <div class="AssetsInfo">
    <van-cell-group title="参保信息">
      <div v-for="item in insurance" :key="item.insurid">
        <van-cell title="维保公司名称 " :value="item.company"/>
        <van-cell title="维保性质 " :value="item.nature"/>
        <van-cell title="参保费用 " :value="item.cost"/>
        <van-cell title="购入日期 " :value="item.buydate"/>
        <van-cell title="维保期限 " :value="item.term"/>
        <van-cell title="联系人 " :value="item.contacts"/>
        <van-cell title="联系电话 " :value="item.telephone"/>
        <van-cell title="添加用户 " :value="item.adduser"/>
        <van-cell title="添加时间 " :value="item.adddate"/>
        <van-cell title="修改用户 " :value="item.edituser"/>
        <van-cell title="修改时间 " :value="item.editdate"/>
        <van-cell title="维保内容 " :value="item.content"/>
        <van-cell title="相关文件 ">
          <template #default>
            <div v-for="item1 in item.file_list" :key="item1.file_name">
              <a style="color: #4994df" size="small" type="info" v-if="item1.file_type === 'pdf'"
                 @click="scan_file(item1.file_url)">{{ item1.file_name }}
              </a>
              <a style="color: #4994df" size="small" type="info" v-if="item1.file_type === 'jpg'||item1.file_type === 'png'"
                 @click="img(item1.file_url)">{{ item1.file_name }}
              </a>
            </div>
          </template>
        </van-cell>
        <van-cell title="备注 " :value="item.remark"></van-cell>
      </div>

    </van-cell-group>
  </div>
</template>
<script>
import { Cell, CellGroup, ImagePreview } from 'vant';

export default {
  components: {
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
  },
  methods: {
    img(url) {
      ImagePreview([process.env.VUE_APP_BASE_PROXY_URL + url]);
    },
    scan_file(path) {
      path = process.env.VUE_APP_BASE_PROXY_URL + path;
      this.$router.push({
        name: 'showFile',
        query: {
          path: path
        }
      });
    },
  },
  props: ['insurance'],
}
</script>

