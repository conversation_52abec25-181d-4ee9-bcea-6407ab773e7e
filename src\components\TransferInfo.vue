<template>
    <div class="TransferInfo">
        <van-cell-group>
            <van-cell title="转科单号" :value="info.transfernum"/>
            <van-cell title="转科文号" :value="info.tran_docnum"/>
            <van-cell title="申请人" :value="info.applicant_user"/>
            <van-cell title="申请时间" :value="info.applicant_time"/>
            <van-cell title="转出科室" :value="info.tranout_depart_name"/>
            <van-cell title="转入科室" :value="info.tranin_depart_name"/>
            <van-cell title="转科日期" :value="info.transfer_date"/>
            <van-cell title="转科原因" :value="info.tran_reason"/>
        </van-cell-group>
    </div>
</template>
<script>
    import {Cell, CellGroup} from 'vant';

    export default {
        name: 'TransferInfo',
        components: {
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
        },
        methods: {

        },
        data() {
            return {
                position: 0,
            }
        },
        props: ['info'],
         mounted() {

         }
    }
</script>

<style scoped lang="scss">


</style>
