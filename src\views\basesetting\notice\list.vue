<template>
    <div class="list">
        <van-row>
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        公告数 <span class="num"> {{total}} </span>
                    </div>
                </div>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="refreshList"
        >
            <div class="not_list" v-for="item in list" :key="item.notid" @click="onclick(item.notid)">
                <div class="not_title">
                    <van-cell>
                        <template #title>
                            <span>{{item.title}}</span>
                            <span v-if="item.top === 1"><van-tag color="#1989FA" plain>置顶</van-tag></span>
                        </template>
                        <template #icon>
                            <van-icon name="gonggao" class-prefix="wx-icon" />
                        </template>
                        <template #label>
                            <span class="name">{{item.adduser}}</span>
                            <span>{{item.add_time}}</span>
                        </template>
                    </van-cell>
                </div>
                <div class="not_content" v-html="item.content"></div>
            </div>
        </van-list>
    </div>
</template>

<script>
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem,Tag} from "vant";
    import {getNoticeList} from "@/api/basesetting/list";

    export default {
        name: 'getNoticeList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            [Tag.name]: Tag,
        },
        data() {
            return {
                list: [],
                keyword: '',
                loading: false,
                finished: false,
                total: 0,
                //全局列表搜索条件
                where: {
                    page: 1
                }
            };
        },
        methods: {
            onclick(notid){
            this.$router.push(this.$store.getters.moduleName+'/Notice/showNotice?id='+notid);
        },
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                setTimeout(() => {
                    getNoticeList(this.where).then(response => {
                        //先判断如果返回的总数为0 则把状态设置为停止加载
                        if (response.total === 0) {
                            this.finished = true;
                        }
                        //判断数据键值是否存在
                        if (Object.prototype.hasOwnProperty.call(response, 'row')) {
                            this.list.push(...response.row);
                        }
                        //仅第一页赋值总数
                        if (this.where.page === 1) {
                            this.total = response.total;
                        }
                        //数据获取到了 停止加载
                        this.loading = false;
                        //全部加载了 且当前列表数量大于总数 设置完成状态
                        if (this.list.length >= this.total) {
                            this.finished = true;
                        }
                        //页数加1
                        this.where.page++;
                    })
                }, 100);
            }
        },
    }
</script>

<style scoped lang="scss">
    .not_list{
        background: #fff;
        width: 96%;
        margin: 1rem auto;
        border-radius: 8px;
        padding-bottom: 0.5rem;

        .top{
            color:#1989FA;
        }
        .wx-icon{
            font-size: 20px;
            margin-right: 6px;
            color:#ff976a;
        }
        .van-cell__title{
            font-size: 16px;
            color:#000;
            padding-bottom: 5px;
            border-bottom: 1px solid #ebedf0;
            margin-left: -20px;
            padding-left: 18px;
        }
        .van-cell__label{
            font-size: .875rem;
        }
        .name{
            margin-right: 20px;
        }
        .not_content{
            width: 90%;
            margin: 0 auto;
            color:#999;
            max-height: 4.8rem;
            overflow: hidden;
            font-size: .875rem;
        }
        .van-tag{
            height: 1.2rem;
            padding: 0 0.4rem;
            float: right;
        }
    }
    .van-cell{background: none;}
</style>
<style>
    p{
        padding: 0;
        margin: 0;
    }
</style>
