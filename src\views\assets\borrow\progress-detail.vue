<template>
    <div class="con_detail">
        <van-tabs :border="false">
            <van-tab title="借调申请信息" >
                 <van-cell-group  >
                    <van-cell title="流水号" :value="borrowinfo.borrow_num"/>
                    <van-cell title="申请科室" :value="borrowinfo.department"/>
                    <van-cell title="申请人" :value="borrowinfo.apply_username"/>
                    <van-cell title="申请时间" :value="borrowinfo.apply_time"/>
                    <van-cell title="预计归还时间" :value="borrowinfo.estimate_back"/>
                    <van-cell title="借用原因" :value="borrowinfo.borrow_reason"/>
                </van-cell-group>
            </van-tab>
            <van-tab title="设备基本信息">
                <AssetsInfo :info='info'/>
            </van-tab>
            <van-tab title="借调审批信息" v-if="approves">
                <Approves :info='approves'/>
            </van-tab>
        </van-tabs>
        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">设备借调进程</h2>
        </div>
        <van-steps direction="vertical" :active="0">
            <van-step v-for="(item,index) in line" :key="index">
                <h3>{{item.statusName}}  {{item.date}}</h3>
                <span v-html="item.text"/>
            </van-step>
            <van-step>
                <h3>设备入库</h3>
                <p>{{info.storage_date}}</p>
            </van-step>
        </van-steps>
    </div>
</template>

<script>
    import {Divider, Swipe, SwipeItem, Cell, CellGroup, Tab, Tabs, Step, Steps, ImagePreview, Image as VanImage, Overlay} from 'vant';
    import {getInfo} from "@/api/assets/borrow/progress";
    import AssetsInfo from "@/components/Assetsinfo";
    import Approves from '@/components/Approves';
    export default {
        name: 'progressDetail',
        components: {
            [Divider.name]: Divider,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Step.name]: Step,
            [Steps.name]: Steps,
            [Steps.Overlay]: Overlay,
            [VanImage.name]: VanImage,
            AssetsInfo,
            Approves,
        },
        methods: {
            getInfo(borid) {
                getInfo(
                    {borid: borid}
                ).then(response => {
                    this.borrowinfo = response.borrow;
                    this.info = response.asArr;
                    this.line = response.line;
                    this.approves = response.approve;
                })
            },
            onChange(index) {
                this.positions = index;
            },
            showImage() {
                ImagePreview({
                    images: this.info.pic_url,
                    startPosition: this.positions,
                });
            },
        },
        data() {
            return {
                position: 0,
                line:[],
                approves: [],
                borrowinfo: [],
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    life: []
                },
            }
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.borid);
        }
    }
</script>

<style scoped lang="scss">
    .red {
        color: #FF5722
    }

    .green {
        color: #85AB70
    }
    .card-header{
        margin-top: 20px;
    }
</style>
