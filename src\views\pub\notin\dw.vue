<template>
    <div>
        <van-image :src="require('@/assets/images/downloads.png')" />
    </div>
</template>

<script>
    import { Image as VanImage } from 'vant';

    export default {
        name: "dw",
        components: {
            [VanImage.name]: VanImage,
        },
        mounted(){
            let path = this.$route.query.path;
            path = encodeURIComponent(path);
            let name = this.$route.query.name;
            let size = this.$route.query.size;
            var is_weixin = this.is_weixin();
            if(is_weixin){
                //是微信浏览器  显示返回按钮   （用户可以选择下载或者返回）

            }else{
                //不是微信浏览器
                //执行下载
                window.location.href=process.env.VUE_APP_BASE_PROXY_URL+process.env.VUE_APP_BASE_AXIOS_URL+"/Notin/downloads?path="+path+"&name="+name+"&size="+size;
            }
        },
        methods:{
            is_weixin(){
                let ua = window.navigator.userAgent.toLowerCase();
                if (ua.match(/MicroMessenger/i) == 'micromessenger') {
                    console.log("微信浏览器");
                    return true;
                }else{
                    console.log("不是微信浏览器");
                    return false;
                }
            }
        }
    }
</script>
