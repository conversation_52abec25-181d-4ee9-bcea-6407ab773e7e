 <template>
    <div class="con_detail" v-if="page.is_display===1">
        <div class="card-header">
            <h2 class="detailTitle">维修单详情</h2>
            <div class="bl1px"></div>
        </div>
        <RepairInfo :info='info'/>
        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">设备修复验收表单</h2>
        </div>
        <van-form ref="repairForm" @submit="onSubmit">
            <van-cell-group>
                <van-field name="repaired" label="是否修复">
                    <template #input>
                        <van-radio-group v-model="form.repaired" direction="horizontal">
                            <van-radio name="1" checked-color="#5FB878">已修复</van-radio>
                            <van-radio name="0" checked-color="#5FB878">未修复</van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-field name="attitude" label="服务态度">
                    <template #input>
                        <van-radio-group v-model="form.attitude" direction="horizontal">
                            <van-radio name="0" checked-color="#5FB878">好</van-radio>
                            <van-radio name="1" checked-color="#5FB878">一般</van-radio>
                            <van-radio name="2" checked-color="#5FB878">差</van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-field name="technical" label="技术水平">
                    <template #input>
                        <van-radio-group v-model="form.technical" direction="horizontal">
                            <van-radio name="0" checked-color="#5FB878">好</van-radio>
                            <van-radio name="1" checked-color="#5FB878">一般</van-radio>
                            <van-radio name="2" checked-color="#5FB878">差</van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-field name="efficiency" label="响应时效">
                    <template #input>
                        <van-radio-group v-model="form.efficiency" direction="horizontal">
                            <van-radio name="0" checked-color="#5FB878">好</van-radio>
                            <van-radio name="1" checked-color="#5FB878">一般</van-radio>
                            <van-radio name="2" checked-color="#5FB878">差</van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-field
                        v-model="form.checkRemark"
                        name="checkRemark"
                        rows="1"
                        autosize
                        label="意见/建议"
                        type="textarea"
                        placeholder="请输入意见/建议"
                />
            </van-cell-group>
            <div style="margin: 16px;">
                <van-button color="#009688" block type="info" native-type="submit">确认验收并提交</van-button>
            </div>
        </van-form>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px" />
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Divider, Cell, CellGroup, Icon, Field, Form, Button, Tab, Tabs, Toast,Loading,Popup,Picker,DatetimePicker,RadioGroup, Radio } from 'vant';
    import {getInfo,submit} from "@/api/repair/check";
    import RepairInfo from "@/components/RepairInfo";

    export default {
        name: 'check',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Loading.name]: Loading,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [DatetimePicker.name]: DatetimePicker,
            RepairInfo,
        },
        data() {
            return {
                info: {},
                page: {
                    is_display: 0,//用于判断是否加载完数据
                },
                form: {
                    repaired: '1',
                    attitude: '0',
                    technical: '0',
                    efficiency: '0',
                    checkRemark: '',
                    repair_check: '',

                },
            }
        },
        methods: {
            getInfo(repid) {
                let params = {repid: repid};
                getInfo(params).then(response => {
                    this.info = response.repArr;
                    this.repair_check = response.repair_check;
                    this.page.is_display = 1;

                });
            },
            onSubmit(values) {
                //发送请求
                values.repid = this.$route.query.repid;
                values.repair_check = this.form.repair_check;
                let _this = this;
                submit(Qs.stringify(values)).then(response => {
                    if (response.status==1) {
                    Toast({
                            type:'success',//失败fail
                            duration:2000,//2秒
                            message: response.msg,
                            icon:'success',//失败cross
                            forbidClick:true,//是否禁止背景点击，避免重复提交
                            onClose(){
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Repair/examine');
                            }
                        });
                    }
                    //this.$router.push('/');
                })
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.repid);
        }
    }
</script>

<style scoped lang="scss">
    .van-button {
        margin-top: 10px;
    }
</style>
