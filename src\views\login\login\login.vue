<template>
  <div class="login">
    <div class="logoDiv">
      <img class="logo" :src="icon" alt="logo">
    </div>
    <h2>{{ title }}</h2>
    <van-form @submit="onSubmit">
      <van-field
          name="username"
          v-model="username"
          label="用户名"
          placeholder="用户名"
          :rules="[{ required: true, message: '请填写用户名' }]"
      />
      <van-field
          name="password"
          v-model="password"
          type="password"
          label="密码"
          placeholder="密码"
          :rules="[{ required: true, message: '请输入密码' }]"
      />
      <van-field
          name="publickey"
          type="hidden"
          :value="pub_key"
      />
      <drag-verify
          :width="320"
          :isPassing.sync="verify"
          text="请按住滑块拖动"
          successText="验证通过"
          handlerIcon="van-icon van-icon-arrow"
          successIcon="van-icon van-icon-passed"
          background="#ccc"
      >
      </drag-verify>
<!--      <Vcode :show="isShow" @success="success" @close="close"/>-->
      <div class="submit-button">
        <van-button round block type="info" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
  </div>
</template>
<script>
import {
  Form,
  Field,
  Button,
  Toast,
} from "vant";

import Qs from 'qs';
import {JSEncrypt} from 'jsencrypt';
import {login, getKey, getOpenid} from "@/api/login/login";
// import Vcode from "vue-puzzle-vcode";
import dragVerify from 'vue-drag-verify2'

export default {
  name: 'login',
  data() {
    return {
      icon: require('@/assets/images/logo-new.png'),
      title: "天成医疗设备管理系统",
      username: "",
      password: "",
      pub_key: '',
      verify: false,
    }
  },
  components: {
    [Form.name]: Form,
    [Field.name]: Field,
    [Button.name]: Button,
    dragVerify
    // Vcode
  },
  mounted() {
    this.get_openid();
    this.get_userinfo();
  },
  methods: {
    get_openid(){
      if(process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX){
        getOpenid().then(res => {
          if(!res.openid){
            window.location.href = process.env.VUE_APP_BASE_PROXY_URL+'/'+process.env.VUE_APP_BASE_AXIOS_URL+'/Login/getUserOpenId'
          }
        });
      }
    },
    get_userinfo(){
      if(localStorage.getItem('uname')){
        this.username = localStorage.getItem('uname');
      }
      if(localStorage.getItem('pw')){
        this.password = localStorage.getItem('pw');
      }
    },
    getPubKey() {
      getKey().then(response => {
        this.pub_key = response.data;
      });
    },
    onSubmit() {
      if (this.verify){
        this.getPubKey();
        var e = this;
        var interval = setInterval(function () {
          if (e.pub_key) {
            clearInterval(interval);
            var params = {};
            params.username = e.username;
            params.token = '123';
            params.deviceId = '123';
            params.publickey = e.pub_key;
            var encrypt = new JSEncrypt();
            encrypt.setPublicKey(params.publickey);
            params.password = encrypt.encrypt(e.password);
            login(Qs.stringify(params)).then(response => {
              if (response.status === 1) {
                let expire_time = new Date().getTime();
                localStorage.setItem("expire_time", JSON.stringify(expire_time));
                localStorage.setItem("uname", e.username);
                localStorage.setItem("pw", e.password);
                localStorage.setItem('Auth',response.token)
                e.$bus.$emit('changeLoginStatus',true)
                e.$router.push('/');
              }
            })
          }
        }, 500);
      }else {
        Toast.fail('请先拖动滑块');
      }
    },
  }
}
</script>
<style scoped lang="scss">
.login {
  text-align: center;
  margin-top: 2rem;

  .submit-button {
    margin: 16px;
  }
}
::v-deep .drag_verify{
  margin: 0 auto;
}
</style>
