<template>
  <div class="list">
    <div class="bgcf">
<!--      <van-button color="#009688" block class="scanQrcode" size="normal" @click="qrCode">扫码查询</van-button>-->
<!--      <van-search-->
<!--          v-model="keyword"-->
<!--          shape="round"-->
<!--          placeholder="搜索(计划名称)"-->
<!--          @search="onSearch"-->
<!--      />-->
    </div>
<!--    &lt;!&ndash;      扫码后选择设备的弹窗&ndash;&gt;-->
<!--    <van-dialog v-model="showModal" :title="showModalTitle" show-cancel-button @confirm="confirmAssets">-->
<!--      <van-swipe class="my-swipe" autoplay="0" indicator-color="#7232DD" width="320"-->
<!--                 @change="(index)=> this.selectIndex = index">-->
<!--        <van-swipe-item v-for="item in selectAssetsList" :key="item.patrid">-->
<!--          <ul>-->
<!--            <li>-->
<!--              <span class="detailText">计划编号：</span>-->
<!--              <span class="text">{{ item.patrol_num }}</span>-->
<!--            </li>-->
<!--            <li>-->
<!--              <span class="detailText">计划名称：</span>-->
<!--              <span class="text">{{ item.patrol_name }}</span>-->
<!--            </li>-->
<!--            <li>-->
<!--              <span class="detailText">保养级别：</span>-->
<!--              <span class="text">{{ item.patrol_level_name }}</span>-->
<!--            </li>-->
<!--          </ul>-->
<!--        </van-swipe-item>-->
<!--      </van-swipe>-->
<!--    </van-dialog>-->
    <van-row>
      <van-col span="18">
        <div class="bgcf total-div">
          <div class="total-div-title">
            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
            周期计划列表 <span class="num"> {{ total }} </span> 期
          </div>
        </div>
      </van-col>
      <van-col span="6">
        <Sort @order="order" :option="option"/>
      </van-col>
    </van-row>
    <van-dialog v-model="show_complete_time" title="当前任务已逾期" show-cancel-button @confirm="batch_maintain">
      <van-field
          label-width="70px"
          v-show="show_complete_field"
          readonly
          clickable
          name="complete_time"
          v-model="yijian_params.complete_time"
          :value="complete_time"
          label="完成时间"
          placeholder="点击选择完成时间"
          @click="showTimePicker = true"
          :error-message="hint"
      />
      <van-popup v-model="showTimePicker" position="bottom">
        <van-datetime-picker
            type="datetime"
            :min-date="minDate"
            :max-date="maxDate"
            @confirm="onCompleteTimeConfirm"
            @cancel="showTimePicker = false"
        />
      </van-popup>
    </van-dialog>
    <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="refreshList"
    >
      <van-card v-for="item in list" :key="item.patrid">
        <template #desc>
          <ul>
            <li>
              <span class="detailText">计划编号：</span>
              <span class="text">{{ item.patrol_num }}</span>
              <span> <van-tag :color="item.bg_color">{{ item.operation_name }}</van-tag> </span>
            </li>
            <li>
              <span class="detailText">计划名称：</span>
              <span class="text">{{ item.patrol_name }}</span>
            </li>
            <li>
              <span class="detailText">保养级别：</span>
              <span class="text">{{ item.patrol_level_name }}</span>
            </li>
            <li>
              <span class="detailText">保养日期：</span>
              <span class="text">{{ item.cycle_start_date }} -- {{ item.cycle_end_date }}</span>
            </li>
            <li>
              <span class="detailText" style="width: 10.1rem;">已执行 / 计划设备台账：</span>
              <span class="text">{{ item.numstatus }}</span>
            </li>
            <li v-if="item.is_cycle == 1">
              <span class="detailText">计划期次：</span>
              <span class="text">第 {{ item.period }} 期</span>
            </li>
            <li>
              <span class="detailText">保养进度：</span>
              <span class="text"><van-progress :percentage="item.percent" stroke-width="8"/></span>
            </li>
          </ul>
        </template>
        <template #footer>
          <van-grid direction="horizontal" :column-num="3" icon-size="24px">
            <van-grid-item>
              <template #text>
                <div>
                  <span class="van-grid-item__text" v-html="item.abnormal_sum"></span>
                </div>
              </template>
              <template #icon>
                <van-icon color="red" name="alert" class-prefix="wx-icon"
                          style="font-size: 22px;padding-right: 2px;"/>
              </template>
            </van-grid-item>
            <van-grid-item text="一键保养" v-if="item.yijian_show" @click="yijian(item.cycid)">
              <template #icon>
                <van-icon color="#7232dd" name="yijian" class-prefix="wx-icon"
                          style="font-size: 24px;padding-right: 2px;"/>
              </template>
            </van-grid-item>
            <van-grid-item text="一键保养" v-else style="opacity: 0.5;cursor: not-allowed;">
              <template #icon>
                <van-icon color="#646566" name="yijian" class-prefix="wx-icon"
                          style="font-size: 24px;padding-right: 2px;"/>
              </template>
            </van-grid-item>
            <van-grid-item icon="more-o" text="设备明细"
                           :to="{ path: $store.getters.moduleName+'/Patrol/operation', query: {cycid: item.cycid,operation:item.operation}}"/>
          </van-grid>
        </template>
      </van-card>
    </van-list>
    <ScrollTop/>
  </div>
</template>

<script>
import Qs from 'qs'
import {
  Cell,
  CellGroup,
  List,
  Button,
  Card,
  Search,
  Col,
  Row,
  Icon,
  DropdownMenu,
  DropdownItem,
  Progress,
  Tag,
  Grid,
  GridItem,
  Notify,
  Dialog,
  Toast,
  Swipe,
  SwipeItem,
  Field,
  Popup,
  DatetimePicker,
} from "vant";
import ScrollTop from "@/components/ScrollTop";
import Sort from "@/components/Sort";
import { getTasksList } from "@/api/patrol/list";
import { getInfo } from "@/api/patrol/operation";
import { getInfo as getDoTaskInfo, setSituation } from "@/api/patrol/do-task";
import wechatUtil from "@/utils/wechatUtil";
import feishuUtil from "@/utils/feishuUtil";
import router from "@/router";
import {getRegexAssnum} from "@/utils/regex";

export default {
  name: 'getAssetsList',
  components: {
    [List.name]: List,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Button.name]: Button,
    [Card.name]: Card,
    [Search.name]: Search,
    [Col.name]: Col,
    [Row.name]: Row,
    [Icon.name]: Icon,
    [Icon.name]: Icon,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    [Progress.name]: Progress,
    [Tag.name]: Tag,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [Swipe.name]: Swipe,
    [SwipeItem.name]: SwipeItem,
    [Dialog.Component.name]: Dialog.Component,
    ScrollTop,
    Sort,
    [Field.name]: Field,
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
  },
  data() {
    return {
      list: [],
      option: [
        {text: '按保养级别（降序）', value: 'A.patrol_level-desc'},
        {text: '按保养级别（升序）', value: 'A.patrol_level-asc'},
      ],
      need_sign: false,//签到功能
      keyword: '',
      loading: false,
      finished: false,
      total: 0,
      //全局列表搜索条件
      where: {
        page: 1,
        patrid:0
      },
      //扫码后选择设备
      selectAssetsList: [],
      showModal: false,
      showModalTitle: '',
      selectIndex: 0,
      selectAssnum: '',
      show_complete_time:false,
      yijian_params:{
        action:'batch_maintain',
        cycid:0,
        assnum:'',
        complete_time:'',
      },
      complete_time:'',
      hint:'',
      minDate: new Date(),
      maxDate: new Date(),
      show_complete_field: false,
      showTimePicker: false,
    };
  },
  methods: {
    //刷新列表 第一次进入自动触发
    refreshList() {
      // 异步更新数据
      setTimeout(() => {
        this.where.patrid = this.$route.query.patrid;
        getTasksList(this.where).then(response => {
          //先判断如果返回的总数为0 则把状态设置为停止加载
          if (response.total === 0) {
            this.finished = true;
          }
          //判断数据键值是否存在
          if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
            this.list.push(...response.rows);
          }
          //仅第一页赋值总数
          if (this.where.page === 1) {
            this.total = response.total;
            this.need_sign = response.need_sign
          }
          //数据获取到了 停止加载
          this.loading = false;
          //全部加载了 且当前列表数量大于总数 设置完成状态
          if (this.list.length >= this.total) {
            this.finished = true;
          }
          //页数加1
          this.where.page++;
        });
      }, 100);
    },
    onSearch(keyword) {
      this.resetWhere();
      this.finished = false;
      //重新加搜索条件
      this.where.search = keyword;
      this.refreshList();
    },
    order(value) {
      this.resetWhere(false);
      //重新加搜索条件
      this.where.page = 1;
      this.where.sort = value.split('-')[0];
      this.where.order = value.split('-')[1];
      this.refreshList();
    },
    getcatid(id) {
      this.resetWhere();
      //重新加搜索条件
      this.where.catid = id;
      this.refreshList();
    },
    getdepartid(id) {
      this.resetWhere();
      //重新加搜索条件
      this.where.departid = id;
      this.refreshList();
    },
    //重置列表条件
    resetWhere(clearWhere = true) {
      this.list = [];//清空列表
      if (clearWhere) {
        //清空搜索条件
        this.where = {};
      }
      //重置为第一页
      this.where.page = 1;
      this.where.patrid = this.$route.query.patrid;
      //重置表格为加载状态
      this.loading = true;
      this.finished = false;
    },

    //确定提交一键保养
    batch_maintain(){
      setSituation(Qs.stringify(this.yijian_params)).then(res => {
        if (res.status === 1) {
          Toast.success({
            message: "一键保养成功",
            duration: 2000,
            onClose: () => {
              this.resetWhere();
              this.refreshList()
            }
          })
        }
      }).catch(err => {
        Toast.fail(err)
      })
    },
    yijian(cycid) {
      this.yijian_params.cycid = 0;
      this.yijian_params.assnum = '';
      this.yijian_params.complete_time = '';
      if (this.need_sign) {
        //一键保养 开启签到的情况
        getInfo({
          operation: 'get_batch_maintain_assnum',
          cycid: cycid,
        }).then(response => {
          if(response.status ==1){
            this.yijian_params.cycid = cycid;
            this.yijian_params.assnum = JSON.stringify(response.info);
            if(response.cycleInfo.is_overdue){
              //逾期的可以选择完成时间
              this.show_complete_time = true;
              this.show_complete_field = true;
              this.yijian_params.complete_time = response.cycleInfo.now_date;
              this.hint = response.cycleInfo.tips;
              this.minDate = new Date(response.cycleInfo.min_date);
              this.maxDate = new Date(response.cycleInfo.max_date);
            }else{
              this.batch_maintain();
            }
          }
        })
      } else {
        //一键保养 关闭签到的情况
        this.tips().then(() => {
          getInfo({
            operation: 'get_batch_maintain_assnum',
            cycid: cycid,
          }).then(response => {
            if(response.status == 1){
              this.yijian_params.cycid = cycid;
              this.yijian_params.assnum = JSON.stringify(response.info);
              if(response.cycleInfo.is_overdue){
                //逾期的可以选择完成时间
                this.show_complete_time = true;
                this.show_complete_field = true;
                this.yijian_params.complete_time = response.cycleInfo.now_date;
                this.hint = response.cycleInfo.tips;
                this.minDate = new Date(response.cycleInfo.min_date);
                this.maxDate = new Date(response.cycleInfo.max_date);
              }else{
                this.batch_maintain();
              }
            }
          })
        }).catch(() => {
        })
      }
    },
    //弹窗提示
    tips() {
      return new Promise((resolve, reject) => {
        Dialog.confirm({
          title: '一键保养？',
          message: '是否对计划内所有待保养设备进行一键保养',
        }).then(() => {
          return resolve();
        }).catch(() => {
          // on cancel
          return reject();
        });
      })
    },
    //扫码后弹窗确认的设备
    confirmAssets() {
      const currentInfo = this.selectAssetsList[this.selectIndex];
      getInfo({
        operation: currentInfo.operation,
        patrid: currentInfo.patrid,
      }).then(response => {
        const data = response.assets;
        let asInfo = data.filter(v => v.assnum === this.selectAssnum);
        if (asInfo[0]['need_sign']) {
          Notify({type: 'danger', message: '您所查看的设备需要签到'});
          return false;
        }
        // if (asInfo[0].hasOwnProperty("disabled")) {
        //     Notify({type: 'danger', message: '您无权限查看该设备'});
        //     return false;
        // }
        this.$router.push({
          path: asInfo[0].actionurl,
          query: {
            assnum: asInfo[0].assnum,
            operation: asInfo[0].operation,
            patrol_level: asInfo[0].patrol_level,
            cycid: asInfo[0].cycid
          }
        });
      });
    },
    // 扫码
    async qrCode() {
      // console.log(this.selectAssetsList)
      //alert(location.href.split('#')[0]);
      var _this = this;
      switch (parseInt(process.env.VUE_APP_VERSION)) {
        case 1:
          // 微信版本
          wechatUtil.init([
            'scanQRCode',//扫一扫
          ]).then((wx) => {
            // 这里写微信的接口
            wx.scanQRCode({
              needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
              scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
              async success(res) {
                // var assnum = res.resultStr;// 当needResult 为 1 时，扫码返回的结果
                // if (assnum.indexOf("ODE_") > 0) {
                //   assnum = res.resultStr.substr(9);
                // }
                const assnum = await getRegexAssnum(res.resultStr)

                //鉴别设备是否存在
                let currentInfo = _this.list.filter(v => v.patrol_assnums.indexOf(assnum) !== -1);
                if (currentInfo.length === 0) {
                  Notify({type: 'danger', message: '未识别该扫码设备，请重试'});
                  return false;
                }
                /*弹窗赋值 S*/
                _this.selectAssetsList = currentInfo;
                _this.showModal = true;
                _this.showModalTitle = '该设备包含在' + currentInfo.length + '个计划内，请选择';
                _this.selectAssnum = assnum
                /*弹窗赋值 E*/
              }
            });
          })
          break;
        case 2:
          // 飞书版本
          await feishuUtil.init(['scanCode']);
          window.h5sdk.ready(() => {
            window.tt.scanCode({
              success(res) {
                // alert(JSON.stringify(`${res.result}`));
                //鉴别设备是否存在
                let currentInfo = _this.list.filter(v => v.patrol_assnums.indexOf(res.result) !== -1);
                if (currentInfo.length === 0) {
                  Notify({type: 'danger', message: '未识别该扫码设备，请重试'});
                  return false;
                }
                /*弹窗赋值 S*/
                _this.selectAssetsList = currentInfo;
                _this.showModal = true;
                _this.showModalTitle = '该设备包含在' + currentInfo.length + '个计划内，请选择';
                _this.selectAssnum = res.result
                /*弹窗赋值 E*/
              },
              fail(res) {
                alert(`调用失败`);
              }
            });
          });
          break;
      }
    },
    onCompleteTimeConfirm(datetime){
      let complete_time = this.timeFormat(datetime)
      this.yijian_params.complete_time = complete_time;
      this.showTimePicker = false;
    },
    timeFormat(time) { // 时间格式化
      let year = time.getFullYear();
      let month = time.getMonth() + 1;
      month = month < 10 ? '0'+month : month;
      let day = time.getDate();
      day = day < 10 ? '0'+day : day;
      let hour = time.getHours();
      hour = hour < 10 ? '0'+hour : hour;
      let minute = time.getMinutes();
      minute = minute < 10 ? '0'+minute : minute;
      return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':00';
    },
  },
}
</script>

<style scoped lang="scss">
.list {
  margin-top: 1rem;

  .van-list .van-card {
    li {
      display: flex;
    }

    .detailText {
      width: 5rem;
    }

    .text {
      flex: 1;
    }
  }

  .van-search {
    margin-bottom: 0;
  }

  .van-progress {
    margin-top: 5px;
  }
}

.my-swipe {
  .van-swipe-item ul {
    margin: 30px;
  }

  ::v-deep .van-swipe__indicator {
    background-color: #1989fa;
  }
}
</style>
<style>
.van-grid-item__content {
  padding: 6px;
}
</style>
