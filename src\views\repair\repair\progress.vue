<template>
    <div class="list">
        <van-search
                v-model="keyword"
                shape="round"
                placeholder="搜索（设备名称、编号、科室）"
                @search="onSearch"
        />
        <van-row>
            <van-col span="18">
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        维修进程 <span class="num"> {{total}} </span> 台
                    </div>
                </div>
            </van-col>
            <van-col span="6">
                <van-dropdown-menu class="filter">
                    <van-dropdown-item title="全部" v-model="orderValue" :options="option" @change="status"/>
                 </van-dropdown-menu>
            </van-col>
        </van-row>

        <van-popup v-model="showReturnDate" position="bottom">
            <van-datetime-picker v-model="currentDate" type="date" title="选择年月日" @confirm="selectReturnDate" @cancel="showReturnDate = false"/>
        </van-popup>

        <van-popup v-model="showCompanyList" position="bottom">
            <van-cell-group>
                <van-field v-model="searchCompanyName" label="搜索公司" placeholder="请输入公司名" @input="searchCompany"/>
            </van-cell-group>
            <van-tree-select :items="companyFilter" :active-id.sync="selectCompany" :main-active-index.sync="companyIndex" @click-item="getCompany"/>
        </van-popup>


        <van-row>
            <van-col span="12" @click="showSelectReturnDate">
                <div class="category cate bgcf">
                    <van-icon class-prefix="wx-icon" name="shebeifenlei" size="36px" color="#FF5722"/>
                    <p class="category-title">返还日期</p>
                </div>
            </van-col>
            <van-col span="12" @click="selectCompanyList">
                <div class="category cate bgcf">
                    <van-icon class-prefix="wx-icon" name="keshiguanli" size="36px" color="#5fb878"/>
                    <p class="category-title">选择公司</p>
                </div>
            </van-col>
        </van-row>

        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="refreshList">
            <van-card :thumb="item.pic_url" v-for="item in list" :key="item.assid"  @click="to(item.repid)">
                <template #title>
                    <van-cell>
                        <!-- 使用 title 插槽来自定义标题 -->
                        <template #title>
                            <span class="custom-title">维修单号：{{item.repnum}}</span>
                        </template>
                        <template #right-icon>
                            <van-tag :color="item.color">{{item.status_name}}</van-tag>
                        </template>
                    </van-cell>
                </template>
                <template #desc>
                    <div class="atn">
                        <span class="ct">设备名称：</span>
                        <span class="cc">{{item.assets}}</span>
                    </div>
                    <div class="atn">
                        <span class="ct">设备编号：</span>
                        <span class="cc">{{item.assnum}}</span>
                    </div>
                    <div class="atn">
                        <span class="ct">所属科室：</span>
                        <span class="cc">{{item.department}}</span>
                    </div>
                </template>
                <template #footer>
                    <div class="gt">
                        <van-row>
                            <van-col span="10">报修人：{{item.appUser}}</van-col>
                            <van-col span="14">报修时间：{{item.applicant_time}}</van-col>
                        </van-row>
                        <div style="background: #B0B9BD;height: 1px;"></div>
                        <van-row>
                            <van-col span="14">公司名称：{{item.offer_company}}</van-col>
                        </van-row>
                        <van-row>
                            <van-col span="10">联系人：{{item.offer_contacts}}</van-col>
                            <van-col span="10">联系电话：{{item.telphone}}</van-col>
                        </van-row>
                        <van-row>
                            <van-col span="10">送修时间：{{item.send_date}}</van-col>
                            <van-col span="10">返还时间：{{item.return_date}}</van-col>
                        </van-row>
                    </div>
                </template>
            </van-card>
        </van-list>
        <ScrollTop/>
    </div>
</template>

<script>
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem,Tag,Image,DatetimePicker,Popup,TreeSelect,Field  } from "vant";
    import {getProgressList} from "@/api/repair/list";
    import ScrollTop from "@/components/ScrollTop";

    export default {
        name: 'getProgressList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            [Tag.name]: Tag,
            [Image.name]: Image,
            [DatetimePicker.name]: DatetimePicker,
            [Popup.name]:Popup,
            [TreeSelect.name]:TreeSelect,
            [Field.name]:Field,
            ScrollTop,
        },
        data() {
            return {
                list: [],
                orderValue: '',
                option: [
                    {text: '全部', value: ''},
                    {text: '待接单', value: '1'},
                    {text: '待检修', value: '2'},
                    {text: '待出库', value: '3'},
                    {text: '待审批', value: '5'},
                    {text: '继续维修', value: '6'},
                    {text: '待验收', value: '7'},
                    {text: '已结束', value: '8'},
                ],
                keyword: '',
                loading: false,
                finished: false,
                total: 0,
                //全局列表搜索条件
                where: {
                    page: 1,
                },
                showReturnDate:false,
                currentDate: new Date(),
                ReturnDate:'',
                company:[],
                selectCompany:[],
                showCompany:[],
                searchCompanyName:'',
                companyIndex:0,
                showCompanyList:false,
                companyFilter:[
                    {
                    text: '',
                    children: [],
                    dot: true,
                    badge: 0
                    }
                ],
            };
        },
        computed: {
            selectCompanyNum(){
                let length = this.selectCompany.length
                if(length == 0){
                    length = null
                }
                return length
            }
        },
        methods: {
            searchCompany(searchCompanyName){
                this.companyFilter[0].children = this.company.filter(item=>item.text.indexOf(searchCompanyName) > -1)
            },
            showSelectReturnDate(){
                this.showReturnDate = true;
            },
            selectCompanyList(){
                this.showCompanyList = true;
            },
            getCompanyList(){
                if(this.company == []){
                    this.getOfferId([])

                }
            },
            timeFormat(time) { // 时间格式化
                let year = time.getFullYear();
                let month = time.getMonth() + 1;
                let day = time.getDate();
                return year + '-' + month + '-' + day;
            },
            selectReturnDate(time){
                this.ReturnDate = this.timeFormat(time);
                this.showReturnDate = false;
                this.getReturnDate(this.ReturnDate)
            },
            getCompany(){
                console.log(this.selectCompany)
                this.getOfferId(this.selectCompany)
            },
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                getProgressList(this.where).then(response => {
                    for (let x in response.rows) {
                        if (Object.prototype.hasOwnProperty.call(response.rows, x)) {
                            this.list.push(response.rows[x]);
                        }
                    }
                    this.tips = response.tips;
                    //仅第一页赋值总数
                    if (this.where.page === 1) {
                        this.total = response.total;
                    }
                    //数据获取到了 停止加载
                    this.loading = false;
                    this.where.page = this.where.page + 1;

                    this.company = response.repair_offer_company;
                    this.companyFilter[0].children = response.repair_offer_company;
                    //全部加载了 后台已经没有数据可反 就置为完成状态
                    if (this.list.length >= this.total) {
                        this.finished = true;
                    }
                })
            },
            to(id){
               this.$router.push(this.$store.getters.moduleName+'/Repair/showRepairDetails?repid='+id);
            },
            onSearch(keyword) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, search: keyword};
                this.refreshList();
            },
            status(value) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, status:value};
                this.refreshList();
            },
            getcatid(id) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, catid: id};
                this.refreshList();
            },
            getdepartid(id) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, departid: id};
                this.refreshList();
            },
            getReturnDate(date) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, return_date: date};
                this.refreshList();
            },
            getOfferId(offerIds) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, offid: offerIds};
                this.refreshList();
            },
        }
    }
</script>

<style scoped lang="scss">
    .custom-title{
        font-size: 18px;
    }
    .van-card{
        padding: 8px 0;
    }
    .van-cell {
        padding: 10px 0;
    }
    .van-card__header{padding: 8px 16px;}
    .van-card__footer{background:#FAFAFA;padding-left: 16px;}
    .atn{margin-top: 5px;}
    .ct{font-size: 14px;}
    .cc{font-size: 14px;color:#B0B9BD;}
    .gt{font-size: 14px;color:#B0B9BD;overflow: hidden;}
    .van-card__footer{
        text-align: left;
    }
    .search-image{
        height: 50px;
    }
    .cate {
        border-right: 1px solid #c9c9c9
    }
        .category {
        padding-left: 10px;

        .wx-icon {
            float: left;
            position: relative;
            top: 8px;
            margin: 0 10px;
        }

        .category-title {
            line-height: 60px;
            font-size: 24px;
            color: #333333;
            margin: 0;
        }

        .category-text {
            font-size: 12px;
            margin: 5px;
            color: #707070;
        }
    }
</style>
