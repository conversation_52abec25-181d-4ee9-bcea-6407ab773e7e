<template>
    <div class="list" style="margin-top: 20px;">
        <van-search
                v-model="keyword"
                shape="round"
                placeholder="搜索（设备名称、编号、科室）"
                @search="onSearch"
        />
        <van-row>
            <van-col span="18">
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        待出库配件 <span class="num"> {{total}} </span> 单
                    </div>
                </div>
            </van-col>
            <van-col span="6">
                <Sort @order="order" :option="option"/>
            </van-col>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="refreshList"
        >
            <van-card :thumb="item.pic_url" v-for="item in list" :key="item.assid">
                <template #desc>
                    <div class="jumpButton">
                        <van-button color="#009688" size="small" :to="{ path: $store.getters.moduleName+'/RepairParts/partsOutWare', query: { repid: item.repid }}">处理</van-button>
                    </div>
                    <van-row>
                        <van-col span="6">
                            <van-grid column-num="1" center :border="false" icon-size="3.2rem">
                                <van-grid-item :icon="item.headimgurl" :text="item.appUser" />
                            </van-grid>
                        </van-col>
                        <van-col span="18">
                            <van-cell :border="false" :value="item.repnum" />
                            <van-cell :border="false" :value="item.assets" />
                            <van-cell :border="false" :value="item.department" />
                            <van-cell :border="false" :title="item.parts_num_html" :value="item.show_time" />
                        </van-col>
                    </van-row>
                </template>
            </van-card>
        </van-list>
        <ScrollTop/>
    </div>
</template>

<script>
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem,Grid,GridItem} from "vant";
    import {partsOutWareList} from "@/api/repair/list";
    import ScrollTop from "@/components/ScrollTop";
    import Sort from "@/components/Sort";

    export default {
        name: 'partsOutWareList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            [Grid.name]: Grid,
            [GridItem.name]: GridItem,
            ScrollTop,
            Sort
        },
        data() {
            return {
                list: [],
                option: [
                    {text: '按配件数量（降序）', value: 'part_num-desc'},
                    {text: '按检修时间（降序）', value: 'overhauldate-desc'},
                    {text: '按配件数量（升序）', value: 'part_num-asc'},
                    {text: '按检修时间（升序）', value: 'overhauldate-asc'},
                ],
                keyword: '',
                orderValue: '',
                loading: false,
                finished: false,
                total: 0,
                //全局列表搜索条件
                where: {
                    page: 1,
                    action:'overhaulLists',
                }
            };
        },
        methods: {
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                partsOutWareList(this.where).then(response => {
                    for (let x in response.rows) {
                        if (Object.prototype.hasOwnProperty.call(response.rows, x)) {
                            this.list.push(response.rows[x]);
                        }
                    }
                    if (response.total!=0) {
                        this.total = response.total;
                    }
                    //数据获取到了 停止加载
                    this.loading = false;
                    this.where.page = this.where.page + 1;
                    //全部加载了 后台已经没有数据可反 就置为完成状态
                    if (this.list.length >= this.total) {
                        this.finished = true;
                    }
                });
            },
            onSearch(keyword) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, search: keyword};
                this.refreshList();
            },
            order(value) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, sort: value.split('-')[0], order: value.split('-')[1]};
                this.refreshList();
            },
            getcatid(id) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, catid: id};
                this.refreshList();
            },
            getdepartid(id) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, departid: id};
                this.refreshList();
            }
        }
    }
</script>

<style scoped lang="scss">
    .van-cell {
        padding: 5px 2px;
        color: #555;
        font-size: 1rem;
    }
    .list .van-list .van-card .jumpButton {
        bottom: 4rem;
    }
    .van-grid{
        border-right: 1px solid #c9c9c9;
        margin-right: 10px;
        padding-top: 25px;
    }
</style>
