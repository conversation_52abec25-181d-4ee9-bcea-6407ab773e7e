<template>
    <div class="tableInfo">
        <table class="cus_table">
            <thead>
            <tr>
                <th v-for="(item,index) in table.headerData" :key="index" :style="{width:`${item.width}%`}">{{item.text}}</th>
            </tr>
            </thead>
            <tbody v-if="table.bodyData.length>0">
            <tr v-for="(item,index) in table.bodyData" :key="index">
                <td v-for="(head,i) in table.headerData" :key="i">{{item[head.field]}}</td>
            </tr>
            </tbody>
            <tbody v-else>
                <tr>
                    <td :colspan="table.headerData.length">{{table.noData}}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
    export default {
        name: "Table",
        props: ['table'],
    }
</script>

<style scoped>
    .tableInfo{
        background: #fff;
        padding: 1rem 0;
    }
    .cus_table {
        width: 94%;
        background-color: #fff;
        color: #666;
        margin: 0 auto;
    }
    table {
        border-collapse: collapse;
        border-spacing: 0;
    }
    thead {
        display: table-header-group;
        vertical-align: middle;
        border-color: inherit;
    }
    .cus_table tbody tr:hover, .cus_table thead tr {
        background-color: #f2f2f2;
    }
    .cus_table th {
        position: relative;
        padding: 9px 15px;
        min-height: 20px;
        line-height: 20px;
        font-size: 14px;
    }
    tbody {
        display: table-row-group;
        vertical-align: middle;
        border-color: inherit;
    }
    .cus_table td, .cus_table th {
        position: relative;
        padding: 9px 15px;
        min-height: 20px;
        line-height: 20px;
        font-size: 14px;
        border: 1px solid #e6e6e6;
        text-align: center;
    }
</style>
