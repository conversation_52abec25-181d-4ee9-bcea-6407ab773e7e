define({
    ro: {
        'Allowed values:'             : '<PERSON><PERSON> permise:',
        'Compare all with predecessor': 'Compar<PERSON> toate cu versiunea precedentă',
        'compare changes to:'         : 'compară cu versiunea:',
        'compared to'                 : 'comparat cu',
        'Default value:'              : '<PERSON>oar<PERSON> implicită:',
        'Description'                 : '<PERSON><PERSON><PERSON><PERSON>',
        'Field'                       : 'Câmp',
        'General'                     : 'General',
        'Generated with'              : 'Generat cu',
        'Name'                        : 'Nume',
        'No response values.'         : 'Nici o valoare returnată.',
        'optional'                    : 'opțional',
        'Parameter'                   : 'Parametru',
        'Permission:'                 : 'Permisiune:',
        'Response'                    : 'Răspuns',
        'Send'                        : 'Trimite',
        'Send a Sample Request'       : 'Trimite o cerere de probă',
        'show up to version:'         : 'arată până la versiunea:',
        'Size range:'                 : 'Interval permis:',
        'Type'                        : 'Tip',
        'url'                         : 'url'
    }
});
