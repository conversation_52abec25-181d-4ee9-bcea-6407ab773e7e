<template>
    <div class="list">
        <div class="bgcf" style="padding: 20px 0 0;">
            <van-search
                    v-model="keyword"
                    shape="round"
                    placeholder="搜索（设备名称、编号、科室）"
                    @search="onSearch"
            />
        </div>
        <van-row>
            <van-col span="18">
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        待处理设备 <span class="num"> {{total}} </span> 台
                    </div>
                </div>
            </van-col>
            <van-col span="6">
                <Sort @order="order" :option="option"/>
            </van-col>
        </van-row>
        <van-row>
            <div style="text-align: center;">
                <span v-html="tips"/>
            </div>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="refreshList"
        >
            <van-card :thumb="item.pic_url" v-for="item in list" :key="item.assid" @click="to_url(item.repid,item.to)">
                <template #title>
                    <van-cell>
                        <!-- 使用 title 插槽来自定义标题 -->
                        <template #title>
                            <span class="custom-title">维修单号：{{item.repnum}}</span>
                        </template>
                        <template #right-icon>
                            <van-tag :color="item.color">{{item.status_name}}</van-tag>
                        </template>
                    </van-cell>
                </template>
                <template #desc>
                    <div class="atn">
                        <span class="ct">设备名称：</span>
                        <span class="cc">{{item.assets}}</span>
                    </div>
                    <div class="atn">
                        <span class="ct">设备编号：</span>
                        <span class="cc">{{item.assnum}}</span>
                    </div>
                    <div class="atn">
                        <span class="ct">所属科室：</span>
                        <span class="cc">{{item.department}}</span>
                    </div>
                    <div class="atn">
                        <span class="ct">维修性质：</span>
                        <span class="cc" v-html="item.repair_type_name"></span>
                    </div>
                </template>
                <template #footer>
                    <div class="gt">
                        <van-row>
                            <van-col span="10">接单人：{{item.response}}</van-col>
                            <van-col span="14">接单时间：{{item.response_date}}</van-col>
                        </van-row>
                    </div>
                </template>
            </van-card>
        </van-list>
        <ScrollTop/>
    </div>
</template>

<script>
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem,Tag} from "vant";
    import {getRepairList} from "@/api/repair/list";
    import ScrollTop from "@/components/ScrollTop";
    import Sort from "@/components/Sort";

    export default {
        name: 'getRepairList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            [Tag.name]: Tag,
            ScrollTop,
            Sort
        },
        data() {
            return {
                list: [],
                option: [
                    {text: '按报修时间（降序）', value: 'applicant_time-desc'},
                    {text: '按维修状态（降序）', value: 'status-desc'},
                    {text: '按报修时间（升序）', value: 'applicant_time-asc'},
                    {text: '按维修状态（升序）', value: 'status-asc'},
                ],
                tips: [],
                keyword: '',
                orderValue: '',
                loading: false,
                finished: false,
                total: 0,
                //全局列表搜索条件
                where: {
                    page: 1,
                }
            };
        },
        methods: {
            to_url(repid,to){
                if(to){
                    this.$router.push({
                        name:'startRepair',
                        query:{
                            repid:repid
                        }
                    });
                }
            },
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                getRepairList(this.where).then(response => {
                    this.tips = response.tips;
                    //全部加载了 后台已经没有数据可反 就置为完成状态
                    if (response.data === [] || response.total === 0) {
                        this.finished = true;
                    }

                    if (response.total === 0) {
                        this.finished = true;
                    }
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    if (this.where.page === 1) {
                        this.total = response.total;
                    }
                    //数据获取到了 停止加载
                    this.loading = false;
                    //全部加载了 且当前列表数量大于总数 设置完成状态
                    if (this.list.length >= this.total) {
                        this.finished = true;
                    }
                    //页数加1
                    this.where.page++;
                });
            },
            onSearch(keyword) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, search: keyword};
                this.refreshList();
            },
            order(value) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, sort: value.split('-')[0], order: value.split('-')[1]};
                this.refreshList();
            },
            getcatid(id) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, catid: id};
                this.refreshList();
            },
            getdepartid(id) {
                this.list = [];
                //清空搜索条件
                this.where = {};
                //重新加搜索条件
                this.where = {page: 1, departid: id};
                this.refreshList();
            }
        }
    }
</script>

<style scoped lang="scss">
    .custom-title{
        font-size: 18px;
    }
    .van-card{
        padding: 8px 0;
    }
    .van-cell {
        padding: 10px 0;
    }
    .van-card__header{padding: 8px 16px;}
    .van-card__footer{background:#FAFAFA;padding-left: 16px;}
    .atn{margin-top: 5px;}
    .ct{font-size: 14px;}
    .cc{font-size: 14px;color:#B0B9BD;}
    .gt{height: 45px;line-height: 45px;font-size: 14px;color:#B0B9BD;overflow: hidden;}
    .van-card__footer{
        text-align: left;
    }
</style>
