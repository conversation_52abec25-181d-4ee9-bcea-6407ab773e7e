<template>
    <div :style="conTop" class="Approves" v-if="info.length > 0">
        <div>
            <div class="demo-preview" v-for="(item,index) in info" :key="index">
                <van-image
                        round
                        width="3rem"
                        height="3rem"
                        :src="item.user_pic"
                />
                <div class="demo-content">
                    <div class="name_time" v-if="item.is_adopt == 1">
                        <van-cell :icon="require('@/assets/images/pass.png')" :title="item.approver" :value="item.approve_time"/>
                    </div>
                    <div class="name_time" v-else-if="item.is_adopt == 2">
                        <van-cell :icon="require('@/assets/images/not_pass.png')" :title="item.approver" :value="item.approve_time"/>
                    </div>
                    <div class="name_time" v-else>
                        <van-cell :icon="require('@/assets/images/wating.png')" :title="item.approver" :value="item.approve_time"/>
                    </div>
                    <p v-html="item.remark"></p>
                </div>
            </div>
        </div>
    </div>
    <div class="Approves" style="text-align: center;" v-else>
        暂无审批信息（不用审批）
    </div>
</template>
<script>
    import {Icon ,Image as VanImage,Skeleton,Divider, Swipe, SwipeItem, Cell, CellGroup, Tab, Tabs, Step, Steps,Overlay} from 'vant';
    export default {
        name: 'Approves',
        components: {
            [Divider.name]: Divider,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Step.name]: Step,
            [Steps.name]: Steps,
            [Steps.Overlay]: Overlay,
            [Skeleton.name]: Skeleton ,
            [VanImage.name]: VanImage ,
            [Icon .name]: Icon  ,
        },
        data() {
            return {
                position: 0,
                loading: true,
                pass_pic: require('@/assets/images/pass.png'),
                not_pass_pic: require('@/assets/images/not_pass.png'),
                icon_img: '',
                conTop: {
                    backgroundImage:'url(' + require('@/assets/images/bg.png') + ')',
                    backgroundRepeat:'repeat-y',
                    backgroundPosition:'40px',
                    backgroundSize:'3px',
                }
            }
        },
        props: ['info'],
        mounted() {
            this.loading = false;
        },

    }
</script>

<style scoped lang="scss">
    .Approves{
        padding: 1rem 0;
        background: #fff;
    }
    .van-cell{
        padding: 6px 6px;
    }
    .van-cell__left-icon, .van-cell__right-icon {
        font-size: 20px;
    }
    .demo-preview {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        padding: 0 16px;
        margin: 10px 0 20px;
    }
    .van-image {
        -webkit-flex-shrink: 0;
        flex-shrink: 0;
        margin-right: 16px;
    }
    .demo-preview .demo-content {
        width: 100%;
        border: 1px #969799 dashed;
        border-radius: 6px;
    }
    .demo-preview .demo-content .name_time {
        margin: 0;
        font-size: 18px;
        line-height: 20px;
    }
    .demo-preview .demo-content p {
        margin: 0 0 10px 10px;
        font-size: 14px;
        line-height: 20px;
        padding-left: 20px;
        color:#969799;
        background: #fff;
    }
    .demo-preview{
        .van-cell__title, .van-cell__value {
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            flex: 0 1 auto;
        }
        .van-cell__title{
            width: 48%;
        }
        .van-cell__value{
            min-width:112px;
        }
    }
</style>
