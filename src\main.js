import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import './assets/fonts/wx-icon/iconfont.css'//引入阿里巴巴矢量图标额外图标库
import './style/tecev.scss'//全局样式
require('./mock/mock');//引入mock
import './permission'

Vue.config.productionTip = false;
import VConsole from 'vconsole';
// const vConsole = new VConsole();
new Vue({
    router,
    store,
    render: h => h(App),
    beforeCreate(){
        Vue.prototype.$bus = this;
    }
}).$mount('#app');
