<template>
  <div class="list">
    <div class="bgcf">
      <van-button color="#009688" block class="scanQrcode" size="normal" @click="qrCode" v-if="jugdePlatform()">扫码申请</van-button>
      <van-search
          v-model="keyword"
          shape="round"
          placeholder="搜索(设备名称、编号、规格型号、科室)"
          @search="onSearch"
      />
      <Category @catid="getcatid" @departid="getdepartid" type="borrow"/>
    </div>
    <van-row>
      <van-col span="18">
        <div class="bgcf total-div">
          <div class="total-div-title">
            <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
            借调申请列表 <span class="num"> {{ total }} </span> 台
          </div>
        </div>
      </van-col>
      <van-col span="6">
        <Sort @order="order" :option="option"/>
      </van-col>
    </van-row>
    <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="refreshList"
    >
      <van-card :thumb="item.pic_url" v-for="item in list" :key="item.assid">
        <template #desc>
          <div class="jumpButton">
            <van-button v-if="item.is_borrowing == 1" type="info" size="small" disabled>借调中</van-button>
            <van-button v-else-if="item.retrial_status==1" type="warning" size="small"
                        :to="{ path: $store.getters.moduleName+'/Borrow/applyBorrow', query: { assid: item.assid,borid:item.borid }}">重新申请
            </van-button>
            <van-button v-else type="info" size="small"
                        :to="{ path: $store.getters.moduleName+'/Borrow/applyBorrow', query: { assid: item.assid }}">申请借调
            </van-button>
          </div>
          <ul>
            <li class="list_li_width">
              <span class="detailText">设备名称：</span>
              <span class="text">{{ item.assets }}</span>
            </li>
            <li>
              <span class="detailText">设备编号：</span>
              <span class="text">{{ item.assnum }}</span>
            </li>
            <li class="list_li_width">
              <span class="detailText">规格型号：</span>
              <span class="text">{{ item.model }}</span>
            </li>
            <!--                        <li>-->
            <!--                            <span class="detailText">当前状态：</span>-->
            <!--                            <span v-html="item.status_name"/>-->
            <!--                        </li>-->
            <li>
              <span class="detailText">开机状态：</span>
              <span v-html="item.startup_name"/>
            </li>
            <li class="list_li_width">
              <span class="detailText">使用科室：</span>
              <span class="text">{{ item.department }}</span>
            </li>
          </ul>
        </template>
      </van-card>
    </van-list>
    <ScrollTop/>
  </div>
</template>

<script>
import { Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem } from "vant";
import { getBorrowAssetsList } from "@/api/assets/borrow/list";
import Category from "@/components/Category";
import ScrollTop from "@/components/ScrollTop";
import Sort from "@/components/Sort";
import wechatUtil from '@/utils/wechatUtil';
import feishuUtil from "@/utils/feishuUtil";
import {getRegexAssnum} from "@/utils/regex";

export default {
  name: 'getApplyList',
  components: {
    [List.name]: List,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Button.name]: Button,
    [Card.name]: Card,
    [Search.name]: Search,
    [Col.name]: Col,
    [Row.name]: Row,
    [Icon.name]: Icon,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    Category,
    ScrollTop,
    Sort
  },
  data() {
    return {
      list: [],
      option: [
        {text: '按科室名称（降序）', value: 'department-desc'},
        {text: '按设备启用时间（降序）', value: 'opendate-desc'},
        {text: '按科室名称（升序）', value: 'department-asc'},
        {text: '按设备启用时间（升序）', value: 'opendate-asc'},
      ],
      keyword: '',
      loading: false,
      finished: false,
      total: 0,
      //全局列表搜索条件
      where: {
        page: 1
      }
    };
  },
  methods: {
    jugdePlatform(){
      return (process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX)
    },
    async qrCode() {
      //alert(location.href.split('#')[0]);
      var _this = this;
      switch (parseInt(process.env.VUE_APP_VERSION)) {
        case 1:
          // 微信版本
          wechatUtil.init([
            'scanQRCode',//扫一扫
          ]).then((wx) => {
            // 这里写微信的接口
            wx.scanQRCode({
              needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
              scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
              async success(res) {
                // var assnum = res.resultStr;// 当needResult 为 1 时，扫码返回的结果
                // if (assnum.indexOf("ODE_") > 0) {
                //   assnum = res.resultStr.substr(9);
                // }
                const assnum = await getRegexAssnum(res.resultStr)

                _this.$router.push({
                  name: 'applyBorrow',
                  query: {
                    assnum: assnum,
                    action: 'brcode'
                  }
                });
              }
            });
          })
          break;
        case 2:
          // 飞书版本
          await feishuUtil.init(['scanCode']);
          window.h5sdk.ready(() => {
            window.tt.scanCode({
              success(res) {
                // alert(JSON.stringify(`${res.result}`));
                _this.$router.push({
                  name: 'applyBorrow',
                  query: {
                    assnum: res.result,
                    action: 'brcode'
                  }
                });
              },
              fail(res) {
                alert(`调用失败`);
              }
            });
          });
          break;
      }
    },
    //刷新列表 第一次进入自动触发
    refreshList() {
      // 异步更新数据
      setTimeout(() => {
        getBorrowAssetsList(this.where).then(response => {
          //先判断如果返回的总数为0 则把状态设置为停止加载
          if (response.total === 0) {
            this.finished = true;
          }
          //判断数据键值是否存在
          if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
            this.list.push(...response.rows);
          }
          //仅第一页赋值总数
          if (this.where.page === 1) {
            this.total = response.total;
          }
          //数据获取到了 停止加载
          this.loading = false;
          //全部加载了 且当前列表数量大于总数 设置完成状态
          if (this.list.length >= this.total) {
            this.finished = true;
          }
          //页数加1
          this.where.page++;
        })
      }, 100);
    },
    onSearch(keyword) {
      this.resetWhere();
      this.finished = false;
      //重新加搜索条件
      this.where.search = keyword;
      this.refreshList();
    },
    order(value) {
      this.resetWhere(false);
      //重新加搜索条件
      this.where.page = 1;
      this.where.sort = value.split('-')[0];
      this.where.order = value.split('-')[1];
      this.refreshList();
    },
    getcatid(id) {
      this.resetWhere();
      //重新加搜索条件
      this.where.catid = id;
      this.refreshList();
    },
    getdepartid(id) {
      this.resetWhere();
      //重新加搜索条件
      this.where.departid = id;
      this.refreshList();
    },
    //重置列表条件
    resetWhere(clearWhere = true) {
      this.list = [];//清空列表
      if (clearWhere) {
        //清空搜索条件
        this.where = {};
      }
      //重置为第一页
      this.where.page = 1;
      //重置表格为加载状态
      this.loading = true;
      this.finished = false;
    }
  }
}
</script>

<style scoped lang="scss">
.jumpButton {
  bottom: 2.1rem !important;
}
</style>
<style>

</style>
