<template>
    <div class="sort">
        <span @click="showList = !showList">排序</span>
        <van-icon class-prefix="wx-icon" name="sort" size="24px" @click="showList = !showList"/>
        <transition name="van-slide-right">
            <div class="sort-list" v-show="showList">
                <dl>
                    <dt>降序</dt>
                    <dd v-for="item in orderList">
                        <span class="orderTypeButton" @click="order('DESC',item.field)">{{item.title}}</span>
                    </dd>
                    <dt>升序</dt>
                    <dd v-for="item in orderList">
                        <span class="orderTypeButton" @click="order('ASC',item.field)">{{item.title}}</span>
                    </dd>
                </dl>
            </div>
        </transition>
    </div>
</template>

<script>
    import {Icon} from "vant";

    export default {
        name: "NewSort",
        data() {
            return {
                showList: false,
                orderList: [
                    {id: Math.ceil(Math.random() * 100), title: '按科室名称', field: 'department'},
                    {id: Math.ceil(Math.random() * 100), title: '按科室设备数量', field: 'assetssum'},
                    {id: Math.ceil(Math.random() * 100), title: '按设备状态', field: 'status'},
                    {id: Math.ceil(Math.random() * 100), title: '按设备启用时间', field: 'opendate'},
                ]
            }
        },
        components: {
            [Icon.name]: Icon
        },
        methods: {
            order: function (type, value) {
                console.log(type, value)
                // this.$emit("order", value);
            },
        }
    }
</script>

<style scoped lang="scss">
    .sort {
        position: absolute;
        right: 10px;
        top: 6px;

        .wx-icon {
            top: 3px;
            position: relative;
        }

        .sort-list {
            background: #000;
            width: 200px;
            opacity: 0.8;
            color: #fff;
            z-index: 4;
            position: absolute;
            top: 42px;
            right: -10px;

            dl {
                margin: 0;
            }

            dl dt {
                margin-bottom: 0;
                height: 2rem;
                line-height: 2rem;
                padding-left: 10px;
            }

            dl dd {
                margin-bottom: 10px;
                height: 32px;
                line-height: 32px;
                border-bottom: 1px dashed #fff;
            }
        }
    }
</style>
