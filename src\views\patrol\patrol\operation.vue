<template>
    <div class="con_detail" v-if="page.is_display">
        <div class="card-header">
            <h2 class="detailTitle">计划基本信息</h2>
            <div class="bl1px"></div>
        </div>
        <van-cell-group>
            <van-cell value-class="vc" title="计划状态" :value="info.cycleInfo.cycle_status_name"/>
            <van-cell title="计划编号" :value="info.cycleInfo.patrol_num"/>
            <van-cell title="计划开始日期" :value="info.cycleInfo.cycle_start_date"/>
            <van-cell title="计划结束日期" :value="info.cycleInfo.cycle_end_date"/>
        </van-cell-group>

        <div class="card-header" style="margin-top: 20px;">
            <h2 class="detailTitle">本计划保养设备列表</h2>
            <div class="bl1px"></div>
        </div>
        <van-card style="margin-top: 0.5rem;" v-for="item in info.assInfo" :key="item.assid">
            <template #desc>
                <ul>
                    <li>
                        <span class="detailText">设备编号：</span>
                        <span class="text">{{item.assnum}}</span>
                        <span> <van-tag :color="item.bg_color">{{item.operation_name}}</van-tag> </span>
                    </li>
                    <li>
                        <span class="detailText">设备名称：</span>
                        <span class="text">{{item.assets}}</span>
                    </li>
                    <li>
                        <span class="detailText">规格型号：</span>
                        <span class="text">{{item.model}}</span>
                    </li>
                    <li>
                        <span class="detailText">使用科室：</span>
                        <span class="text">{{item.department}}</span>
                    </li>
                  <li>
                    <span class="detailText">设备状态：</span>
                    <span class="text" v-html="item.status_name"></span>
                  </li>
                    <li>
                        <span class="detailText">执行状态：</span>
                        <span class="text" v-html="item.assets_status_name"></span>
                    </li>
                </ul>
            </template>
            <template #footer>
                <van-grid direction="horizontal" :column-num="2" icon-size="24px" v-if="item.is_complete === 1">
                    <van-grid-item v-if="item.show_upload">
                        <template #default>
                            <van-uploader max-size="10485760" multiple :after-read="afterRead(item.cycid,item.assnum)">
                                <van-icon color="#7232dd" name="shangchuan" class-prefix="wx-icon"
                                          style="font-size: 24px;padding-right: 2px;"/>
                                <span>上传纸质报告</span>
                            </van-uploader>
                        </template>
                    </van-grid-item>
                    <van-grid-item text="上传纸质报告" v-else style="opacity: 0.5;cursor: not-allowed;">
                        <template #icon>
                            <van-icon color="#646566" name="shangchuan" class-prefix="wx-icon"
                                      style="font-size: 24px;padding-right: 2px;"/>
                        </template>
                    </van-grid-item>

                    <van-grid-item text="查看保养结果" @click="goUrl(item)">
                        <template #icon>
                            <van-icon color="#ad0000" name="chakan2" class-prefix="wx-icon"
                                      style="font-size: 24px;padding-right: 2px;"/>
                        </template>
                    </van-grid-item>
                </van-grid>
                <van-grid direction="horizontal" :column-num="2" icon-size="24px" v-else>
                    <div v-if="jugdePlatform()">
                        <van-grid-item text="扫码签到" v-if="item.need_sign" @click="sign_in(item.assnum,item.cycid)">
                        <template #icon>
                            <van-icon color="#009688" name="scan" class-prefix="wx-icon"
                                      style="font-size: 24px;padding-right: 2px;"/>
                        </template>
                    </van-grid-item>
                    <van-grid-item text="扫码签到" v-else style="opacity: 0.5;cursor: not-allowed;">
                        <template #icon>
                            <van-icon color="#646566" name="scan" class-prefix="wx-icon"
                                      style="font-size: 20px;padding-right: 2px;"/>
                        </template>
                    </van-grid-item>
                    </div>


                    <van-grid-item text="录入明细" v-if="item.doTask" @click="goUrl(item)">
                        <template #icon>
                            <van-icon color="#1989FA" name="luru" class-prefix="wx-icon"
                                      style="font-size: 20px;padding-right: 2px;"/>
                        </template>
                    </van-grid-item>
                    <van-grid-item text="录入明细" v-else style="opacity: 0.5;cursor: not-allowed;">
                        <template #icon>
                            <van-icon color="#646566" name="luru" class-prefix="wx-icon"
                                      style="font-size: 20px;padding-right: 2px;"/>
                        </template>
                    </van-grid-item>
                </van-grid>
            </template>
        </van-card>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import {
        Loading,
        CellGroup,
        Cell,
        Tab,
        Tabs,
        Popup,
        Field,
        Form,
        Button,
        DatetimePicker,
        Card,
        Dialog,
        Toast,
        Grid,
        GridItem,
        Icon,
        Tag,
        Uploader
    } from 'vant';

    import Qs from 'qs';
    import {getInfo, sign_in} from "@/api/patrol/operation";
    import wechatUtil from "@/utils/wechatUtil";
    import {setSituation} from "@/api/patrol/do-task";
    import {getRegexAssnum} from "@/utils/regex";

    export default {
        name: "operation",
        components: {
            [Loading.name]: Loading,
            [CellGroup.name]: CellGroup,
            [Cell.name]: Cell,
            [Card.name]: Card,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Popup.name]: Popup,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [DatetimePicker.name]: DatetimePicker,
            [Tag.name]: Tag,
            [Grid.name]: Grid,
            [GridItem.name]: GridItem,
            [Icon.name]: Icon,
            [Dialog.Component.name]: Dialog.Component,
            [Uploader.name]: Uploader,
        },
        data() {
            return {
                page: {
                    is_display: false,
                    showTimePicker: false,
                    currentDate: new Date(),
                    assetsListPopup: false
                },
                info: {},
                form: {}
            }
        },
        methods: {
            jugdePlatform(){
                return (process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX)
            },
            //上传
            afterRead(cycid, assnum) {
                return file => {
                  // 上传至服务器
                  if (file.hasOwnProperty('length')){
                    file.forEach(v=>{
                      let form = new FormData();
                      form.append("action", "upload");
                      form.append("file", v.file);
                      form.append("cycid", cycid);
                      form.append("assnum", assnum);
                      setSituation(form).then(res => {
                        if (res.status === 1) {
                          Toast.success(res.msg)
                        }
                      }).catch(err => {
                        Toast.fail(err)
                      });
                    })
                  }else {
                    let form = new FormData();
                    form.append("action", "upload");
                    form.append("file", file.file);
                    form.append("cycid", cycid);
                    form.append("assnum", assnum);
                    setSituation(form).then(res => {
                      if (res.status === 1) {
                        Toast.success(res.msg)
                      }
                    }).catch(err => {
                      Toast.fail(err)
                    });
                  }
                }
            },
            //进页面先获取相关信息
            getInfo() {
                getInfo({
                    operation: this.$route.query.operation,
                    cycid: this.$route.query.cycid,
                }).then(response => {
                    this.info = response.info;
                    this.page.is_display = true;
                })
            },
            goUrl(item) {
              console.log(item);
                this.$router.push({
                    path: item.actionurl,
                    query: {
                        assnum: item.assnum,
                        operation: 'setSituation',
                        cycid: item.cycid
                    }
                });
            },
            //签到
            sign_in(current_assnum,cycid) {
                var _this = this;
                wechatUtil
                    .init([
                        'getLocation',//获取位置
                        'scanQRCode'//扫一扫
                    ])
                    .then((wx) => {
                        // 这里写微信的接口
                        wx.getLocation({
                                type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
                                success: function (res) {
                                    let latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
                                    let longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
                                    wx.scanQRCode({
                                        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                                        scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
                                        async success (res) {
                                            // let assnum = res.resultStr;
                                            // if (assnum.indexOf("ODE_") > 0) {
                                            //     assnum = res.resultStr.substr(9);
                                            // }
                                          const assnum = await getRegexAssnum(res.resultStr)

                                          let params = {};
                                            if (current_assnum !== assnum) {
                                                Toast.fail("扫码设备与签到设备不一致");
                                                return false;
                                            }
                                            params.assnum = assnum;
                                            params.latitude = latitude;
                                            params.longitude = longitude;
                                            params.cycid = cycid;
                                            params.operation = 'sign_in';
                                            sign_in(Qs.stringify(params)).then(response => {
                                                if (response.status === 1) {
                                                  Toast.success({
                                                    message: '上传成功',
                                                    duration: 2000,
                                                    onClose: () => {
                                                      _this.getInfo();
                                                    }
                                                  })
                                                } else {
                                                    Toast.fail(response.msg);
                                                }
                                            });
                                        }
                                    });
                                }
                            }
                        );
                    })
            },
        },
        mounted() {
            this.getInfo()
        }

    }
</script>

<style lang="scss" scoped>
    li {
        display: flex;
        margin-bottom: 0.3rem;
    }

    .detailText {
        width: 4.5rem;
        color: #555;
    }

    .text {
        flex: 1;
        color: #333;
    }

    .operation {
        background-color: #f5f5fa;
        height: 100vh;
    }

    .van-card {
        font-size: 14px;
        background: #fff;
        margin-bottom: 10px;
    }

    .mgt1 {
        margin-top: 1rem;
    }

    .vc {
        color: red;
    }
</style>
<style>
    .van-grid-item__content {
        padding: 6px;
    }
</style>
