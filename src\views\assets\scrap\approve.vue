<template>
    <div class="con_detail" v-if="page.is_display===1">
        <van-tabs :border="false">
            <van-tab title="设备基本信息">
                <Assetsinfo :info='info'/>
            </van-tab>
            <van-tab title="报废申请信息" v-if="info.scrap!=[]&&info.scrap!=undefined">
                <van-cell-group>
                    <van-cell title="报废单号" :value="info.scrap.scrapnum"/>
                    <van-cell title="报废日期" :value="info.scrap.scrapdate"/>
                    <van-cell title="申请人" :value="info.scrap.apply_user"/>
                    <van-cell title="报废原因" :value="info.scrap.scrap_reason"/>
                </van-cell-group>
            </van-tab>
        </van-tabs>

        <div style="margin-bottom: 20px;">
            <div class="card-header">
                <h2 class="detailTitle">审批记录</h2>
                <div class="bl1px"></div>
            </div>
            <Approves :info='info.approves'/>
        </div>
        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">审批表单</h2>
        </div>
        <addApprove :info="page" @values="onSubmit" v-if="page.show_form==1" />
        <div class="cus_loading" v-show="show_loading"><van-loading type="spinner" color="#1989fa" /></div>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {
        Divider,
        Cell,
        CellGroup,
        Icon,
        Field,
        Toast,
        Form,
        Button,
        Tab,
        Tabs,
        Popup,
        Picker,
        DatetimePicker,
        RadioGroup,
        Radio,
        Loading,
        Steps,
        Step,
        Dialog
    } from 'vant';
    import {getInfo, submit} from "@/api/assets/scrap/approve";
    import Assetsinfo from "@/components/Assetsinfo";
    import Approves from "@/components/Approves";
    import addApprove from "@/components/addApprove";

    export default {
        name: 'approve',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Steps.name]: Steps,
            [Step.name]: Step,
            [Loading.name]: Loading,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [DatetimePicker.name]: DatetimePicker,
            [Dialog .name]: Dialog ,
            Assetsinfo,
            Approves,
            addApprove
        },
        data() {
            return {
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    approves: [],//审批记录
                    scrap: [],
                },
                page: {
                    approveDate: '',
                    approveUser: '',
                    is_display: 0,
                    all_approver: '',
                    active: '',
                    show_form: 1,
                },
                form: {
                    is_adopt: '1',
                    remark: '',
                },
                show_loading:0
            }
        },
        methods: {
            onSubmit(values) {
                values.scrid = this.$route.query.scrid;
                let _this = this;
                _this.show_loading = 1;
                submit(Qs.stringify(values)).then(response => {
                    _this.show_loading = 0;
                    Toast({
                        type: 'success',//失败fail
                        duration: 2000,//2秒
                        message: response.msg,
                        icon: 'success',//失败cross
                        forbidClick: true,//是否禁止背景点击，避免重复提交
                        onClose() {
                            //关闭后跳转到首页
                            _this.$router.push(this.$store.getters.moduleName+'/Notin/approve');
                        }
                    });
                })
            },
            getInfo(scrid) {
                let params = {scrid: scrid};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.info.scrap = response.scrap;
                    this.info.approves = response.approves;
                    this.page.approveDate = response.approveDate;
                    this.page.approveUser = response.approveUser;
                    this.page.all_approver = response.scrap.all_approver;
                    this.page.active = response.scrap.app_user_num;
                    this.page.is_display = 1;
                    this.page.show_form = response.is_display;
                });
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.scrid);
        }
    }
</script>
<style scoped lang="scss">
    .card-header {
        margin-top: 20px;
    }

    .app_pro {
        background: #fff;
    }
</style>
<style>
    .app_line {
        height: 22% !important;
    }
</style>
