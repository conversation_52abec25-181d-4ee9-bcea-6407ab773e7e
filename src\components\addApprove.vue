<template>
    <div class="addApproves">
        <van-form ref="approvesForm" @submit="onSubmit">
                <van-field name="is_adopt" label="审批意见">
                  <template #input>
                    <van-radio-group v-model="is_adopt" direction="horizontal">
                        <van-radio name="1">通过</van-radio>
                        <van-radio name="2">不通过</van-radio>
                    </van-radio-group>
                  </template>
                </van-field>
                <van-field
                        v-model="remark"
                        name="remark"
                        rows="1"
                        autosize
                        label="备注"
                        type="textarea"
                        placeholder="请输入审批备注"
                />
                <van-field label="审批人" :value="info.approveUser" readonly/>
                <van-field label="审批时间" :value="info.approveDate" readonly/>
                <div style="margin: 16px;">
                <van-button round block type="info" native-type="submit" >
                    确认并提交
                </van-button>
               </div>
        </van-form>
    </div>
</template>
<script>
    import {Divider, Swipe, SwipeItem,Field, Cell,Form, Button, CellGroup, Tab, Tabs, Step, Steps,Overlay,RadioGroup, Radio,Dialog } from 'vant';
    export default {
        name: 'addApproves',
        components: {
            [Divider.name]: Divider,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Cell.name]: Cell,
            [Field.name]: Field,
            [CellGroup.name]: CellGroup,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Step.name]: Step,
            [Steps.name]: Steps,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [Steps.Overlay]: Overlay,
        },
        methods: {
            onSubmit(values) {
                let msg = '确定通过审批？';
                if(values.is_adopt == 2){
                    msg = '确定驳回申请？';
                }
                Dialog.confirm({
                    message: msg,
                })
                .then(() => {
                    this.$emit("values", values);
                })
                .catch(() => {
                    // on cancel
                });
            }
        },
        data() {
            return {
                position: 0,
                is_adopt:'1',
                remark:'',
            }
        },
        props: ['info'],
    }
</script>

<style scoped lang="scss">
    </style>
