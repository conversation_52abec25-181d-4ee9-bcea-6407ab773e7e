<template>
    <div class="list">
        <div v-if="show.show_repair==1">
            <van-row>
                <van-col span="18" @click="is_display.repair=is_display.repair?false:true;">
                    <div class="bgcf total-div">
                        <div class="total-div-title">
                            <van-icon size="24px" :name="list_img.repair"/>
                            维修流程审批 <span class="num"> {{total.repair}} </span>条
                            <span class="tips" v-show="tips.repair.up" @click="show_hidden('repair')">收起<van-icon name="arrow-up" /></span>
                            <span class="tips" v-show="tips.repair.down" @click="show_hidden('repair')">展开<van-icon name="arrow-down" /></span>
                        </div>
                    </div>

                </van-col>
                <van-col span="6">
                    <Sort @order="repair_order" :option="option.repair"/>
                </van-col>
            </van-row>
            <van-list
                    v-model="loading.repair"
                    v-show="is_display.repair"
                    :finished="finished.repair"
                    finished-text="没有更多了"
                    @load="repairList"
            >
                <van-card v-for="item in list.repair_list" :key="item.assid">
                    <template #desc>
                        <ul>
                            <li>
                                <span class="detailText">维修单号：</span>
                                <span class="text">{{item.repnum}}</span>
                            </li>
                            <li>
                                <span class="detailText">报修科室：</span>
                                <span class="text">{{item.department}}</span>
                            </li>
                            <li>
                                <span class="detailText">设备名称：</span>
                                <span class="text">{{item.assets}}</span>
                            </li>
                            <li>
                                <span class="detailText">设备编号：</span>
                                <span class="text">{{item.assnum}}</span>
                            </li>
                            <li>
                                <span class="detailText">规格型号：</span>
                                <span class="text">{{item.model}}</span>
                            </li>
                            <li>
                                <span class="detailText">报修时间：</span>
                                <span class="text">{{item.applicant_time}}</span>
                            </li>
                        </ul>
                    </template>
                    <template #footer>
                        <van-button v-if="item.btn=='审批'" type="warning" block size="small" class="detail-button"
                                    :to="{ path: $store.getters.moduleName+'/Repair/addApprove', query: { repid: item.repid }}">审批
                        </van-button>
                        <van-button v-else type="default" block size="small" class="detail-button">{{item.btn}}
                        </van-button>
                    </template>
                </van-card>
            </van-list>

            <van-divider/>
        </div>
        <div v-if="show.show_transfer==1">
            <van-row>
                <van-col span="18" @click="is_display.transfer=is_display.transfer?false:true;">
                    <div class="bgcf total-div">
                        <div class="total-div-title">
                            <van-icon size="24px" :name="list_img.transfer"/>
                            转科流程审批 <span class="num"> {{total.transfer}} </span>条
                            <span class="tips" v-show="tips.transfer.up" @click="show_hidden('transfer')">收起<van-icon name="arrow-up" /></span>
                            <span class="tips" v-show="tips.transfer.down" @click="show_hidden('transfer')">展开<van-icon name="arrow-down" /></span>
                        </div>
                    </div>
                </van-col>
                <van-col span="6">
                    <Sort @order="transfer_order" :option="option.transfer"/>
                </van-col>
            </van-row>
            <van-list
                    v-model="loading.transfer"
                    v-show="is_display.transfer"
                    :finished="finished.transfer"
                    finished-text="没有更多了"
                    @load="transferList"
            >
                <van-card v-for="item in list.transfer_list" :key="item.assid">
                    <template #desc>
                        <ul>
                            <li>
                                <span class="detailText">转出科室：</span>
                                <span class="text">{{item.tranout_depart_name}}<img :src="require('@/assets/images/list/right.png')" style="width: 24px;margin-left: 5px;margin-bottom: -7px;"></span>

                            </li>
                            <li>
                                <span class="detailText">转入科室：</span>
                                <span class="text">{{item.tranin_depart_name}}<img :src="require('@/assets/images/list/left.png')" style="width: 24px;margin-left: 5px;margin-bottom: -7px;"></span>
                            </li>
                            <li>
                                <span class="detailText">设备名称：</span>
                                <span class="text">{{item.assets}}</span>
                            </li>
                            <li>
                                <span class="detailText">设备编号：</span>
                                <span class="text">{{item.assnum}}</span>
                            </li>
                            <li>
                                <span class="detailText">规格型号：</span>
                                <span class="text">{{item.model}}</span>
                            </li>
                            <li>
                                <span class="detailText">申请时间：</span>
                                <span class="text">{{item.applicant_time}}</span>
                            </li>
                        </ul>
                    </template>
                    <template #footer>
                        <van-button v-if="item.btn=='审批'" type="warning" block size="small" class="detail-button"
                                    :to="{ path: $store.getters.moduleName+'/Transfer/approval', query: { atid: item.atid }}">审批
                        </van-button>
                        <van-button v-else type="default" block size="small" class="detail-button">{{item.btn}}
                        </van-button>
                    </template>
                </van-card>
            </van-list>
            <van-divider/>
        </div>
        <div v-if="show.show_borrow==1">
            <van-row>
                <van-col span="18" @click="is_display.borrow=is_display.borrow?false:true;">
                    <div class="bgcf total-div">
                        <div class="total-div-title">
                            <van-icon size="24px" :name="list_img.borrow"/>
                            借调流程审批 <span class="num"> {{total.borrow}} </span>条
                            <span class="tips" v-show="tips.borrow.up" @click="show_hidden('borrow')">收起<van-icon name="arrow-up" /></span>
                            <span class="tips" v-show="tips.borrow.down" @click="show_hidden('borrow')">展开<van-icon name="arrow-down" /></span>
                        </div>
                    </div>
                </van-col>
                <van-col span="6">
                    <Sort @order="borrow_order" :option="option.borrow"/>
                </van-col>
            </van-row>
            <van-list
                    v-model="loading.borrow"
                    v-show="is_display.borrow"
                    :finished="finished.borrow"
                    finished-text="没有更多了"
                    @load="borrowList"
            >
                <van-card v-for="item in list.borrow_list" :key="item.assid">
                    <template #desc>
                        <ul>
                            <li>
                                <span class="detailText">借出科室：</span>
                                <span class="text">{{item.department}}<img :src="require('@/assets/images/list/right.png')" style="width: 24px;margin-left: 5px;margin-bottom: -7px;"></span>
                            </li>
                            <li>
                                <span class="detailText">借入科室：</span>
                                <span class="text">{{item.apply_department}}<img :src="require('@/assets/images/list/left.png')" style="width: 24px;margin-left: 5px;margin-bottom: -7px;"></span>
                            </li>
                            <li>
                                <span class="detailText">设备名称：</span>
                                <span class="text">{{item.assets}}</span>
                            </li>
                            <li>
                                <span class="detailText">规格型号：</span>
                                <span class="text">{{item.model}}</span>
                            </li>
                            <li>
                                <span class="detailText">申请时间：</span>
                                <span class="text">{{item.apply_time}}</span>
                            </li>
                            <li>
                                <span class="detailText">预计归还：</span>
                                <span class="text">{{item.estimate_back}}</span>
                            </li>
                        </ul>
                    </template>
                    <template #footer>
                        <van-button v-if="item.btn=='审批'" type="warning" block size="small" class="detail-button"
                                    :to="{ path: $store.getters.moduleName+'/Borrow/approveBorrow', query: { borid: item.borid }}">审批
                        </van-button>
                        <van-button v-else type="default" block size="small" class="detail-button">{{item.btn}}
                        </van-button>
                    </template>
                </van-card>
            </van-list>
            <van-divider/>
        </div>
        <div v-if="show.show_scrap==1">
            <van-row>
                <van-col span="18" @click="is_display.scrap=is_display.scrap?false:true;">
                    <div class="bgcf total-div">
                        <div class="total-div-title">
                            <van-icon size="24px" :name="list_img.scrap"/>
                            报废流程审批 <span class="num"> {{total.scrap}} </span>条
                            <span class="tips" v-show="tips.scrap.up" @click="show_hidden('scrap')">收起<van-icon name="arrow-up" /></span>
                            <span class="tips" v-show="tips.scrap.down" @click="show_hidden('scrap')">展开<van-icon name="arrow-down" /></span>
                        </div>
                    </div>
                </van-col>
                <van-col span="6">
                    <Sort @order="scrap_order" :option="option.scrap"/>
                </van-col>
            </van-row>
            <van-list
                    v-model="loading.scrap"
                    v-show="is_display.scrap"
                    :finished="finished.scrap"
                    finished-text="没有更多了"
                    @load="scrapList"
            >
                <van-card v-for="item in list.scrap_list" :key="item.assid">
                    <template #desc>
                        <ul>
                            <li>
                                <span class="detailText">报废单号：</span>
                                <span class="text">{{item.scrapnum}}</span>
                            </li>
                            <li>
                                <span class="detailText">设备名称：</span>
                                <span class="text">{{item.assets}}</span>
                            </li>
                            <li>
                                <span class="detailText">设备编号：</span>
                                <span class="text">{{item.assnum}}</span>
                            </li>
                            <li>
                                <span class="detailText">报废日期：</span>
                                <span class="text">{{item.scrapdate}}</span>
                            </li>
                            <li>
                                <span class="detailText">申请人：</span>
                                <span class="text">{{item.apply_user}}</span>
                            </li>
                            <li>
                                <span class="detailText">使用科室：</span>
                                <span class="text">{{item.department}}</span>
                            </li>
                        </ul>
                    </template>
                    <template #footer>
                        <van-button v-if="item.btn=='审批'" type="warning" block size="small" class="detail-button"
                                    :to="{ path: $store.getters.moduleName+'/Scrap/examine', query: { scrid: item.scrid }}">审批
                        </van-button>
                        <van-button v-else type="default" block size="small" class="detail-button">{{item.btn}}
                        </van-button>
                    </template>
                </van-card>
            </van-list>
            <van-divider/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {
        Cell,
        CellGroup,
        List,
        Button,
        Card,
        Search,
        Col,
        Row,
        Icon,
        DropdownMenu,
        DropdownItem,
        Divider
    } from "vant";
    import {getApproveList, postApproveList} from "@/api/common/approve";
    import Sort from "@/components/Sort";

    export default {
        name: 'getApproveList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Divider.name]: Divider,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            Sort,
        },
        data() {
            return {
                list_img:{
                    repair:process.env.VUE_APP_BASE_PROXY_URL + '/Public/mobile/images/icon/wxcl.png',
                    scrap:process.env.VUE_APP_BASE_PROXY_URL + '/Public/mobile/images/icon/bfsq.png',
                    borrow:process.env.VUE_APP_BASE_PROXY_URL + '/Public/mobile/images/icon/jdsq.png',
                    transfer:process.env.VUE_APP_BASE_PROXY_URL + '/Public/mobile/images/icon/zksq.png',
                },
                list: {
                    repair_list: [],
                    borrow_list: [],
                    scrap_list: [],
                    transfer_list: [],
                },
                show: {
                    show_borrow: 0,
                    show_repair: 0,
                    show_scrap: 0,
                    show_transfer: 0,
                },
                is_display: {
                    repair: true,
                    borrow: true,
                    scrap: true,
                    transfer: true,
                },
                tips: {
                    repair: {
                        up:true,
                        down:false
                    },
                    borrow: {
                        up:true,
                        down:false
                    },
                    scrap: {
                        up:true,
                        down:false
                    },
                    transfer: {
                        up:true,
                        down:false
                    },
                },
                keyword: '',
                loading: {
                    repair: false,
                    borrow: false,
                    scrap: false,
                    transfer: false,
                },
                finished: {
                    repair: false,
                    borrow: false,
                    scrap: false,
                    transfer: false,
                },
                option: {
                    repair: [
                        {text: '按科室名称（降序）', value: 'department-desc'},
                        {text: '按报修时间（降序）', value: 'applicant_time-desc'},
                        {text: '按科室名称（升序）', value: 'department-asc'},
                        {text: '按报修时间（升序）', value: 'applicant_time-asc'},],
                    borrow: [
                        {text: '按科室名称（降序）', value: 'department-desc'},
                        {text: '按申请时间（降序）', value: 'applicant_time-desc'},
                        {text: '按科室名称（升序）', value: 'department-asc'},
                        {text: '按申请时间（升序）', value: 'applicant_time-asc'},],
                    transfer: [
                        {text: '按科室名称（降序）', value: 'department-desc'},
                        {text: '按申请时间（降序）', value: 'applicant_time-desc'},
                        {text: '按科室名称（升序）', value: 'department-asc'},
                        {text: '按申请时间（升序）', value: 'applicant_time-asc'},],
                    scrap: [
                        {text: '按科室名称（降序）', value: 'department-desc'},
                        {text: '按申请时间（降序）', value: 'applicant_time-desc'},
                        {text: '按科室名称（升序）', value: 'department-asc'},
                        {text: '按申请时间（升序）', value: 'applicant_time-asc'},],

                },
                total: {
                    repair: 0,
                    borrow: 0,
                    scrap: 0,
                    transfer: 0,
                },
                //全局列表搜索条件
                where: {}
            };
        },
        methods: {
            repairList() {
                let data = [];
                if (this.where.order) {
                    data.order = this.where.order;
                    data.sort = this.where.sort;
                }
                data.action = "repair";
                postApproveList(Qs.stringify(data)).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished.repair = true;
                    }
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.repair_list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    this.total.repair = response.total;
                    //数据获取到了 停止加载
                    this.loading.repair = false;
                    this.finished.repair = true;
                    this.where = [];
                })
            },
            borrowList() {
                let data = [];
                if (this.where.order) {
                    data.order = this.where.order;
                    data.sort = this.where.sort;
                }
                data.action = "borrow";
                postApproveList(Qs.stringify(data)).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished.borrow = true;
                    }
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.borrow_list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    this.total.borrow = response.total;
                    //数据获取到了 停止加载
                    this.loading.borrow = false;
                    this.finished.borrow = true;
                    this.where = [];
                })
            },
            transferList() {
                let data = [];
                if (this.where.order) {
                    data.order = this.where.order;
                    data.sort = this.where.sort;
                }
                data.action = "transfer";
                postApproveList(Qs.stringify(data)).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished.transfer = true;
                    }
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.transfer_list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    this.total.transfer = response.total;
                    //数据获取到了 停止加载
                    this.loading.transfer = false;
                    this.finished.transfer = true;
                    this.where = [];
                });
            },
            scrapList() {
                let data = [];
                if (this.where.order) {
                    data.order = this.where.order;
                    data.sort = this.where.sort;
                }
                data.action = "scrap";
                postApproveList(Qs.stringify(data)).then(response => {
                    //先判断如果返回的总数为0 则把状态设置为停止加载
                    if (response.total === 0) {
                        this.finished.scrap = true;
                    }
                    //判断数据键值是否存在
                    if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                        this.list.scrap_list.push(...response.rows);
                    }
                    //仅第一页赋值总数
                    this.total.scrap = response.total;
                    //数据获取到了 停止加载
                    this.loading.scrap = false;
                    this.finished.scrap = true;
                    this.where = [];
                })
            },
            getcompetence() {
                getApproveList(this.where).then(response => {
                    this.show = response.data;
                });
            },
            repair_order(value) {
                this.list.repair_list = [];
                this.loading.repair = true;
                this.finished.repair = false;
                //重新加搜索条件
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.repairList();
            },
            borrow_order(value) {
                this.list.borrow_list = [];
                this.loading.borrow = true;
                this.finished.borrow = false;
                //重新加搜索条件
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.borrowList();
            },
            scrap_order(value) {
                this.list.scrap_list = [];
                this.loading.scrap = true;
                this.finished.scrap = false;
                //重新加搜索条件
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.scrapList();
            },
            transfer_order(value) {
                this.list.transfer_list = [];
                this.loading.transfer = true;
                this.finished.transfer = false;
                //重新加搜索条件
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.transferList();
            },
            show_hidden(module){
                if(this.is_display[module]){
                    this.tips[module].up = false;
                    this.tips[module].down = true;
                }else{
                    this.tips[module].down = false;
                    this.tips[module].up = true;
                }
            }
        }, mounted() {
            //获取相关设备信息
            this.getcompetence();
        }
    }
</script>

<style scoped lang="scss">
.tips{
    color:#DEDEDE;
    margin-left: 20px;
}
.tips{
    .van-icon{
        top:2px;
        padding-left: 8px;
    }
}
</style>
