<template>
    <div class="AssetsInfo">
        <van-cell-group>
            <van-cell title="设备名称 " :value="info.assets"/>
            <van-cell title="设备编号" :value="info.assnum"/>
            <van-cell title="设备原编号" :value="info.assorignum"/>
            <van-cell title="规格/型号" :value="info.model"/>
            <van-cell title="产品序列号" :value="info.serialnum"/>
            <van-cell title="注册证编号" :value="info.registration"/>
            <van-cell title="设备常用名" :value="info.common_name"/>
            <van-cell title="设备分类" :value="info.cate_name"/>
        </van-cell-group>
        <van-cell-group :class="[isActive?'static':'']">
            <van-cell title="系统辅助分类" :value="info.helpcat"/>
            <van-cell title="设备原值(元)" :value="info.buy_price"/>
            <van-cell title="预计使用年限" :value="info.expected_life"/>
            <van-cell title="残净值率(%)" :value="info.residual_value"/>
            <van-cell title="生产厂商" :value="info.factoryInfo.sup_name"/>
            <van-cell title="供应商" :value="info.supplierInfo.sup_name"/>
            <van-cell title="维修商" :value="info.repairInfo.sup_name"/>
            <van-cell title="出厂日期" :value="info.factorydate"/>
            <van-cell title="保修到期日期" :value="info.guarantee_date"/>
            <van-cell title="保修状态">
                <template #right-icon><span v-html="info.guaranteeStatus"/></template>
            </van-cell>
            <van-cell title="单位" :value="info.unit"/>
            <van-cell title="品牌" :value="info.brand"/>
            <van-cell title="出厂编号" :value="info.factorynum"/>
            <van-cell title="设备类型" :value="info.type"/>
            <van-cell title="发票编号" :value="info.invoicenum"/>
            <van-cell title="所属科室" :value="info.department"/>
            <van-cell title="管理科室" :value="info.managedepart"/>
            <van-cell title="所在位置" :value="info.address"/>
            <van-cell title="资产负责人" :value="info.assetsrespon"/>
            <van-cell title="财务分类" :value="info.finance"/>
            <van-cell title="设备来源" :value="info.assfrom"/>
            <van-cell title="资金来源" :value="info.capitalfrom"/>
            <van-cell title="入库日期" :value="info.storage_date"/>
            <van-cell title="启用日期" :value="info.opendate"/>
            <van-cell title="折旧方式" :value="info.depreciation_method_name"/>
            <van-cell title="折旧年限" :value="info.depreciable_lives"/>
        </van-cell-group>
        <div class="moreButton" @click="display()">{{text}}</div>
    </div>
</template>
<script>
    import {Divider, Swipe, SwipeItem, Cell, CellGroup, Tab, Tabs, Step, Steps, Overlay} from 'vant';

    export default {
        name: 'AssetsInfo',
        components: {
            [Divider.name]: Divider,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Step.name]: Step,
            [Steps.name]: Steps,
            [Steps.Overlay]: Overlay,
        },
        methods: {
            display() {
                if (this.isActive) {
                    this.isActive = false;
                    this.text = '收起';
                } else {
                    this.isActive = true;
                    this.text = '加载更多';
                }
            }
        },
        data() {
            return {
                position: 0,
                isActive: true,
                text: '加载更多',
            }
        },
        props: ['info'],
    }
</script>

<style scoped lang="scss">
    .moreButton {
        text-align: center;
        height: 2rem;
        line-height: 2rem;
        color: #2a7ae2;
    }

    .static {
        display: none;
    }
</style>
