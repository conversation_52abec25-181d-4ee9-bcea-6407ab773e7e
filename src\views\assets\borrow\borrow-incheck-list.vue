<template>
    <div class="list">
                <van-search
                        v-model="keyword"
                        shape="round"
                        placeholder="搜索(设备名称、编号、单号)"
                        @search="onSearch"
                />
        <van-row>
            <van-col span="18">
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        待借入检查确定设备 <span class="num"> {{total}} </span> 台
                    </div>
                </div>
            </van-col>
            <van-col span="6">
                <Sort @order="order"  :option="option"/>
            </van-col>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="refreshList"
        >
            <van-card :thumb="item.pic_url" v-for="item in list" :key="item.assid" @click="tourl(item.borid)">
                <template #title>
                    <van-cell>
                        <!-- 使用 title 插槽来自定义标题 -->
                        <template #title>
                            <span class="custom-title">借调单号：{{item.borrow_num}}</span>
                        </template>
                        <template #right-icon>
                            <van-tag type="primary"><span>待借入验收</span></van-tag>
                        </template>
                    </van-cell>
                </template>
                <template #desc>
                    <div class="atn">
                        <span class="ct">设备名称：</span>
                        <span class="cc">{{item.assets}}</span>
                    </div>
                    <div class="atn">
                        <span class="ct">设备编号：</span>
                        <span class="cc">{{item.assnum}}</span>
                    </div>
                    <div class="atn">
                        <span class="ct">被借科室：</span>
                        <span class="cc">{{item.department}}</span>
                    </div>
                </template>
                <template #footer>
                    <div class="gt">
                        <van-row>
                            <van-col span="10">申请人：{{item.apply_user}}</van-col>
                            <van-col span="14">申请时间：{{item.apply_time}}</van-col>
                        </van-row>
                    </div>
                </template>





<!--                <template #footer>-->
<!--                    <van-button type="primary" block size="small" class="detail-button" :to="{ path: '/M/Borrow/borrowInCheck', query: { borid: item.borid }}">借入验收</van-button>-->
<!--                </template>-->
            </van-card>
        </van-list>
        <ScrollTop/>
    </div>
</template>

<script>
    import {Tag,Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem} from "vant";
    import {getBorrowInCheckList} from "@/api/assets/borrow/list";
    import ScrollTop from "@/components/ScrollTop";
    import Sort from "@/components/Sort";

    export default {
        name: 'getBorrowInCheckList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            [Tag.name]: Tag,
            ScrollTop,
            Sort
        },
        data() {
            return {
                list: [],
                option: [
                    {text: '按借调申请时间（降序）', value: 'apply_time-desc'},
                    {text: '按申请科室（降序）', value: 'apply_departid-desc'},
                    {text: '按借调申请时间（升序）', value: 'apply_time-asc'},
                    {text: '按申请科室（升序）', value: 'apply_departid-asc'},
                ],
                keyword: '',
                orderValue: '',
                loading: false,
                finished: false,
                total: 0,
                //全局列表搜索条件
                where: {
                    page: 1
                }
            };
        },
        methods: {
            tourl(borid){
                this.$router.push({
                    name:'borrowInCheck',
                    query:{
                        borid:borid
                    }
                });
            },
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                setTimeout(() => {
                    getBorrowInCheckList(this.where).then(response => {
                        //先判断如果返回的总数为0 则把状态设置为停止加载
                        if (response.total === 0) {
                            this.finished = true;
                        }
                        //判断数据键值是否存在
                        if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                            this.list.push(...response.rows);
                        }
                        //仅第一页赋值总数
                        if (this.where.page === 1) {
                            this.total = response.total;
                        }
                        //数据获取到了 停止加载
                        this.loading = false;
                        //全部加载了 且当前列表数量大于总数 设置完成状态
                        if (this.list.length >= this.total) {
                            this.finished = true;
                        }
                        //页数加1
                        this.where.page++;
                    })
                }, 100);
            },
            onSearch(keyword) {
                this.resetWhere();
                this.finished = false;
                //重新加搜索条件
                this.where.search = keyword;
                this.refreshList();
            },
            order(value) {
                this.resetWhere(false);
                //重新加搜索条件
                this.where.page = 1;
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.refreshList();
            },
            // getcatid(id) {
            //     this.resetWhere();
            //     //重新加搜索条件
            //     this.where.catid = id;
            //     this.refreshList();
            // },
            // getdepartid(id) {
            //     this.resetWhere();
            //     //重新加搜索条件
            //     this.where.departid = id;
            //     this.refreshList();
            // },
            //重置列表条件
            resetWhere(clearWhere = true) {
                this.list = [];//清空列表
                if (clearWhere) {
                    //清空搜索条件
                    this.where = {};
                }
                //重置为第一页
                this.where.page = 1;
                //重置表格为加载状态
                this.loading = true;
                this.finished = false;
            }
        }
    }
</script>

<style scoped lang="scss">
    .list {
        .total-div {
            margin-top: 0;
        }

        ::v-deep .filter {
            margin-top: 0;
        }
        .van-search{
            margin-top: 10px;
        }


        .custom-title{
            font-size: 18px;
        }

        .van-card{
            padding: 8px 0;
        }
        .van-cell {
            padding: 10px 0;
        }
        .van-card__header{padding: 8px 16px;}
        .van-card__footer{background:#FAFAFA;padding-left: 16px;}
        .atn{margin-top: 5px;}
        .ct{font-size: 14px;}
        .cc{font-size: 14px;color:#B0B9BD;}
        .gt{height: 45px;line-height: 45px;font-size: 14px;color:#B0B9BD;overflow: hidden;}
        .van-card__footer{
            text-align: left;
        }
    }

</style>
