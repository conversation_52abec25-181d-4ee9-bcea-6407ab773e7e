<template>
    <div class="bottom" v-if="showStatus">
        <div style="height: 10rem;"></div>
        <van-tabbar v-model="active">
            <van-tabbar-item icon="arrow-left" @click="back">后退</van-tabbar-item>
            <van-tabbar-item icon="arrow" @click="advance">前进</van-tabbar-item>
            <!-- <van-tabbar-item icon="home-o" to="/">首页</van-tabbar-item>
            <van-tabbar-item icon="close" to="/logout">登出</van-tabbar-item> -->

            <!-- <van-tabbar-item>
                <component :is="dynamicComponent" />
                <button @click="loadComponentA">加载左右滑</button>
            </van-tabbar-item> -->
            
        </van-tabbar>
    </div>
</template>
<script>
    import {Tabbar, TabbarItem } from 'vant';
    export default {
        name: 'Bottom',
        components: {
            [Tabbar.name]: Tabba<PERSON>,
            [TabbarItem.name]: TabbarItem,
        },
        mounted(){
            this.$bus.$on('changeLoginStatus',(status) => {
                this.showStatus = status
            })

        },
        methods: {
            loadComponentA() {
                import('@/components/pageFooter').then(component => {
                    this.dynamicComponent = component.default || component;
                });
            },
            back(){
                this.$router.go(-1);
            },
            advance(){
                this.$router.go(1)
            },
        },
        data() {
            return {
                position: 0,
                is_adopt:'1',
                remark:'',
                active: 0,
                showStatus: false,
                dynamicComponent: null,
            }
        },
        props: ['info'],
    }
</script>

<style scoped lang="scss">
    </style>
