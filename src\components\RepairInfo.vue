<template>
    <div class="RepairInfo">
        <van-cell-group>
            <van-cell title="维修单号 " :value="info.repnum"/>
            <van-cell title="报修科室" :value="info.department"/>
            <van-cell title="设备编号" :value="info.assnum"/>
            <van-cell title="设备名称" :value="info.assets"/>
            <van-cell title="规格/型号" :value="info.model"/>
            <van-cell title="报修人" :value="info.applicant"/>
            <van-cell title="报修时间" :value="info.applicant_time"/>
            <van-cell title="接单时间" :value="info.response_date"/>
        </van-cell-group>
        <van-cell-group :class="[isActive?'static':'']">
            <van-cell title="维修工程师" :value="info.response"/>
            <van-cell title="故障问题">
                <template #right-icon><span v-html="info.fault_problem"/></template>
            </van-cell>
            <van-cell title="故障描述" :value="info.breakdown"/>
            <div v-if="jugdePlatform()">
                <van-cell title="语音描述" v-if="info.wxTapeAmr==''" value="无"/>
            <van-cell title="语音描述" v-else>
                <template #right-icon>
                    <audio :src="info.wxTapeAmr" id="audio"></audio>
                    <van-icon class-prefix="wx-icon" :name="page.icon_name" style="line-height: inherit;" @click="bofang"/>
                    <span @click="bofang">{{info.seconds}}〞 点击播放</span>
                </template>
            </van-cell>
            </div>

            <van-cell title="故障照片" v-if="!info.pic_url" value="无"/>
            <van-cell title="故障照片" v-else>
                <template #right-icon>
                    <a href="javascript:" @click="img">查看(总共{{info.imgCount}}张)</a>
                </template>
            </van-cell>
            <van-cell title="检修备注" :value="info.repair_remark"/>
            <van-cell title="维修性质" :value="info.repTypeName"/>
            <van-cell title="维修工时" :value="info.working_hours"/>
            <van-cell title="维修费用" :value="info.actual_price"/>
            <van-cell title="处理详情" :value="info.dispose_detail"/>
        </van-cell-group>
        <div class="moreButton" @click="display()">{{text}}</div>
    </div>
</template>
<script>
    import {
        Divider,
        Swipe,
        SwipeItem,
        Cell,
        CellGroup,
        Tab,
        Tabs,
        Step,
        Steps,
        Overlay,
        ImagePreview,
        Icon
    } from 'vant';

    export default {
        name: 'RepairInfo',
        components: {
            [Divider.name]: Divider,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Cell.name]: Cell,
            [Icon.name]: Icon,
            [CellGroup.name]: CellGroup,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Step.name]: Step,
            [ImagePreview.name]: ImagePreview,
            [Steps.name]: Steps,
            [Steps.Overlay]: Overlay,
        },
        methods: {
            jugdePlatform(){
                return (process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX)
            },
            display() {
                if (this.isActive) {
                    this.isActive = false;
                    this.text = '收起';
                } else {
                    this.isActive = true;
                    this.text = '加载更多';
                }
            },
            bofang() {
                let audio = document.getElementById("audio");
                if (audio.paused) { //判断当前的状态是否为暂停，若是则点击播放，否则暂停
                    this.page.icon_name = 'zanting';
                    audio.play();
                    let sec = this.page.voiceTime * 1000;
                    //变更图标
                    setTimeout(() => {
                        this.page.icon_name = 'yuyin';
                    }, sec);
                } else {
                    this.page.icon_name = 'yuyin';
                    audio.pause();
                }
            },
            img() {
                ImagePreview(this.page.img);
            },
        },
        data() {
            return {
                position: 0,
                isActive: true,
                text: '加载更多',
                page: {
                    icon_name: 'yuyin',
                    voiceTime: 0,
                    img: [],
                }
            }
        },
        props: ['info'],
        mounted() {
            if (this.info.wxTapeAmr != "") {
                this.info.wxTapeAmr = process.env.VUE_APP_BASE_PROXY_URL + this.info.wxTapeAmr;
                this.page.voiceTime = this.info.seconds;
            }
            for (let i in this.info.pic_url) {
                this.page.img.push(process.env.VUE_APP_BASE_PROXY_URL + this.info.pic_url[i]);
            }
        }
    }
</script>

<style scoped lang="scss">
    .moreButton {
        text-align: center;
        height: 2rem;
        line-height: 2rem;
        color: #2a7ae2;
    }

    .static {
        display: none;
    }
</style>
