//移动端全局样式控制
.bgcf {
  background-color: #fff
}
/*测试 仅用于解决线上部署时 cell的宽度不一致导致刷新后宽度变化的问题*/
.van-field__label{
  flex: none !important;
}

//列表页
.list {
  background-color: #f5f5fa;
  margin-top: 1rem;

  .scanQrcode {
    margin: 6px auto 8px;
    width: 94%;
    font-size: 16px;
  }
  .van-search{
    margin-bottom: 18px;
  }
  .filter {
    margin-top: 10px;
  }

  .van-card {
    font-size: 16px;
    background-color: #fff;
  }

  .detail-button {
    margin-top: 10px;
    font-size: 16px;
  }

  .van-card__thumb {
    width: 100px;
    height: 132px;
    margin-top: 5px;
  }

  .total-div {
    margin-top: 10px;
    height: 48px;

    .total-div-title {
      padding: 5px 0 0 20px;
      position: relative;
    }

    .van-icon {
      top: 5px;
      left: -5px;
    }

    .num {
      color: #FF5722
    }
  }

  .van-dropdown-menu__bar {
    box-shadow: none;
  }

  .van-card__footer .van-button {
    margin-left: 0;
  }

  .van-row {
    margin-bottom: 0.5rem;
  }

  .van-list {
    .van-card {
      .content {
        cursor: pointer;
      }

      li {
        margin-bottom: 0.6rem;
      }
      .list_li_width {
        width: 98%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .detailText {
        text-align: right;
        display: inline-block;
        width: 5rem;
        color: #555;
        font-size: 0.875rem;
      }
      .text {
        font-size: 1rem;
        color: #333;
      }
      .jumpButton {
        position: absolute;
        right: 0.1rem;
        bottom: 1.5rem;
        z-index: 3;
        color: #B3B3B3;
      }
    }
  }
}

//隐藏元素
.hide {
  display: none
}

//全局loading
.global-loading {
  text-align: center;
  height: 600px;
  line-height: 600px;
}

//全局修改vant button样式
//.van-button--primary {
//  background-color: #009688 !important;
//  border-color: #009688 !important;
//}

.van_title{
  font-size: 1rem;
  color: #333;
}
.detailTitle {
  padding-left: 1rem;
  font-size: 1rem;
  color: #333;
  display: inline-block;
}
.bl1px {
  border-left: 5px solid #5FB878;
  position: absolute;
  width: 1px;
  height: 60%;
  top: 20%;
  left: 1rem;
}
.card-header {
  position: relative;
  padding: 0 15px;
  border-bottom: 1px solid #f6f6f6;
  border-radius: 2px 2px 0 0;
  font-size: 14px;
  background: #fff;
}
.moreButton{
  background: #fff;
}
.RepairInfo{
  margin-bottom: 1rem;
}
.AssetsInfo{
  margin-bottom: 1rem;
}
.formImage {
  width: 1.5rem;
  height: 1.5rem;
  display: inline-block;
  margin-bottom: -5px;
}
.formTitle {
  font-size: 1rem;
  color:#333;
  display: inline-block;
  padding-left: 5px;
}
.con_detail{
  background: #f5f5fa;
}



.van-doc-demo-block__title {
  margin: 0;
  padding: 32px 16px 16px;
  color: rgba(69, 90, 100, 0.6);
  font-weight: normal;
  font-size: 14px;
  line-height: 16px;
}

.cus_loading{
  width: 100%;
  margin: 0 auto;
  text-align: center;
  bottom:40%;
  position: fixed;
}
.van-uploader {
  margin-left: 1rem;
}
