import router from './router'
//import store from './store'
//全局路由前置钩子
router.beforeEach(function (to, from, next) {
    //如果有title 就改变title
    if (to.meta.title) {
        document.title = to.meta.title;
    }else{
        document.title = '医疗设备管理系统';
    }
    if (to.meta.needLogin) {

        if(process.env.VUE_APP_PLATFORM != process.env.VUE_APP_WX){
            let expire_time = JSON.parse(localStorage.getItem('expire_time'))
            let time = new Date().getTime() - 24 * 60 * 60 * 1000
            if(time < expire_time){
                next()
            }else{
                localStorage.removeItem('expire_time')
                next({name:"login"})
            }
        }else{
            next()
        }
        
        //页面是否登录
        // if (localStorage.getItem("token")) {
        //     //本地存储中是否有token(uid)数据
        //     next(); //表示已经登录
        // } else {
        //     //next可以传递一个路由对象作为参数 表示需要跳转到的页面
        //     // next({
        //     //     name: "login"
        //     // });SELECT A.assnum,A.patrid FROM sb_patrol_plans_assets A LEFT JOIN sb_assets_info AS B ON A.assid = B.assid  WHERE B.departid IN ('1','2','3','4','5','6','7','8','9','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31','32','33','34','36','37','38','40','41','42','43','44','45','46','47') GROUP BY A.patrid
        //     next();
        // }
    } else {
        //表示不需要登录
        next(); //继续往后走
    }
});

//全局路由后置钩子
router.afterEach((/*to, from*/) => {
    //let path = to.fullPath.slice(1); // 去除'/'
    // let path = to.fullPath; // 去除'/'
    // if (!sessionStorage.getItem('initLink')) {
    //     // 解决ios微信下，分享签名不成功的问题,将第一次的进入的url缓存起来。
    //     sessionStorage.setItem('initLink', document.URL)
    // }
    // let url;
    // if (!!window.__wxjs_is_wkwebview) {
    //     // ios
    //     url = sessionStorage.getItem('initLink')
    // } else {
    //     // 安卓 process..env.VUE_APP_BASE_PROXY_URL 自己定义各个环境下域名变量
    //     url = location.origin + process..env.VUE_APP_BASE_PROXY_URL + path;
    // }
    // store.commit('app/setInitLink', url)
});
