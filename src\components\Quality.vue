<template>
    <div>
        <van-tabs :border="false">
            <van-tab title="质控计划详情信息" v-if="info.detailInfo">
                <van-cell title="检测报告编号" :value="info.qsInfo.plan_num"/>
                <van-cell title="质控计划名称" :value="info.qsInfo.plan_name"/>
                <van-cell title="检测依据" :value="info.qsInfo.basis"/>
                <van-cell title="检测仪器(标准器、模拟器)" :value="info.qsInfo.instrument"/>
                <van-cell title="检测人" :value="info.detailInfo.adduser"/>
                <van-cell title="检测时间" :value="info.detailInfo.addtime"/>
            </van-tab>
            <van-tab title="质控计划详情信息" v-else>
                <van-cell title="检测报告编号" :value="info.qsInfo.plan_num"/>
                <van-cell title="质控计划名称" :value="info.qsInfo.plan_name"/>
                <van-cell title="制定人" :value="info.qsInfo.adduser"/>
                <van-cell title="制定时间" :value="info.qsInfo.addtime"/>
            </van-tab>
            <van-tab title="检测设备基础信息">
                <van-cell-group>
                    <van-cell title="设备编号" :value="info.asInfo.assnum"/>
                    <van-cell title="设备名称" :value="info.asInfo.assets"/>
                    <van-cell title="规格/型号" :value="info.asInfo.model"/>
                    <van-cell title="使用科室" :value="info.asInfo.department"/>
                    <van-cell title="产品系列号" :value="info.asInfo.serialnum"/>
                    <van-cell title="品牌" :value="info.asInfo.brand"/>
                    <van-cell title="启用日期" :value="info.asInfo.opendate"/>
                    <van-cell title="上次质控时间" :value="info.asInfo.lasttesttime"/>
                </van-cell-group>
            </van-tab>
        </van-tabs>
    </div>
</template>
<script>
    import {Divider, Swipe, SwipeItem, Cell, CellGroup, Tab, Tabs, Step, Steps, Overlay} from 'vant';

    export default {
        name: 'AssetsInfo',
        components: {
            [Divider.name]: Divider,
            [Swipe.name]: Swipe,
            [SwipeItem.name]: SwipeItem,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Step.name]: Step,
            [Steps.name]: Steps,
            [Steps.Overlay]: Overlay,
        },
        data() {
            return {
            }
        },
        props: ['info'],
    }
</script>
