<template>
    <div class="list">
        <van-search
                v-model="keyword"
                shape="round"
                placeholder="请输入搜索关键词"
                @search="onSearch"
        />

        <van-row>
            <van-col span="18">
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        设备质控结果 <span class="num"> {{total}} </span> 台
                    </div>
                </div>
            </van-col>
            <van-col span="6">
                <Sort @order="order" :option="option"/>
            </van-col>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="refreshList"
        >
            <van-card  v-for="item in list" :key="item.assid">
                <template #desc>
                    <ul>
                        <li>
                            <span class="detailText">计划名称：</span>
                            <span class="text">{{item.plan_name}}</span>
                        </li>
                        <li>
                            <span class="detailText">使用科室：</span>
                            <span class="text">{{item.department}}</span>
                        </li>
                        <li>
                            <span class="detailText">设备名称：</span>
                            <span class="text">{{item.assets}}</span>
                        </li>
                        <li>
                            <span class="detailText">设备编号：</span>
                            <span class="text">{{item.assnum}}</span>
                        </li>
                        <li>
                            <span class="detailText">周期计划：</span>
                            <span v-html="item.cycle_status"></span>
                        </li>
                        <li>
                            <span class="detailText">当前状态：</span>
                            <span v-html="item.status_name"/>
                        </li>
                        <li>
                            <span class="detailText">检测人：</span>
                            <span class="text">{{item.username}}</span>
                        </li>
                    </ul>
                </template>
                <template #footer>
                    <van-button type="info" block size="small" class="detail-button" :to="{ path: $store.getters.moduleName+'/Quality/showDetail/'+item.template_name, query: {qsid: item.qsid}}">查看</van-button>
                </template>
            </van-card>
        </van-list>
        <ScrollTop/>
    </div>
</template>

<script>
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem} from "vant";
    import {getResultlist} from "@/api/quality/list";
    import ScrollTop from "@/components/ScrollTop";
    import Sort from "@/components/Sort";

    export default {
        name: 'getAssetsList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            ScrollTop,
            Sort
        },
        data() {
            return {
                list: [],
                option: [
                    {text: '按预计执行日期（降序）', value: 'do_date-desc'},
                    {text: '按预计执行日期（升序）', value: 'do_date-asc'},
                ],
                keyword: '',
                loading: false,
                finished: false,
                total: 0,
                //全局列表搜索条件
                where: {
                    page: 1
                }
            };
        },
        methods: {
            //刷新列表 第一次进入自动触发
            refreshList() {
                // 异步更新数据
                setTimeout(() => {
                    getResultlist(this.where).then(response => {
                        //先判断如果返回的总数为0 则把状态设置为停止加载
                        if (response.total === 0) {
                            this.finished = true;
                        }
                        //判断数据键值是否存在
                        if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                            this.list.push(...response.rows);
                        }
                        //仅第一页赋值总数
                        if (this.where.page === 1) {
                            this.total = response.total;
                        }
                        //数据获取到了 停止加载
                        this.loading = false;
                        //全部加载了 且当前列表数量大于总数 设置完成状态
                        if (this.list.length >= this.total) {
                            this.finished = true;
                        }
                        //页数加1
                        this.where.page++;
                    });
                }, 100);
            },
            onSearch(keyword) {
                this.resetWhere();
                this.finished = false;
                //重新加搜索条件
                this.where.search = keyword;
                this.refreshList();
            },
            order(value) {
                this.resetWhere(false);
                //重新加搜索条件
                this.where.page = 1;
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.refreshList();
            },
            getcatid(id) {
                this.resetWhere();
                //重新加搜索条件
                this.where.catid = id;
                this.refreshList();
            },
            getdepartid(id) {
                this.resetWhere();
                //重新加搜索条件
                this.where.departid = id;
                this.refreshList();
            },
            //重置列表条件
            resetWhere(clearWhere = true) {
                this.list = [];//清空列表
                if (clearWhere) {
                    //清空搜索条件
                    this.where = {};
                }
                //重置为第一页
                this.where.page = 1;
                //重置表格为加载状态
                this.loading = true;
                this.finished = false;
            }
        },
    }
</script>

<style scoped lang="scss">

</style>
