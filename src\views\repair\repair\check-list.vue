<template>
    <div class="list" style="margin-top: 20px;">
        <van-search
                v-model="keyword"
                shape="round"
                placeholder="搜索（设备名称、编号、科室）"
                @search="onSearch"
        />
        <van-row>
            <van-col span="18">
                <div class="bgcf total-div">
                    <div class="total-div-title">
                        <van-icon size="24px" :name="require('@/assets/images/list/list.png')"/>
                        待验收 <span class="num"> {{total}} </span> 台
                    </div>
                </div>
            </van-col>
            <van-col span="6">
                <Sort @order="order" :option="option"/>
            </van-col>
        </van-row>
        <van-list
                v-model="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="examineList"
        >
            <van-card :thumb="item.pic_url" v-for="item in list" :key="item.assid">
                <template #desc>
                    <div class="jumpButton">
                        <van-button color="#009688" size="small" :to="{ path: $store.getters.moduleName+'/Repair/checkRepair', query: { repid: item.repid }}">验收</van-button>
                    </div>
                    <van-row>
                        <van-col span="6">
                            <van-grid column-num="1" center :border="false" icon-size="3.2rem">
                            <van-grid-item :icon="item.headimgurl" :text="item.appUser" />
                            </van-grid>
                        </van-col>
                        <van-col span="18">
                            <van-cell :border="false" :value="item.repnum" />
                            <van-cell :border="false" :value="item.assets" />
                            <van-cell :border="false" :title="item.department" :value="item.appTime" />
                        </van-col>
                    </van-row>
                </template>
            </van-card>
        </van-list>
        <ScrollTop/>
    </div>
</template>

<script>
    import {Cell, CellGroup, List, Button, Card, Search, Col, Row, Icon, DropdownMenu, DropdownItem,Grid, GridItem} from "vant";
    import {examineList} from "@/api/repair/list";
    import ScrollTop from "@/components/ScrollTop";
    import Sort from "@/components/Sort";

    export default {
        name: 'examineList',
        components: {
            [List.name]: List,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Button.name]: Button,
            [Card.name]: Card,
            [Search.name]: Search,
            [Col.name]: Col,
            [Row.name]: Row,
            [Icon.name]: Icon,
            [DropdownMenu.name]: DropdownMenu,
            [DropdownItem.name]: DropdownItem,
            [Grid.name]: Grid,
            [GridItem.name]: GridItem,
            ScrollTop,
            Sort
        },
        data() {
            return {
                list: [],
                option: [
                    {text: '按报修时间（降序）', value: 'applicant_time-desc'},
                    {text: '按报修时间（升序）', value: 'applicant_time-asc'},
                ],
                keyword: '',
                orderValue: '',
                loading: false,
                finished: false,
                total: 0,
                //全局列表搜索条件
                where: {
                    page: 1
                }
            };
        },
        methods: {
            //刷新列表 第一次进入自动触发
            examineList() {
                // 异步更新数据
                setTimeout(() => {
                    examineList(this.where).then(response => {
                        //先判断如果返回的总数为0 则把状态设置为停止加载
                        if (response.total === 0) {
                            this.finished = true;
                        }
                        //判断数据键值是否存在
                        if (Object.prototype.hasOwnProperty.call(response, 'rows')) {
                            this.list.push(...response.rows);
                        }
                        //仅第一页赋值总数
                        if (this.where.page === 1) {
                            this.total = response.total;
                        }
                        //数据获取到了 停止加载
                        this.loading = false;
                        //全部加载了 且当前列表数量大于总数 设置完成状态
                        if (this.list.length >= this.total) {
                            this.finished = true;
                        }
                        //页数加1
                        this.where.page++;
                    })
                }, 100);
            },
            onSearch(keyword) {
                this.resetWhere();
                this.finished = false;
                //重新加搜索条件
                this.where.search = keyword;
                this.examineList();
            },
            order(value) {
                this.resetWhere(false);
                //重新加搜索条件
                this.where.page = 1;
                this.where.sort = value.split('-')[0];
                this.where.order = value.split('-')[1];
                this.examineList();
            },
            getcatid(id) {
                this.resetWhere();
                //重新加搜索条件
                this.where.catid = id;
                this.examineList();
            },
            getdepartid(id) {
                this.resetWhere();
                //重新加搜索条件
                this.where.departid = id;
                this.examineList();
            },
            //重置列表条件
            resetWhere(clearWhere = true) {
                this.list = [];//清空列表
                if (clearWhere) {
                    //清空搜索条件
                    this.where = {};
                }
                //重置为第一页
                this.where.page = 1;
                //重置表格为加载状态
                this.loading = true;
                this.finished = false;
            }
        }
    }
</script>

<style scoped lang="scss">
    .van-cell {
        padding: 5px 2px;
        color: #555;
        font-size: 1rem;
    }
    .list .van-list .van-card .jumpButton {
        bottom: 4rem;
    }
    .van-grid{
        border-right: 1px solid #c9c9c9;
        margin-right: 10px;
    }
    ::v-deep .van-icon__image{
        border-radius:25px;
    }
</style>
