import request from '@/utils/request';

export function getSignature() {
    return request({
        url: '/Notin/getSignature',
        method: 'post',
    })
}

export function downRecord(data) {
    return request({
        url: '/Notin/wxRecordDown',
        method: 'post',
        data
    })
}

export function uploadRecord(data) {
    return request({
        url: '/Repair/addRepair',
        method: 'post',
        data
    })
}

export function uploadFile(file) {
    const data = new FormData(); // 很重要，模拟form表单
    data.append('file', file); //  将图片文件流放到参数里
    return request({
        url: '/Notin/upFile',
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data',// 类型设置
        },
        data
    })
}

