<template>
    <div class="tool-fixbar" v-show="isShowButton===true">
        <van-button icon="arrow-up" type="primary" color="#9F9F9F" @click="goTop"/>
    </div>
</template>

<script>
    import {Button} from "vant";

    export default {
        name: "ScrollTop",
        data() {
            return {
                isShowButton: false,
            }
        },
        components: {
            [Button.name]: But<PERSON>
        },
        mounted() {
            window.addEventListener('scroll', this.handleScroll)
        },
        destroyed() {
            document.removeEventListener('scroll', this.handleScroll);
            document.body.scrollTop = document.documentElement.scrollTop = 0
        },
        methods: {
            goTop() {
                // 回到顶部 平滑滚动
                window.scrollTo({top:0, behavior:'smooth'});
                this.isShowButton = false
            },
            handleScroll() {
                // 滚动操作监听
                let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
                let offsetTop = 0;
                // console.log(scrollTop)
                try {
                    offsetTop = document.querySelector('.tool-fixbar').offsetTop
                } catch (e) {
                    offsetTop = 0
                }
                if (scrollTop > offsetTop) {
                    this.isShowButton = scrollTop > 500;
                } else {
                    this.isShowButton = false
                }
            }
        }
    }
</script>

<style scoped lang="scss">
    .tool-fixbar {
        position: fixed;
        bottom: 160px;
        right: 15px;
    }
</style>
