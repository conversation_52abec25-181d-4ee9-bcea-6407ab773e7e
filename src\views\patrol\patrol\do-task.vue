<template>
    <div class="do-task" v-if="page.is_display">
        <div class="card-header">
            <h2 class="detailTitle">计划基本信息</h2>
            <div class="bl1px"></div>
        </div>
        <van-cell-group>
            <van-cell title="保养模板名称" :value="info.asArr.name"/>
            <van-cell title="保养明细项" :value="info.asArr.count"/>
            <van-cell title="当前状态" :value="info.asArr.status_name"/>
            <van-cell title="设备编号" :value="info.asArr.assnum"/>
            <van-cell title="设备名称" :value="info.asArr.assets"/>
            <van-cell title="设备类型" :value="info.asArr.type_name"/>
            <van-cell title="规格型号" :value="info.asArr.model"/>
            <van-cell title="使用科室" :value="info.asArr.department_name"/>
        </van-cell-group>

    <van-collapse v-model="page.activeNames" style="margin-top: 1rem;">
      <van-popup v-model="showPicker" position="bottom">
        <van-picker
            show-toolbar
            :columns="columns"
            @confirm="onConfirm"
            @cancel="showPicker = false"
        />
      </van-popup>
      <van-collapse-item :title="item.name" :name="item.ppid" v-for="(item,index) in form" :key="item.ppid">
        <van-row>
          <van-col span="4">编号</van-col>
          <van-col span="15">明细名称</van-col>
          <van-col span="5">保养结果</van-col>
        </van-row>
        <van-row class="form-input" v-for="(item1,index1) in item.detail">
          <van-col span="4">{{item1.num}}</van-col>
          <van-col span="16">{{item1.name}}</van-col>
          <van-col span="4" v-if="info.executeData && info.executeData.status === '2'">{{item1.result}}</van-col>
          <van-col span="4" v-else>
            <van-field
                readonly
                clickable
                name="picker"
                :value="item1.result"
                placeholder="点击选择"
                @click="()=>{showPicker = true;selectIndex[0] = index;selectIndex[1] = index1;}"
            />
          </van-col>
          <van-col span="24" v-if="info.executeData && info.executeData.status === '2'">
            <van-field v-if="item1.result !== '合格'" v-model="item1.remark" placeholder="请输入" label="处理详情：" disabled/>
          </van-col>
          <van-col span="24" v-else v-show="item1.result !== '合格'">
            <van-field v-model="item1.abnormal_remark" placeholder="请输入异常处理详情" label="处理详情：" :error-message="item1.abnormal_remark && item1.abnormal_remark.length > 0 ? '' : '请填写此处信息'"/>
          </van-col>
        </van-row>
      </van-collapse-item>
    </van-collapse>
    <van-field label="设备现状：">
      <template #input>
        <div v-if="info.executeData && info.executeData.status === '2'"
             v-html="info.executeData.asset_status"></div>
        <van-radio-group v-model="submitData.asset_status" :disabled="page.assets_disabled"
                         direction="horizontal" v-else @change="assetStatusChange" class="customRadio">
          <van-radio name="1">工作正常</van-radio>
          <van-radio name="2">有小问题，但不影响使用</van-radio>
          <van-radio name="3" :disabled="page.abnormal_count === 0">有故障，需要进一步维修</van-radio>
          <van-radio name="4" :disabled="page.abnormal_count === 0">无法正常使用</van-radio>
          <van-radio name="5">该设备正在维修</van-radio>
          <van-radio name="6">该设备已报废</van-radio>
        </van-radio-group>
      </template>
    </van-field>
    <div v-if="info.executeData && info.executeData.status === '2'">
      <van-field label="完成时间：">
        <template #input>
          <div>{{info.executeData.finish_time}}</div>
        </template>
      </van-field>
        <van-field label="改善建议：">
            <template #input>
                <div>{{info.executeData.remark}}</div>
            </template>
        </van-field>
      <van-field label="纸质报告：">
        <template #input>
          <van-button type="info" size="small" style="margin-right: 1rem;" v-if="info.path.length > 0" @click="showImage()">查看报告</van-button>
          <van-uploader max-size="10485760" multiple :after-read="afterRead(info.cycid,info.asArr.assnum)">
            <van-button size="small" type="primary">上传纸质报告</van-button>
          </van-uploader>
        </template>
      </van-field>
    </div>
    <div v-else>
      <van-field
          v-model="submitData.remark"
          rows="3"
          autosize
          label="改善建议："
          type="textarea"
          maxlength="120"
          show-word-limit
          placeholder="请输入改善建议"
      />
      <van-field label="是否保养：">
        <template #input>
          <van-checkbox v-model="submitData.execute_status" shape="square" @click="checkBoxChange"/> 该设备不做保养
        </template>
      </van-field>
      <van-field
          v-model="submitData.reason"
          rows="1"
          autosize
          label="原因"
          required
          type="textarea"
          placeholder="请输入不做保养原因"
          v-show="submitData.execute_status"
          :rules="[{ required: true, message: '请输入不做保养原因' }]"
      />
      <van-field
          v-show="show_complete_field"
          readonly
          clickable
          name="complete_time"
          v-model="submitData.complete_time"
          :value="complete_time"
          label="完成时间"
          placeholder="点击选择完成时间"
          @click="showTimePicker = true"
          :error-message="hint"
      />
      <van-popup v-model="showTimePicker" position="bottom">
        <van-datetime-picker
            type="datetime"
            :min-date="minDate"
            :max-date="maxDate"
            @confirm="onCompleteTimeConfirm"
            @cancel="showTimePicker = false"
        />
      </van-popup>
      <van-field label="纸质报告" v-show="page.upload_show">
        <template #input>
          <van-button type="info" size="small" style="margin-right: 1rem;" v-if="info.path.length > 0" @click="showImage()">查看报告</van-button>
          <van-uploader max-size="10485760" multiple :after-read="afterRead(info.cycid,info.asArr.assnum)">
            <van-button size="small" type="primary">上传纸质报告</van-button>
          </van-uploader>
        </template>
      </van-field>
      <div style="display: flex;flex-direction: column;align-items: center;margin-bottom: 1.2rem;">
        <van-button square block :color="item.color" :disabled="item.disabled" v-for="item in page.actions"
                    style="margin-top: 1rem;width: 90%;" @click="actionSelect(item.name)">
          {{item.name}}
        </van-button>
      </div>
    </div>

        <van-action-sheet
                v-model="page.showAction"
                :actions="page.actions"
                cancel-text="取消"
                @select="actionSelect"/>
        <van-popup v-model="page.addRepair" round position="bottom">
            <van-picker
                    title="选择报修人"
                    show-toolbar
                    :columns="page.addRepairUser"
                    @cancel="page.addRepair = false"
                    @confirm="onConfirmAddRepair"
            />
        </van-popup>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import Qs from 'qs'
    import {
        Button,
        Card,
        Cell,
        CellGroup,
        DatetimePicker,
        Dialog,
        Field,
        Form,
        Loading,
        Popup,
        Tab,
        Tabs,
        Collapse,
        CollapseItem,
        Col,
        Row,
        Picker,
        Checkbox,
        CheckboxGroup,
        RadioGroup,
        Radio,
        ActionSheet,
        Toast,
        Image as VanImage,
        ImagePreview,
        Uploader,
        Notify
    } from "vant";
    import {getInfo, setSituation} from "@/api/patrol/do-task";

    export default {
        name: "do-task",
        components: {
            [Loading.name]: Loading,
            [CellGroup.name]: CellGroup,
            [Cell.name]: Cell,
            [Card.name]: Card,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Popup.name]: Popup,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [DatetimePicker.name]: DatetimePicker,
            [Dialog.Component.name]: Dialog.Component,
            [Collapse.name]: Collapse,
            [CollapseItem.name]: CollapseItem,
            [Col.name]: Col,
            [Row.name]: Row,
            [Picker.name]: Picker,
            [RadioGroup.name]: RadioGroup,
            [Radio.name]: Radio,
            [Checkbox.name]: Checkbox,
            [CheckboxGroup.name]: CheckboxGroup,
            [ActionSheet.name]: ActionSheet,
            [VanImage.name]: VanImage,
            [Uploader.name]: Uploader,
            [ImagePreview.Component.name]: ImagePreview.Component,
        },
        data() {
            return {
                page: {
                    is_display: false,
                    activeNames: [],
                    showAction: false,
                    actions: [
                        {name: '转至报修', disabled: true, color: "#1E9FFF"},
                        {name: '完成并保存该设备保养', disabled: false, color: "#FFB800"},
                        {name: '暂存该设备保养', disabled: false, color: "#009688"},
                        {name: '不进行保养并结束', disabled: true, color: "#FF5722"}
                    ],
                    assets_disabled: false,
                    addRepair: false,
                    addRepairUser: [],
                    abnormal_count: 0,
                },
                selectIndex: [0, 0],
                info: {},
                value: '',
                columns: ['合格', '修复', '可用', '待修'],
                showPicker: false,
                form: {},
                submitData: {
                    execute_status: false,
                    asset_status: '1',
                    reason: '',
                    complete_time: ''
                },
                complete_time:'',
                hint:'',
                minDate: new Date(),
                maxDate: new Date(),
                show_complete_field: false,
                showTimePicker: false,
            }
        },
        methods: {
            showImage() {
                ImagePreview(this.info.path);
            },
            //上传
            afterRead(cycid, assnum) {
                return file => {
                  if (file.hasOwnProperty('length')){
                    let _this = this;
                    file.forEach(v=>{
                      let form = new FormData();
                      form.append("action", "upload");
                      form.append("file", v.file);
                      form.append("cycid", cycid);
                      form.append("assnum", assnum);
                      setSituation(form).then(res => {
                        if (res.status === 1) {
                          Toast.success({
                            message: '上传成功',
                            duration: 2000,
                            onClose: () => {
                              _this.getInfo();
                            }
                          })
                        }
                      }).catch(err => {
                        Toast.fail(err)
                      });
                    })
                  }else {
                    // 上传至服务器
                    let form = new FormData();
                    let _this = this;
                    form.append("action", "upload");
                    form.append("file", file.file);
                    form.append("cycid", cycid);
                    form.append("assnum", assnum);
                    setSituation(form).then(res => {
                      if (res.status === 1) {
                        Toast.success({
                          message: '上传成功',
                          duration: 2000,
                          onClose: () => {
                            _this.getInfo();
                          }
                        })
                      }
                    }).catch(err => {
                      Toast.fail(err)
                    });
                  }
                }
            },
            getInfo() {
                getInfo({
                    assnum: this.$route.query.assnum,
                    operation: this.$route.query.operation,
                    patrol_level: this.$route.query.patrol_level,
                    cycid: this.$route.query.cycid
                }).then(response => {
                    this.info = response;
                    this.form = this.info.data;
                    this.info.user.forEach(v => {
                        this.page.addRepairUser.push({
                            'text': v.username,
                            'value': v.userid,
                        })
                    });
                    this.form.forEach(v => {
                        this.page.activeNames.push(v.ppid)
                    });
                    if (this.info.executeData) {
                        this.submitData.asset_status = this.info.executeData.asset_status_num;
                        this.submitData.remark = this.info.executeData.remark
                        // this.submitData.execute_status = parseInt(this.info.executeData.asset_status_num) === 7;
                        // this.submitData.reason = this.info.executeData.reason;
                    }
                  if (this.info.cycleInfo) {
                    if(this.info.cycleInfo.is_overdue){
                      this.show_complete_field = true;
                      this.hint = this.info.cycleInfo.tips;
                      this.submitData.complete_time = this.info.cycleInfo.now_date;
                      this.minDate = new Date(this.info.cycleInfo.min_date);
                      this.maxDate = new Date(this.info.cycleInfo.max_date);
                    }
                  }
                    this.page.is_display = true;
                })
            },
            onConfirm(value) {
                this.form[this.selectIndex[0]]['detail'][this.selectIndex[1]]['result'] = value;
                this.showPicker = false;
            },
            onCompleteTimeConfirm(datetime){
              let complete_time = this.timeFormat(datetime)
              this.submitData.complete_time = complete_time;
              this.showTimePicker = false;
            },
            timeFormat(time) { // 时间格式化
              let year = time.getFullYear();
              let month = time.getMonth() + 1;
              month = month < 10 ? '0'+month : month;
              let day = time.getDate();
              day = day < 10 ? '0'+day : day;
              let hour = time.getHours();
              hour = hour < 10 ? '0'+hour : hour;
              let minute = time.getMinutes();
              minute = minute < 10 ? '0'+minute : minute;
              return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':00';
            },
            //点击是否保养
            checkBoxChange() {
                status = this.submitData.execute_status;
                if (status === 'true') {
                    this.page.actions[1]['disabled'] = true;
                    this.page.actions[2]['disabled'] = true;
                    this.page.actions[3]['disabled'] = false;
                    this.page.assets_disabled = true;
                } else {
                    this.page.assets_disabled = false;
                    this.page.actions[1]['disabled'] = !(this.submitData.asset_status === '1' || this.submitData.asset_status === '2' || this.submitData.asset_status === '5' || this.submitData.asset_status === '6');
                    this.page.actions[2]['disabled'] = false;
                    this.page.actions[3]['disabled'] = true;
                }
            },
            actionSelect(name) {
                //最终保存方式
                // this.page.showAction = false;
                //获取数据
                let params = this.getParams();
                if (!params) return false;
                switch (name) {
                    case '转至报修':
                        this.page.addRepair = true;
                        break;
                    case '完成并保存该设备保养':
                        this.sendPost(params);
                        break;
                    case '暂存该设备保养':
                        params.executeStatus = "1";
                        this.sendPost(params);
                        break;
                    case '不进行保养并结束':
                        if (!this.submitData.reason.trim()) {
                            Notify({ type: 'danger', message: '请填写不进行保养的原因' });
                            return false;
                        }
                        params.reason = this.submitData.reason;
                        params.asset_status = "7";
                        this.sendPost(params);
                        break;
                }
            },
            assetStatusChange(name) {
                switch (parseInt(name)) {
                    case 1://工作正常
                    case 2://有小问题 正常
                    case 5://正在维修
                    case 6://已报废
                        this.page.actions[0]['disabled'] = true;
                        this.page.actions[1]['disabled'] = false;
                        this.page.actions[2]['disabled'] = false;
                        break;
                    case 3:
                    case 4:
                        this.page.actions[0]['disabled'] = false;
                        this.page.actions[1]['disabled'] = true;
                        this.page.actions[2]['disabled'] = true;
                        break;
                }
            },
            onConfirmAddRepair(obj) {
                //转至报修 确认按钮
                let params = this.getParams();
                params.userid = obj.value;
                params.applicant = obj.text;
                this.sendPost(params);
                this.page.addRepair = false;
            },
            //获取保存的数据
            getParams() {
                let ppid = [], abnormal_remark = [], result = [], flag = true;
                this.form.forEach(v => {
                    v.detail.forEach(v1 => {
                        if (v1.result !== '合格' && v1.abnormal_remark.trim() === '') {
                            flag = false;
                        }
                        ppid.push(v1.ppid);
                        abnormal_remark.push(!v1.abnormal_remark ? '#' : v1.abnormal_remark);
                        result.push(v1.result)
                    })
                });
                if (flag) {
                    return {
                        asset_status: this.submitData.asset_status,
                        assetnum: this.info.asArr.assnum,
                        cycid: this.info.cycid,
                        executeStatus: "2",
                        action: "setSituation",
                        remark: this.submitData.remark,
                        complete_time: this.submitData.complete_time,
                        ppid: ppid.toString(),
                        abnormal_remark: abnormal_remark.toString(),
                        result: result.toString(),
                    }
                } else {
                    Notify({ type: 'danger', message: '请补充异常详情信息' });
                    return false;
                }

            },
            sendPost(data) {
                let _this = this;
                setSituation(Qs.stringify(data)).then(res => {
                    if (res.status === 1) {
                        Toast({
                            type: 'success',//失败fail
                            duration: 2000,//2秒
                            message: res.msg,
                            icon: 'success',//失败cross
                            forbidClick: true,//是否禁止背景点击，避免重复提交
                            onClose() {
                                //关闭后跳转到首页
                                _this.$router.go(-1);
                            }
                        });
                    }
                }).catch(err => {
                    Toast.fail(err)
                });
            }
        },
        watch: {
            form: {
                deep: true,
                handler: function (v) {
                    let that = this;
                    that.page.abnormal_count = 0;
                    v.forEach(value => {
                        value.detail.forEach(value1 => {
                            if (value1.result !== '合格') {
                                that.page.abnormal_count++;
                            }
                        })
                    });
                }
            }
        },
        mounted() {
            this.getInfo();
        }
    }
</script>

<style scoped lang="scss">
    .do-task {
        background-color: #f5f5fa;
        height: 100vh;
    }

    .form-input {
        margin-top: 0.5rem;

        .van-cell {
            padding: 0;
        }
    }

    .customRadio {
        ::v-deep .van-radio {
            margin-bottom: 0.8rem;
        }
    }

</style>
<style>
    .van-field__label {
        width: 70px;
    }
</style>
