<template>
    <div>
        <van-divider>报废明细</van-divider>
        <van-tabs :border="false">
            <van-tab title="报废申请信息">
                <van-cell-group>
                    <van-cell title="报废单号" :value="scriptdata.scrapnum"/>
                    <van-cell title="报废日期" :value="scriptdata.scrapdate"/>
                    <van-cell title="申请人" :value="scriptdata.apply_user"/>
                    <van-cell title="申请时间" :value="scriptdata.add_time"/>
                    <van-cell title="报废原因" :value="scriptdata.scrap_reason"/>
                </van-cell-group>
            </van-tab>
            <van-tab title="设备基本信息">
                <AssetsInfo :info='info'/>
            </van-tab>
            <van-tab title="报废审批信息">
                <Approves :info='approves'/>
            </van-tab>
        </van-tabs>
    </div>
</template>

<script>
    import {Divider, Cell, CellGroup, Icon, Tab, Tabs} from 'vant';
    import {getInfo} from "@/api/assets/scrap/show";
    import AssetsInfo from "@/components/Assetsinfo";
    import Approves from '@/components/Approves';

    export default {
        name: 'applyScrap',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            AssetsInfo,
            Approves,
        },
        data() {
            return {
                scrap_reason: '',
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: []
                },
                approves: [],
                scriptdata: [],
            }
        },
        methods: {
            getInfo(scrid) {
                let params = {scrid: scrid, action: 'showScrap'};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.approves = response.approves;
                    this.scriptdata = response;
                })
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.scrid);
        }
    }
</script>
