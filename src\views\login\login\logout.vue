<template>
    <div>
        <van-empty :description="description">
            <!-- <van-button round type="info" class="bottom-button" @click="login">
                重新授权/切换账号登录
            </van-button>
          <br/>
          <br/> -->
            <van-button round type="primary" class="bottom-button" @click="new_login">
              登录其他账号
            </van-button>
        </van-empty>
    </div>
</template>
<script>

    import {logout} from "@/api/login/login";
    import {Empty,Button } from 'vant';
    export default {
        name: 'logout',
        components:{
            [Empty.name]: Empty,
            [Button.name]: Button,
        },
        data() {
            return {
                description:'',
                dynamicComponent: null,
            }
        },
        mounted: function () {
            localStorage.removeItem("Auth")
            localStorage.removeItem("expire_time")
            this.$bus.$emit('changeLoginStatus',false)
            // localStorage.removeItem("pw")
            // localStorage.removeItem("Auth")
            logout().then(res => {
                this.description = res.msg;
            })
        },
        methods: {
            // login(){
            //     this.$router.replace({name:'login'})
            // },
            new_login(){
              this.$router.replace({name:'login'})
            }
        }
    }
</script>
