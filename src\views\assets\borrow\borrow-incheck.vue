<template>
    <div class="con_detail">
        <van-tabs :border="false">
            <van-tab title="设备基本信息">
                <AssetsInfo :info='info'/>
            </van-tab>
            <van-tab title="借调申请信息">
                <van-cell-group>
                    <van-cell title="流水号" :value="info.borrowdata.borrow_num"/>
                    <van-cell title="申请科室" :value="info.borrowdata.department"/>
                    <van-cell title="申请人" :value="info.borrowdata.apply_username"/>
                    <van-cell title="申请时间" :value="info.borrowdata.apply_time"/>
                    <van-cell title="借调原因" :value="info.borrowdata.borrow_reason"/>
                    <van-cell title="预计归还时间" :value="info.borrowdata.estimate_back"/>
                    <van-cell title="借出时间" :value="info.borrowdata.borrow_in_time"/>
                </van-cell-group>
            </van-tab>
            <van-tab title="借调审批信息">
                <Approves :info='info.approves'/>
            </van-tab>
        </van-tabs>
        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">借调确认表单</h2>
        </div>
        <van-form ref="borrowForm" @submit="onSubmit" v-if="page.is_display">
            <van-cell-group title="一同借调附属设备"  v-if="page.subsidiary.length>0">
              <van-cell v-for="item in page.subsidiary" :key="item.assid" :title="item.assets" :value="item.model" />
          </van-cell-group>
            <van-field name="borrow_in_time" label="确定借入时间" required :value="form.borrow_in_time" placeholder="点击选择时间"
                       @click="page.showSelectTransfer = true"
                       :rules="[{ validator, message: '请选择时间',trigger:'onChange' }]"/>
            <van-popup v-model="page.showSelectTransfer" position="bottom">
                <van-datetime-picker
                        v-model="page.currentDate"
                        type="datetime"
                        title="选择时间"
                        @confirm="onConfirmTransferDate"
                        @cancel="page.showSelectTransfer = false"
                />
            </van-popup>
            <van-field
                    v-model="form.end_reason"
                    name="end_reason"
                    rows="1"
                    autosize
                    label="补充备注"
                    type="textarea"
                    placeholder="补充备注"
            />
            <!-- 不借入 -->
            <van-popup v-model="page.shownoform" position="bottom">
                <van-field v-model="form.end_reason" type="textarea" label="不借入原因" placeholder="请输入不借入原因" autosize rows="5" :rules="[{ validator:end_reason, message: '请输入不借入原因',trigger:'onChange' }]"/>
                <van-button block type="primary" native-type="Button" @click="changeFormType('no')" >
                    确认
                </van-button>
            </van-popup>
            <div style="padding: 16px;background: #fff;">
                <van-button block type="primary" native-type="Button" @click="changeFormType('yes')" style="margin-bottom: 16px;">
                    确认设备完好无损并借入使用
                </van-button>
                <van-button block type="warning" native-type="Button" @click="page.shownoform=true">
                    不借入并结束流程
                </van-button>
            </div>
        </van-form>
    </div>
</template>

<script>
    import Qs from 'qs';
    import {Divider, Cell, CellGroup, Icon, Field, Toast, Form, Button, Tab, Tabs, Popup, Picker, DatetimePicker} from 'vant';
    import {getInfo, submit} from "@/api/assets/borrow/showInCheck";
    import AssetsInfo from "@/components/Assetsinfo";
    import Approves from '@/components/Approves';

    export default {
        name: 'applyScrap',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [DatetimePicker.name]: DatetimePicker,
            AssetsInfo,
            Approves,
        },
        data() {
            return {
                scrap_reason: '',
                info: {
                    factoryInfo: [],
                    supplierInfo: [],
                    repairInfo: [],
                    approves: [],
                    borrowdata: [],
                },
                page: {
                    currentDate: new Date(),//当前日期
                    showSelectTransfer: false,//时间控件显示
                    is_display:true,
                    subsidiary:[],
                    shownoform:false,
                },
                form: {
                    borrow_in_time: '',
                    end_reason: '',
                    formType: 'yes',
                }
            }
        },
        methods: {
            end_reason(val){
                if (!this.page.shownoform) {
                    return true;
                }else if (val) {
                    return true;
                }
                return false;
            },
            validator(val){
                if (this.page.shownoform) {
                    return true;
                }else if (val) {
                    return true;
                }
                return false;
            },
            onConfirmTransferDate(time) {
                this.form.borrow_in_time = this.timeFormat(time);
                this.page.showSelectTransfer = false;
            },
            onSubmit(values) {
                if (this.form.formType === 'yes') {
                    values.status = 2;
                } else {
                    values.status = 4;
                }
                values.borid = this.$route.query.borid;
                let _this = this;
                submit(Qs.stringify(values)).then(response => {
                    if (response.status==1) {
                    Toast({
                            type:'success',//失败fail
                            duration:2000,//2秒
                            message: response.msg,
                            icon:'success',//失败cross
                            forbidClick:true,//是否禁止背景点击，避免重复提交
                            onClose(){
                                //关闭后跳转到首页
                                _this.$router.push(this.$store.getters.moduleName+'/Borrow/borrowInCheckList');
                            }
                        });
                }
                })
            },
            changeFormType(type) {
                this.form.formType = type;//改变提交表单的formType
                this.$refs.borrowForm.submit();//执行提交
            },
            timeFormat(time) { // 时间格式化
                let year = time.getFullYear();
                let month = time.getMonth() + 1;
                let day = time.getDate();
                let hour = time.getHours();
                let minute = time.getMinutes();
                return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
            },
            getInfo(borid) {
                let params = {borid: borid, action: 'showScrap'};
                getInfo(params).then(response => {
                    this.info = response.asArr;
                    this.info.approves = response.approve;
                    this.info.borrowdata = response.borrow;
                    this.page.is_display = response.is_display;
                    this.page.subsidiary = response.subsidiary;
                })
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.borid);
        }
    }
</script>
<style scoped lang="scss">
    .card-header{margin-top: 20px;}
</style>
