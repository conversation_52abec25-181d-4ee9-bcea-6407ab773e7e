# 天成医疗设备管理系统移动端开发文档

## 项目安装&更新命令
```
npm install
```

### 编译带热更新开发命令
```
npm run serve
```

### 构建线上生产版本
```
npm run build
```

### 生成接口文档
```
apidoc -i src/doc_file -o apidoc/
```

### 项目目录结构

```
│  .env.development //开发环境变量
│  .env.production //生产环境变量
│  .gitignore //git忽略文件
│  babel.config.js
│  package-lock.json
│  package.json //包文件
│  README.md //项目文件说明
│  vue.config.js //vue配置文件  
├─apidoc //接口文档
├─node_modules //依赖包
├─public //公共资源文件（打包时原封不动）
└─src
    │  App.vue //入口文件
    │  main.js //入口文件的js
    ├─api //接口目录     
    ├─assets //公共资源文件（打包时hash赋值）     
    ├─components //公共组件
    ├─doc_file // 生成接口文档的文件     
    ├─mock //mock.js 模拟数据
    ├─router //路由文件
    ├─store //全局状态管理
    ├─style //全局样式控制
    ├─utils //工具包 如封装ajax等
    └─views //视图文件
```

### 常见问题
1. Axios发送请求时params和data的区别以及post传数据到后台接口时需要注意的问题
    - params是添加到url的请求字符串中的，用于get请求
    - data是添加到请求体（body）中的， 用于post请求
    - post请求的时候如果需要传参数需要使用Qs.stringify()对数据进行转换，否则后台会接收不到，原因axios默认发送数据时，数据格式是Request Payload，而并非我们常用的Form Data格式，后端未必能正常获取到，所以在发送之前，需要使用qs模块对其进行处理。    





